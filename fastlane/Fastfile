# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

platform :android do
  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

    desc "Upload Prod General to Google Drive"
    lane :prodGeneral do
        gradle(task: "clean assembleProdGeneralRelease")
        upload_to_google_drive(
            drive_keyfile: 'fastlane/client_key.json',
            service_account: true,
            folder_id: '1WYD1pBkxYgaUz8hb3t6cwtSBG0QAaZ1c',
            upload_files: ['app\build\outputs\apk\prodGeneral\release\app-prodGeneral-release.apk'],
        )
     end

    desc "Upload Staging General to Google Drive"
     lane :stagingGeneral do
         gradle(task: "clean assembleStagingGeneralDebug")
         upload_to_google_drive(
             drive_keyfile: 'fastlane/client_key.json',
             service_account: true,
             folder_id: '1WYD1pBkxYgaUz8hb3t6cwtSBG0QAaZ1c',
             upload_files: ['app\build\outputs\apk\stagingGeneral\debug\app-stagingGeneral-debug.apk'],
         )
      end

    desc "Upload Prod Sumni D3 Mini Apk to Google Drive"
     lane :prodPos2 do
         gradle(task: "clean assembleProdSumniPos2Release")
         upload_to_google_drive(
             drive_keyfile: 'fastlane/client_key.json',
             service_account: true,
             folder_id: '1WYD1pBkxYgaUz8hb3t6cwtSBG0QAaZ1c',
             upload_files: ['app\build\outputs\apk\prodSumniPos2\release\app-prodSumniPos2-release.apk'],
         )
      end


     desc "Upload Prod Sumni TS2 Apk to Google Drive"
       lane :prodPos3 do
           gradle(task: "clean assembleProdSumniPos3Release")
           upload_to_google_drive(
               drive_keyfile: 'fastlane/client_key.json',
               service_account: true,
               folder_id: '1WYD1pBkxYgaUz8hb3t6cwtSBG0QAaZ1c',
               upload_files: ['app\build\outputs\apk\prodSumniPos3\release\app-prodSumniPos3-release.apk'],
           )
        end

     lane :uploadGoogleDriveAll do
         stagingGeneral()
         prodGeneral()
         prodPos2()
         prodPos3()
     end
     desc "Upload all staging APKs to Google Drive"
       lane :uploadDPOSStaging do
             upload_to_google_drive(
                                 drive_keyfile: 'fastlane/client_key.json',
                                 service_account: true,
                                 folder_id: '1BCIY53syov9g3EzBss3OAryDaFrVIsk0',
                                 upload_files: [
                                 'stagingGeneral\debug\app-stagingGeneral-debug.apk',
                                 'stagingGeneral\release\app-stagingGeneral-release.apk',
                                 'stagingSumniPos2\debug\app-stagingSumniPos2-debug.apk',
                                 'stagingSumniPos2\release\app-stagingSumniPos2-release.apk',
                                 ],
                             )
       end

    desc "Upload all prod APKs to Google Drive"
          lane :uploadDPOSProd do
                upload_to_google_drive(
                                    drive_keyfile: 'fastlane/client_key.json',
                                    service_account: true,
                                    folder_id: '1BCIY53syov9g3EzBss3OAryDaFrVIsk0',
                                    upload_files: [
                                    'prodGeneral\debug\app-prodGeneral-debug.apk',
                                    'prodGeneral\release\app-prodGeneral-release.apk',
                                    'prodSumniPos2\debug\app-prodSumniPos2-debug.apk',
                                    'prodSumniPos2\release\app-prodSumniPos2-release.apk'
                                    ],
                                )
          end


       desc "Upload all prod APKs to Google Drive"
                lane :uploadDPOSOnlyProdRelease do
                      upload_to_google_drive(
                                          drive_keyfile: 'fastlane/client_key.json',
                                          service_account: true,
                                          folder_id: '1BCIY53syov9g3EzBss3OAryDaFrVIsk0',
                                          upload_files: [
                                          'prodSumniPos2\release\app-prodSumniPos2-release.apk'
                                          ],
                                      )
                end

      desc "Upload all staging APKs to Google Drive"
             lane :uploadDPOSStagingOnly do
                   upload_to_google_drive(
                                       drive_keyfile: 'fastlane/client_key.json',
                                       service_account: true,
                                       folder_id: '1BCIY53syov9g3EzBss3OAryDaFrVIsk0',
                                       upload_files: [
                                       'stagingSumniPos2\release\app-stagingSumniPos2-release.apk',
                                       ],
                                   )
             end


    desc "Upload all staging and prod releaseAPKs to Google Drive"
    lane :uploadDPOSStagingProdRelease do
     upload_to_google_drive(
              drive_keyfile: 'fastlane/client_key.json',
              service_account: true,
              folder_id: '1BCIY53syov9g3EzBss3OAryDaFrVIsk0',
              upload_files: [
                       'stagingGeneral\release\app-stagingGeneral-release.apk',
                        'prodSumniPos2\release\app-prodSumniPos2-release.apk'
              ],
          )
    end

    desc "Upload all prod APKs to Google Drive"
                lane :uploadMPOSOnlyProdRelease do
                      upload_to_google_drive(
                                          drive_keyfile: 'fastlane/client_key.json',
                                          service_account: true,
                                          folder_id: '1KzMh8ssveG6Mb4EoKC5rwNSd9CyT_2SA',
                                          upload_files: [
                                          'prodGeneralMobilePOS\release\app-prodGeneralMobilePOS-release.apk',
                                          'prodSumniMobilePOS\release\app-prodSumniMobilePOS-release.apk'
                                          ],
                                      )
                end

  desc "Submit a new Beta Build to Crashlytics Beta"
   lane :prod do
     gradle(task: "clean assembleProdSumniRelease")
   end
end
