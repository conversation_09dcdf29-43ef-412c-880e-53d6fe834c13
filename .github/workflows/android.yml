name: Upload APK in Releases
on:
  push:
  # Sequence of patterns matched against refs/tags
    #tags:
    #  - 'v*' # Push events to matching v*, i.e. v1.0, v20.15.10
    branches: 
      - main
      - dev
      - feature/*

jobs:
  apk:
    name: Upload APK in Releases
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v1
      - name: set up JDK 1.8
        uses: actions/setup-java@v1
        with:
          java-version: 17
          distribution: "temurin"

      - name: Grant Permission to Execute
        run: chmod +x gradlew
        
      - name: Build Staging APK
        run: bash ./gradlew assembleStagingSumniRelease --stacktrace
      
      - name: Upload Staging APK to Github Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: app-staging
          path: app/build/outputs/apk/stagingSumni/release/app-stagingSumni-release.apk

      - name: Build Production APK
        run: bash ./gradlew assembleProdSumniRelease --stacktrace
      
      - name: Upload Prod APK to Github Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: app-prod
          path: app/build/outputs/apk/prodSumni/release/app-prodSumni-release.apk

      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ****************************************
        with:
          tag_name: ${{ github.run_id }}
          release_name: Release ${{ github.run_id }}
          draft: false
          prerelease: false

      - name: Upload Staging APK to Release
        id: upload-release-asset-staging 
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ****************************************
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }} # This pulls from the CREATE RELEASE step above, referencing it's ID to get its outputs object, which include a `upload_url`. See this blog post for more info: https://jasonet.co/posts/new-features-of-github-actions/#passing-data-to-future-steps 
          asset_path: ./app/build/outputs/apk/stagingSumni/release/app-stagingSumni-release.apk
          asset_name: app-staging-release.apk
          asset_content_type: application/zip

      - name: Upload Prod APK to Release
        id: upload-release-asset-prod 
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ****************************************
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }} # This pulls from the CREATE RELEASE step above, referencing it's ID to get its outputs object, which include a `upload_url`. See this blog post for more info: https://jasonet.co/posts/new-features-of-github-actions/#passing-data-to-future-steps 
          asset_path: ./app/build/outputs/apk/prodSumni/release/app-prodSumni-release.apk
          asset_name: app-prod-release.apk
          asset_content_type: application/zip

      - name: Distribute Staging to AppCenter
        uses: wzieba/AppCenter-Github-Action@v1.3.2
        with:
          appName: sheerazam/dasa-app
          token: 9a30e7b357e161e29b9bb6d59ca13b8d997d619f
          group: dasa-staging
          file: app/build/outputs/apk/stagingSumni/release/app-stagingSumni-release.apk
          debug: false

      - name: Distribute Prod to AppCenter
        uses: wzieba/AppCenter-Github-Action@v1.3.2
        with:
          appName: sheerazam/dasa-app
          token: 9a30e7b357e161e29b9bb6d59ca13b8d997d619f
          group: dasa-prod
          file: app/build/outputs/apk/prodSumni/release/app-prodSumni-release.apk
          debug: false
