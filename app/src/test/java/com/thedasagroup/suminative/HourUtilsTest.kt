package com.thedasagroup.suminative

import org.junit.Test
import org.junit.Assert.*
import org.junit.Before

/**
 * Unit tests for HourUtils class.
 * Tests the getPortion method which calculates time portions based on hour and minute values.
 */
class HourUtilsTest {

    private lateinit var hourUtils: HourUtils

    @Before
    fun setUp() {
        hourUtils = HourUtils()
    }

    @Test
    fun getPortion_withHour0AndMinute0_returns0() {
        val result = hourUtils.getPortion(0, 0)
        assertEquals(0, result)
    }

    @Test
    fun getPortion_withHour0AndMinute15_returns1() {
        val result = hourUtils.getPortion(0, 15)
        assertEquals(1, result)
    }

    @Test
    fun getPortion_withHour0AndMinute30_returns2() {
        val result = hourUtils.getPortion(0, 30)
        assertEquals(2, result)
    }

    @Test
    fun getPortion_withHour0AndMinute45_returns3() {
        val result = hourUtils.getPortion(0, 45)
        assertEquals(3, result)
    }

    @Test
    fun getPortion_withHour1AndMinute0_returns0() {
        val result = hourUtils.getPortion(1, 0)
        assertEquals(0, result)
    }

    @Test
    fun getPortion_withHour1AndMinute15_returns1() {
        val result = hourUtils.getPortion(1, 15)
        assertEquals(1, result)
    }

    @Test
    fun getPortion_withHour1AndMinute30_returns2() {
        val result = hourUtils.getPortion(1, 30)
        assertEquals(2, result)
    }

    @Test
    fun getPortion_withHour1AndMinute45_returns3() {
        val result = hourUtils.getPortion(1, 45)
        assertEquals(3, result)
    }

    @Test
    fun getPortion_withDifferentHours_followsPattern() {
        // Test pattern for different hours with minute 0
        assertEquals(0, hourUtils.getPortion(0, 0))  // (0*4 + 0) % 4 = 0
        assertEquals(0, hourUtils.getPortion(1, 0))  // (1*4 + 0) % 4 = 0
        assertEquals(0, hourUtils.getPortion(2, 0))  // (2*4 + 0) % 4 = 0
        assertEquals(0, hourUtils.getPortion(3, 0))  // (3*4 + 0) % 4 = 0
        assertEquals(0, hourUtils.getPortion(4, 0))  // (4*4 + 0) % 4 = 0
    }

    @Test
    fun getPortion_minuteRanges_correctQuarterHourMapping() {
        val hour = 5
        
        // First quarter (0-14 minutes): minute/15 = 0
        assertEquals(0, hourUtils.getPortion(hour, 0))
        assertEquals(0, hourUtils.getPortion(hour, 7))
        assertEquals(0, hourUtils.getPortion(hour, 14))
        
        // Second quarter (15-29 minutes): minute/15 = 1
        assertEquals(1, hourUtils.getPortion(hour, 15))
        assertEquals(1, hourUtils.getPortion(hour, 22))
        assertEquals(1, hourUtils.getPortion(hour, 29))
        
        // Third quarter (30-44 minutes): minute/15 = 2
        assertEquals(2, hourUtils.getPortion(hour, 30))
        assertEquals(2, hourUtils.getPortion(hour, 37))
        assertEquals(2, hourUtils.getPortion(hour, 44))
        
        // Fourth quarter (45-59 minutes): minute/15 = 3
        assertEquals(3, hourUtils.getPortion(hour, 45))
        assertEquals(3, hourUtils.getPortion(hour, 52))
        assertEquals(3, hourUtils.getPortion(hour, 59))
    }

    @Test
    fun getPortion_edgeCases_validResults() {
        // Test edge cases with boundary values
        assertEquals(0, hourUtils.getPortion(0, 0))   // Start of day
        assertEquals(3, hourUtils.getPortion(23, 59)) // End of day
        assertEquals(0, hourUtils.getPortion(12, 0))  // Noon
        assertEquals(2, hourUtils.getPortion(6, 30))  // Half past 6
    }

    @Test
    fun getPortion_largeHourValues_correctModuloResult() {
        // Test with larger hour values to verify modulo behavior
        assertEquals(0, hourUtils.getPortion(100, 0))  // (100*4 + 0) % 4 = 0
        assertEquals(0, hourUtils.getPortion(101, 0))  // (101*4 + 0) % 4 = 0
        assertEquals(0, hourUtils.getPortion(102, 0))  // (102*4 + 0) % 4 = 0
        assertEquals(0, hourUtils.getPortion(103, 0))  // (103*4 + 0) % 4 = 0
    }

    @Test
    fun getPortion_combinedValues_correctCalculation() {
        // Test various combinations to verify the formula: (hour * 4 + (minute / 15)) % 4
        assertEquals(1, hourUtils.getPortion(2, 15))   // (2*4 + 1) % 4 = 1
        assertEquals(2, hourUtils.getPortion(3, 30))   // (3*4 + 2) % 4 = 2
        assertEquals(3, hourUtils.getPortion(4, 45))   // (4*4 + 3) % 4 = 3
        assertEquals(0, hourUtils.getPortion(5, 0))    // (5*4 + 0) % 4 = 0
    }

    @Test
    fun getPortion_allPossibleResults_covering0To3() {
        // Ensure all possible return values (0, 1, 2, 3) can be achieved
        val results = mutableSetOf<Int>()
        
        for (hour in 0..3) {
            for (minuteQuarter in 0..3) {
                val minute = minuteQuarter * 15
                results.add(hourUtils.getPortion(hour, minute))
            }
        }
        
        // Verify all possible results (0, 1, 2, 3) are covered
        assertEquals(setOf(0, 1, 2, 3), results)
    }
} 