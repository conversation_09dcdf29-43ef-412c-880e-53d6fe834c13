package com.thedasagroup.suminative.ui.products

import com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer
import com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper
import org.junit.Test
import org.junit.Assert.*

/**
 * Test class for customer linking functionality in ProductsScreenViewModel
 */
class CustomerLinkingTest {

    @Test
    fun `test linkRewardsCustomer with no table selected links to global customer`() {
        // Given
        val initialState = ProductsScreenState()
        val rewardsCustomer = RewardsCustomer(
            id = 123,
            name = "<PERSON>",
            email = "<EMAIL>",
            phone = "1234567890",
            businessId = 1
        )

        // When
        val updatedState = initialState.copy(selectedCustomer = rewardsCustomer)

        // Then
        assertEquals(rewardsCustomer, updatedState.selectedCustomer)
        assertTrue(updatedState.tableCustomers.isEmpty())
    }

    @Test
    fun `test linkRewardsCustomer with table selected links to table customer`() {
        // Given
        val tableSelection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )
        val initialState = ProductsScreenState(
            selectedTables = listOf(tableSelection),
            selectedTableIndex = 0
        )
        val rewardsCustomer = RewardsCustomer(
            id = 123,
            name = "John Doe",
            email = "<EMAIL>",
            phone = "1234567890",
            businessId = 1
        )

        // When
        val updatedState = initialState.copy(
            tableCustomers = mapOf(1 to rewardsCustomer)
        )

        // Then
        assertEquals(rewardsCustomer, updatedState.tableCustomers[1])
        assertNull(updatedState.selectedCustomer)
    }

    @Test
    fun `test getCurrentCustomer returns table customer when table is selected`() {
        // Given
        val tableSelection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )
        val rewardsCustomer = RewardsCustomer(
            id = 123,
            name = "John Doe",
            email = "<EMAIL>",
            phone = "1234567890",
            businessId = 1
        )
        val state = ProductsScreenState(
            selectedTables = listOf(tableSelection),
            selectedTableIndex = 0,
            tableCustomers = mapOf(1 to rewardsCustomer)
        )

        // When
        val currentCustomer = state.getCurrentCustomer()

        // Then
        assertEquals(rewardsCustomer, currentCustomer)
    }

    @Test
    fun `test getCurrentCustomer returns global customer when no table is selected`() {
        // Given
        val rewardsCustomer = RewardsCustomer(
            id = 123,
            name = "John Doe",
            email = "<EMAIL>",
            phone = "1234567890",
            businessId = 1
        )
        val state = ProductsScreenState(
            selectedCustomer = rewardsCustomer
        )

        // When
        val currentCustomer = state.getCurrentCustomer()

        // Then
        assertEquals(rewardsCustomer, currentCustomer)
    }

    @Test
    fun `test getCurrentCustomer returns null when no customer is linked`() {
        // Given
        val state = ProductsScreenState()

        // When
        val currentCustomer = state.getCurrentCustomer()

        // Then
        assertNull(currentCustomer)
    }

    @Test
    fun `test table customer is cleared when table is removed`() {
        // Given
        val tableSelection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )
        val rewardsCustomer = RewardsCustomer(
            id = 123,
            name = "John Doe",
            email = "<EMAIL>",
            phone = "1234567890",
            businessId = 1
        )
        val initialState = ProductsScreenState(
            selectedTables = listOf(tableSelection),
            selectedTableIndex = 0,
            tableCustomers = mapOf(1 to rewardsCustomer)
        )

        // When - simulate table removal
        val updatedState = initialState.copy(
            selectedTables = emptyList(),
            selectedTableIndex = 0,
            tableCustomers = emptyMap()
        )

        // Then
        assertTrue(updatedState.tableCustomers.isEmpty())
        assertTrue(updatedState.selectedTables.isEmpty())
    }

    @Test
    fun `test multiple tables can have different customers`() {
        // Given
        val table1Selection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )
        val table2Selection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 2,
            areaId = 1,
            tableName = "Table 2",
            areaName = "Main Area"
        )
        val customer1 = RewardsCustomer(
            id = 123,
            name = "John Doe",
            email = "<EMAIL>",
            phone = "1234567890",
            businessId = 1
        )
        val customer2 = RewardsCustomer(
            id = 456,
            name = "Jane Smith",
            email = "<EMAIL>",
            phone = "0987654321",
            businessId = 1
        )

        // When
        val state = ProductsScreenState(
            selectedTables = listOf(table1Selection, table2Selection),
            selectedTableIndex = 0,
            tableCustomers = mapOf(
                1 to customer1,
                2 to customer2
            )
        )

        // Then
        assertEquals(customer1, state.tableCustomers[1])
        assertEquals(customer2, state.tableCustomers[2])
        assertEquals(customer1, state.getCurrentCustomer()) // Table 1 is selected
    }

    @Test
    fun `test customer switches when table index changes`() {
        // Given
        val table1Selection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )
        val table2Selection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 2,
            areaId = 1,
            tableName = "Table 2",
            areaName = "Main Area"
        )
        val customer1 = RewardsCustomer(
            id = 123,
            name = "John Doe",
            email = "<EMAIL>",
            phone = "1234567890",
            businessId = 1
        )
        val customer2 = RewardsCustomer(
            id = 456,
            name = "Jane Smith",
            email = "<EMAIL>",
            phone = "0987654321",
            businessId = 1
        )

        val initialState = ProductsScreenState(
            selectedTables = listOf(table1Selection, table2Selection),
            selectedTableIndex = 0, // Table 1 selected
            tableCustomers = mapOf(
                1 to customer1,
                2 to customer2
            )
        )

        // When - switch to table 2
        val updatedState = initialState.copy(selectedTableIndex = 1)

        // Then
        assertEquals(customer1, updatedState.getCurrentCustomer()) // Should still be customer1 since we're testing state logic

        // Test the actual switching logic
        val stateWithTable2Selected = initialState.copy(selectedTableIndex = 1)
        assertEquals(customer2, stateWithTable2Selected.getCurrentCustomer()) // Now should be customer2
    }

    @Test
    fun `test customer popup visibility logic`() {
        // Given
        val tableSelection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )
        val customer = RewardsCustomer(
            id = 123,
            name = "John Doe",
            email = "<EMAIL>",
            phone = "1234567890",
            businessId = 1
        )

        // Test 1: No table, no customer - should hide popup
        val stateNoCustomer = ProductsScreenState()
        assertNull(stateNoCustomer.getCurrentCustomer()) // Popup should be hidden

        // Test 2: No table, global customer - should show popup
        val stateGlobalCustomer = ProductsScreenState(selectedCustomer = customer)
        assertEquals(customer, stateGlobalCustomer.getCurrentCustomer()) // Popup should be shown

        // Test 3: Table selected, no customer for table - should hide popup
        val stateTableNoCustomer = ProductsScreenState(
            selectedTables = listOf(tableSelection),
            selectedTableIndex = 0
        )
        assertNull(stateTableNoCustomer.getCurrentCustomer()) // Popup should be hidden

        // Test 4: Table selected, customer for table - should show popup
        val stateTableWithCustomer = ProductsScreenState(
            selectedTables = listOf(tableSelection),
            selectedTableIndex = 0,
            tableCustomers = mapOf(1 to customer)
        )
        assertEquals(customer, stateTableWithCustomer.getCurrentCustomer()) // Popup should be shown
    }

    @Test
    fun `test table with customer takes precedence over global customer`() {
        // Given
        val tableSelection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )
        val globalCustomer = RewardsCustomer(
            id = 123,
            name = "Global Customer",
            email = "<EMAIL>",
            phone = "1234567890",
            businessId = 1
        )
        val tableCustomer = RewardsCustomer(
            id = 456,
            name = "Table Customer",
            email = "<EMAIL>",
            phone = "0987654321",
            businessId = 1
        )

        // When - both global and table customer exist
        val state = ProductsScreenState(
            selectedTables = listOf(tableSelection),
            selectedTableIndex = 0,
            selectedCustomer = globalCustomer,
            tableCustomers = mapOf(1 to tableCustomer)
        )

        // Then - table customer should take precedence
        assertEquals(tableCustomer, state.getCurrentCustomer())
        assertNotEquals(globalCustomer, state.getCurrentCustomer())
    }
}
