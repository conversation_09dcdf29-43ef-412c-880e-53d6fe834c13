package com.thedasagroup.suminative.ui.products.cart

import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.StoreItem
import org.junit.Test
import org.junit.Assert.*

class BillTabTest {

    @Test
    fun `test CartTab enum includes BILL tab`() {
        val tabs = CartTab.values()
        
        assertEquals(3, tabs.size)
        assertEquals("Order", CartTab.ORDER.displayName)
        assertEquals("Bill", CartTab.BILL.displayName)
        assertEquals("Pay", CartTab.PAY.displayName)
    }

    @Test
    fun `test bill tab order is correct`() {
        val tabs = CartTab.values()
        
        assertEquals(CartTab.ORDER, tabs[0])
        assertEquals(CartTab.BILL, tabs[1])
        assertEquals(CartTab.PAY, tabs[2])
    }

    @Test
    fun `test order summary calculation for bill tab`() {
        val cart1 = Cart(
            storeItem = StoreItem(
                id = 1,
                name = "Pizza",
                price = 15.99
            ),
            quantity = 2,
            price = 15.99,
            netPayable = 31.98
        )

        val cart2 = Cart(
            storeItem = StoreItem(
                id = 2,
                name = "Burger",
                price = 12.50
            ),
            quantity = 1,
            price = 12.50,
            netPayable = 12.50
        )

        val order = Order(
            carts = mutableListOf(cart1, cart2),
            netPayable = 44.48, // 31.98 + 12.50
            tax = 8.90, // 20% tax
            totalPrice = 53.38 // 44.48 + 8.90
        )

        // Verify order calculations
        assertEquals(2, order.carts?.size)
        assertEquals(44.48, order.netPayable, 0.01)
        assertEquals(8.90, order.tax, 0.01)
        assertEquals(53.38, order.totalPrice, 0.01)
    }

    @Test
    fun `test bill tab shows correct item count`() {
        val cart1 = Cart(
            storeItem = StoreItem(id = 1, name = "Item 1"),
            quantity = 1,
            netPayable = 10.00
        )

        val cart2 = Cart(
            storeItem = StoreItem(id = 2, name = "Item 2"),
            quantity = 3,
            netPayable = 30.00
        )

        val order = Order(
            carts = mutableListOf(cart1, cart2),
            netPayable = 40.00,
            tax = 8.00,
            totalPrice = 48.00
        )

        // Bill tab should show total number of cart items (not quantity)
        val itemCount = order.carts?.size ?: 0
        assertEquals(2, itemCount) // 2 different items in cart
        
        // But individual items should show their quantities
        assertEquals(1, cart1.quantity)
        assertEquals(3, cart2.quantity)
    }

    @Test
    fun `test bill tab handles empty order`() {
        val emptyOrder = Order(
            carts = mutableListOf(),
            netPayable = 0.0,
            tax = 0.0,
            totalPrice = 0.0
        )

        assertEquals(0, emptyOrder.carts?.size)
        assertEquals(0.0, emptyOrder.netPayable, 0.01)
        assertEquals(0.0, emptyOrder.tax, 0.01)
        assertEquals(0.0, emptyOrder.totalPrice, 0.01)
    }

    @Test
    fun `test bill tab handles null values gracefully`() {
        val cartWithNulls = Cart(
            storeItem = null,
            quantity = null,
            price = null,
            netPayable = null,
            notes = null
        )

        val orderWithNulls = Order(
            carts = mutableListOf(cartWithNulls),
            netPayable = null,
            tax = null,
            totalPrice = null
        )

        // Should handle null values without crashing
        assertEquals(1, orderWithNulls.carts?.size)
        assertNull(orderWithNulls.netPayable)
        assertNull(orderWithNulls.tax)
        assertNull(orderWithNulls.totalPrice)
        
        // Cart item should handle nulls
        assertNull(cartWithNulls.storeItem)
        assertNull(cartWithNulls.quantity)
        assertNull(cartWithNulls.netPayable)
        assertNull(cartWithNulls.notes)
    }

    @Test
    fun `test bill tab shows item details correctly`() {
        val cartWithDetails = Cart(
            storeItem = StoreItem(
                id = 1,
                name = "Margherita Pizza"
            ),
            quantity = 2,
            price = 15.99,
            netPayable = 31.98,
            notes = "Extra cheese, no olives"
        )

        val order = Order(
            carts = mutableListOf(cartWithDetails),
            netPayable = 31.98,
            tax = 6.40,
            totalPrice = 38.38
        )

        // Verify item details are preserved for bill display
        val item = order.carts?.first()
        assertEquals("Margherita Pizza", item?.storeItem?.name)
        assertEquals(2, item?.quantity)
        assertEquals(31.98, item?.netPayable, 0.01)
        assertEquals("Extra cheese, no olives", item?.notes)
    }

    @Test
    fun `test bill tab calculation with multiple items and tax`() {
        val items = listOf(
            Cart(storeItem = StoreItem(name = "Starter"), netPayable = 8.50),
            Cart(storeItem = StoreItem(name = "Main"), netPayable = 18.99),
            Cart(storeItem = StoreItem(name = "Dessert"), netPayable = 6.50),
            Cart(storeItem = StoreItem(name = "Drink"), netPayable = 3.50)
        )

        val subtotal = items.sumOf { it.netPayable ?: 0.0 } // 37.49
        val tax = subtotal * 0.20 // 20% tax = 7.50
        val total = subtotal + tax // 44.99

        val order = Order(
            carts = items.toMutableList(),
            netPayable = subtotal,
            tax = tax,
            totalPrice = total
        )

        assertEquals(4, order.carts?.size)
        assertEquals(37.49, order.netPayable, 0.01)
        assertEquals(7.50, order.tax, 0.01)
        assertEquals(44.99, order.totalPrice, 0.01)
    }
}
