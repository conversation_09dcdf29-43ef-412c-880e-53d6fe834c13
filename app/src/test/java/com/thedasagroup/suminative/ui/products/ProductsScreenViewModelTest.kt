package com.thedasagroup.suminative.ui.products

import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.response.stock.StockItem
import com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for table-specific cart functionality in ProductsScreenViewModel
 */
class ProductsScreenViewModelTest {

    @Test
    fun `test getCurrentTableOrder returns global order when no tables selected`() {
        val state = ProductsScreenState(
            order = Order(totalPrice = 100.0),
            selectedTables = emptyList(),
            selectedTableIndex = 0
        )
        
        val currentOrder = state.getCurrentTableOrder()
        assertEquals(100.0, currentOrder.totalPrice, 0.0)
    }

    @Test
    fun `test getCurrentTableOrder returns table-specific order when table selected`() {
        val table1 = AreaTableSelectionHelper.AreaTableSelection(
            areaId = 1,
            areaName = "Main Area",
            tableId = 101,
            tableName = "Table 1",
            tableCapacity = 4
        )
        
        val table1Order = Order(totalPrice = 50.0)
        val globalOrder = Order(totalPrice = 100.0)
        
        val state = ProductsScreenState(
            order = globalOrder,
            selectedTables = listOf(table1),
            selectedTableIndex = 0,
            tableOrders = mapOf(101 to table1Order)
        )
        
        val currentOrder = state.getCurrentTableOrder()
        assertEquals(50.0, currentOrder.totalPrice, 0.0)
    }

    @Test
    fun `test getCurrentTableOrder returns empty order when table not found in tableOrders`() {
        val table1 = AreaTableSelectionHelper.AreaTableSelection(
            areaId = 1,
            areaName = "Main Area",
            tableId = 101,
            tableName = "Table 1",
            tableCapacity = 4
        )
        
        val globalOrder = Order(totalPrice = 100.0)
        
        val state = ProductsScreenState(
            order = globalOrder,
            selectedTables = listOf(table1),
            selectedTableIndex = 0,
            tableOrders = emptyMap() // No table orders
        )
        
        val currentOrder = state.getCurrentTableOrder()
        assertEquals(0.0, currentOrder.totalPrice ?: 0.0, 0.0)
    }

    @Test
    fun `test getCurrentTableId returns null when no tables selected`() {
        val state = ProductsScreenState(
            selectedTables = emptyList(),
            selectedTableIndex = 0
        )
        
        val currentTableId = state.getCurrentTableId()
        assertNull(currentTableId)
    }

    @Test
    fun `test getCurrentTableId returns correct table ID when table selected`() {
        val table1 = AreaTableSelectionHelper.AreaTableSelection(
            areaId = 1,
            areaName = "Main Area",
            tableId = 101,
            tableName = "Table 1",
            tableCapacity = 4
        )
        
        val state = ProductsScreenState(
            selectedTables = listOf(table1),
            selectedTableIndex = 0
        )
        
        val currentTableId = state.getCurrentTableId()
        assertEquals(101, currentTableId)
    }

    @Test
    fun `test getCurrentTableId returns correct table ID for different selected index`() {
        val table1 = AreaTableSelectionHelper.AreaTableSelection(
            areaId = 1,
            areaName = "Main Area",
            tableId = 101,
            tableName = "Table 1",
            tableCapacity = 4
        )
        
        val table2 = AreaTableSelectionHelper.AreaTableSelection(
            areaId = 1,
            areaName = "Main Area",
            tableId = 102,
            tableName = "Table 2",
            tableCapacity = 6
        )
        
        val state = ProductsScreenState(
            selectedTables = listOf(table1, table2),
            selectedTableIndex = 1 // Select second table
        )
        
        val currentTableId = state.getCurrentTableId()
        assertEquals(102, currentTableId)
    }

    @Test
    fun `test getCurrentTableId returns null when selectedTableIndex is out of bounds`() {
        val table1 = AreaTableSelectionHelper.AreaTableSelection(
            areaId = 1,
            areaName = "Main Area",
            tableId = 101,
            tableName = "Table 1",
            tableCapacity = 4
        )
        
        val state = ProductsScreenState(
            selectedTables = listOf(table1),
            selectedTableIndex = 5 // Out of bounds
        )
        
        val currentTableId = state.getCurrentTableId()
        assertNull(currentTableId)
    }

    @Test
    fun `test service charge auto-application for new table`() {
        val table1 = AreaTableSelectionHelper.AreaTableSelection(
            areaId = 1,
            areaName = "Main Area",
            tableId = 101,
            tableName = "Table 1",
            tableCapacity = 4
        )

        // Test case 1: Service charge should be auto-applied for new table
        val state1 = ProductsScreenState(
            selectedTables = listOf(table1),
            selectedTableIndex = 0,
            tableServiceChargeApplied = emptyMap(),
            tableServiceChargeManuallyRemoved = emptyMap()
        )

        // Service charge should be applied for new table (assuming auto-apply is enabled)
        val shouldApply1 = state1.tableServiceChargeManuallyRemoved[101] ?: false
        assertFalse("Service charge should not be marked as manually removed for new table", shouldApply1)
    }

    @Test
    fun `test service charge manual removal tracking`() {
        val table1 = AreaTableSelectionHelper.AreaTableSelection(
            areaId = 1,
            areaName = "Main Area",
            tableId = 101,
            tableName = "Table 1",
            tableCapacity = 4
        )

        // Test case: Service charge manually removed should be tracked
        val state = ProductsScreenState(
            selectedTables = listOf(table1),
            selectedTableIndex = 0,
            tableServiceChargeApplied = mapOf(101 to false),
            tableServiceChargeManuallyRemoved = mapOf(101 to true)
        )

        val wasManuallyRemoved = state.tableServiceChargeManuallyRemoved[101] ?: false
        assertTrue("Service charge manual removal should be tracked", wasManuallyRemoved)

        val isCurrentlyApplied = state.tableServiceChargeApplied[101] ?: false
        assertFalse("Service charge should not be applied when manually removed", isCurrentlyApplied)
    }

    @Test
    fun `test service charge state for multiple tables`() {
        val table1 = AreaTableSelectionHelper.AreaTableSelection(
            areaId = 1,
            areaName = "Main Area",
            tableId = 101,
            tableName = "Table 1",
            tableCapacity = 4
        )

        val table2 = AreaTableSelectionHelper.AreaTableSelection(
            areaId = 1,
            areaName = "Main Area",
            tableId = 102,
            tableName = "Table 2",
            tableCapacity = 6
        )

        // Test case: Different service charge states for different tables
        val state = ProductsScreenState(
            selectedTables = listOf(table1, table2),
            selectedTableIndex = 0,
            tableServiceChargeApplied = mapOf(101 to true, 102 to false),
            tableServiceChargeManuallyRemoved = mapOf(101 to false, 102 to true)
        )

        // Table 1: Service charge applied, not manually removed
        val table1Applied = state.tableServiceChargeApplied[101] ?: false
        val table1ManuallyRemoved = state.tableServiceChargeManuallyRemoved[101] ?: false
        assertTrue("Table 1 should have service charge applied", table1Applied)
        assertFalse("Table 1 should not have service charge manually removed", table1ManuallyRemoved)

        // Table 2: Service charge not applied, manually removed
        val table2Applied = state.tableServiceChargeApplied[102] ?: false
        val table2ManuallyRemoved = state.tableServiceChargeManuallyRemoved[102] ?: false
        assertFalse("Table 2 should not have service charge applied", table2Applied)
        assertTrue("Table 2 should have service charge manually removed", table2ManuallyRemoved)
    }

    @Test
    fun `test isServiceChargeApplied for current table`() {
        val table1 = AreaTableSelectionHelper.AreaTableSelection(
            areaId = 1,
            areaName = "Main Area",
            tableId = 101,
            tableName = "Table 1",
            tableCapacity = 4
        )

        // Test case: Check service charge status for current table
        val state = ProductsScreenState(
            selectedTables = listOf(table1),
            selectedTableIndex = 0,
            tableServiceChargeApplied = mapOf(101 to true),
            serviceChargeApplied = false // Global service charge not applied
        )

        val isApplied = state.isServiceChargeApplied()
        assertTrue("Service charge should be applied for current table", isApplied)
    }

    @Test
    fun `test isServiceChargeApplied for global order`() {
        // Test case: Check service charge status for global order (no tables)
        val state = ProductsScreenState(
            selectedTables = emptyList(),
            selectedTableIndex = 0,
            serviceChargeApplied = true,
            tableServiceChargeApplied = emptyMap()
        )

        val isApplied = state.isServiceChargeApplied()
        assertTrue("Service charge should be applied for global order", isApplied)
    }
}
