package com.thedasagroup.suminative.ui.products

import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.StoreItem
import com.thedasagroup.suminative.data.model.response.options_details.OptionDetails
import com.thedasagroup.suminative.ui.areas.AreaTableSelectionHelper
import org.junit.Test
import org.junit.Assert.*

/**
 * Test class for auto course selection functionality
 */
class AutoCourseSelectionTest {

    @Test
    fun `test auto course creation when no courses exist - global cart`() {
        // Given - empty state with no courses
        val initialState = ProductsScreenState(
            availableCourses = emptyList(),
            selectedCourseForNewItems = "",
            currentActiveCourse = null,
            order = Order(carts = emptyList())
        )

        // When - simulating the logic from createAndSelectFirstCourse for global cart
        val updatedState = initialState.copy(
            availableCourses = listOf(MealCourse(name = "Course 1")),
            selectedCourseForNewItems = "Course 1",
            currentActiveCourse = "Course 1"
        )

        // Then - verify course was created and auto-selected
        assertEquals(1, updatedState.availableCourses.size)
        assertEquals("Course 1", updatedState.availableCourses.first().name)
        assertEquals("Course 1", updatedState.selectedCourseForNewItems)
        assertEquals("Course 1", updatedState.currentActiveCourse)
    }

    @Test
    fun `test auto course creation when no courses exist - table specific`() {
        // Given - empty state with table selected but no courses
        val tableSelection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )
        val initialState = ProductsScreenState(
            selectedTables = listOf(tableSelection),
            selectedTableIndex = 0,
            tableAvailableCourses = mapOf(1 to emptyList()),
            tableSelectedCourseForNewItems = mapOf(1 to ""),
            tableActiveCourses = mapOf(1 to ""),
            tableOrders = mapOf(1 to Order(carts = emptyList()))
        )

        // When - simulating the logic from createAndSelectFirstCourse for table-specific cart
        val updatedState = initialState.copy(
            tableAvailableCourses = mapOf(1 to listOf(MealCourse(name = "Course 1"))),
            tableSelectedCourseForNewItems = mapOf(1 to "Course 1"),
            tableActiveCourses = mapOf(1 to "Course 1")
        )

        // Then - verify course was created and auto-selected for the table
        assertEquals(1, updatedState.tableAvailableCourses[1]?.size)
        assertEquals("Course 1", updatedState.tableAvailableCourses[1]?.first()?.name)
        assertEquals("Course 1", updatedState.tableSelectedCourseForNewItems[1])
        assertEquals("Course 1", updatedState.tableActiveCourses[1])
    }

    @Test
    fun `test no course creation when courses already exist`() {
        // Given - state with existing courses
        val existingCourse = MealCourse(name = "Existing Course")
        val initialState = ProductsScreenState(
            availableCourses = listOf(existingCourse),
            selectedCourseForNewItems = "existing_course",
            currentActiveCourse = "existing_course",
            order = Order(carts = emptyList())
        )

        // When - checking if courses exist (they do)
        val coursesExist = initialState.availableCourses.isNotEmpty()

        // Then - no new course should be created
        assertTrue(coursesExist)
        assertEquals(1, initialState.availableCourses.size)
        assertEquals("Existing Course", initialState.availableCourses.first().name)
    }

    @Test
    fun `test getCurrentTableAvailableCourses returns empty when no courses exist`() {
        // Given - empty state
        val initialState = ProductsScreenState(
            availableCourses = emptyList(),
            tableAvailableCourses = emptyMap()
        )

        // When - getting current table available courses
        val availableCourses = initialState.getCurrentTableAvailableCourses()

        // Then - should return empty list
        assertTrue(availableCourses.isEmpty())
    }

    @Test
    fun `test getCurrentTableSelectedCourseForNewItems returns empty when no course selected`() {
        // Given - empty state
        val initialState = ProductsScreenState(
            selectedCourseForNewItems = "",
            tableSelectedCourseForNewItems = emptyMap()
        )

        // When - getting current table selected course
        val selectedCourse = initialState.getCurrentTableSelectedCourseForNewItems()

        // Then - should return empty string
        assertEquals("", selectedCourse)
    }
}
