package com.thedasagroup.suminative.ui.products

import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.response.products.StockItem
import com.thedasagroup.suminative.ui.areas.AreaTableSelectionHelper
import org.junit.Test
import org.junit.Assert.*

/**
 * Test class for auto course creation when adding first item to cart
 */
class AddItemAutoCourseCreationTest {

    @Test
    fun `test addItemToCart creates course for table when no courses exist`() {
        // Given - table with no courses and empty cart
        val tableSelection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )

        val initialState = ProductsScreenState(
            selectedTables = listOf(tableSelection),
            selectedTableIndex = 0,
            tableOrders = mapOf(1 to Order(carts = emptyList())),
            tableAvailableCourses = mapOf(1 to emptyList()), // No courses
            tableSelectedCourseForNewItems = mapOf(1 to ""),
            tableActiveCourses = mapOf(1 to "")
        )

        // When - simulating addItemToCart logic for first item
        val availableCourses = initialState.getCurrentTableAvailableCourses()
        val shouldCreateCourse = availableCourses.isEmpty()

        // Then - should trigger course creation
        assertTrue("Should detect no courses and trigger creation", shouldCreateCourse)

        // When - simulating createAndSelectFirstCourse for table
        val stateAfterCourseCreation = initialState.copy(
            tableAvailableCourses = mapOf(1 to listOf(MealCourse(name = "Course 1"))),
            tableSelectedCourseForNewItems = mapOf(1 to "Course 1"),
            tableActiveCourses = mapOf(1 to "Course 1")
        )

        // Then - verify course was created and selected
        val coursesAfterCreation = stateAfterCourseCreation.getCurrentTableAvailableCourses()
        val selectedCourseAfterCreation = stateAfterCourseCreation.getCurrentTableSelectedCourseForNewItems()

        assertEquals("Should have created Course 1", 1, coursesAfterCreation.size)
        assertEquals("Course should be named Course 1", "Course 1", coursesAfterCreation[0].name)
        assertEquals("Course 1 should be auto-selected", "Course 1", selectedCourseAfterCreation)
    }

    @Test
    fun `test addItemToCart creates course for global cart when no courses exist`() {
        // Given - no table selected, no global courses, empty cart
        val initialState = ProductsScreenState(
            selectedTables = emptyList(),
            selectedTableIndex = -1,
            order = Order(carts = emptyList()),
            availableCourses = emptyList(), // No courses
            selectedCourseForNewItems = "",
            currentActiveCourse = ""
        )

        // When - simulating addItemToCart logic for first item
        val availableCourses = initialState.getCurrentTableAvailableCourses()
        val shouldCreateCourse = availableCourses.isEmpty()

        // Then - should trigger course creation
        assertTrue("Should detect no courses and trigger creation", shouldCreateCourse)

        // When - simulating createAndSelectFirstCourse for global
        val stateAfterCourseCreation = initialState.copy(
            availableCourses = listOf(MealCourse(name = "Course 1")),
            selectedCourseForNewItems = "Course 1",
            currentActiveCourse = "Course 1"
        )

        // Then - verify course was created and selected
        val coursesAfterCreation = stateAfterCourseCreation.getCurrentTableAvailableCourses()
        val selectedCourseAfterCreation = stateAfterCourseCreation.getCurrentTableSelectedCourseForNewItems()

        assertEquals("Should have created Course 1", 1, coursesAfterCreation.size)
        assertEquals("Course should be named Course 1", "Course 1", coursesAfterCreation[0].name)
        assertEquals("Course 1 should be auto-selected", "Course 1", selectedCourseAfterCreation)
    }

    @Test
    fun `test addItemToCart does not create course when courses already exist for table`() {
        // Given - table with existing courses
        val tableSelection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )

        val existingCourses = listOf(MealCourse(name = "Existing Course"))
        val initialState = ProductsScreenState(
            selectedTables = listOf(tableSelection),
            selectedTableIndex = 0,
            tableOrders = mapOf(1 to Order(carts = emptyList())),
            tableAvailableCourses = mapOf(1 to existingCourses),
            tableSelectedCourseForNewItems = mapOf(1 to "Existing Course"),
            tableActiveCourses = mapOf(1 to "Existing Course")
        )

        // When - checking if course creation should trigger
        val availableCourses = initialState.getCurrentTableAvailableCourses()
        val shouldCreateCourse = availableCourses.isEmpty()

        // Then - should NOT trigger course creation
        assertFalse("Should not create course when courses already exist", shouldCreateCourse)
        assertEquals("Should keep existing course", 1, availableCourses.size)
        assertEquals("Should keep existing course name", "Existing Course", availableCourses[0].name)
    }

    @Test
    fun `test addItemToCart does not create course when courses already exist for global`() {
        // Given - global courses exist
        val existingCourses = listOf(MealCourse(name = "Existing Course"))
        val initialState = ProductsScreenState(
            selectedTables = emptyList(),
            selectedTableIndex = -1,
            order = Order(carts = emptyList()),
            availableCourses = existingCourses,
            selectedCourseForNewItems = "Existing Course",
            currentActiveCourse = "Existing Course"
        )

        // When - checking if course creation should trigger
        val availableCourses = initialState.getCurrentTableAvailableCourses()
        val shouldCreateCourse = availableCourses.isEmpty()

        // Then - should NOT trigger course creation
        assertFalse("Should not create course when courses already exist", shouldCreateCourse)
        assertEquals("Should keep existing course", 1, availableCourses.size)
        assertEquals("Should keep existing course name", "Existing Course", availableCourses[0].name)
    }

    @Test
    fun `test course assignment to item after auto-creation for table`() {
        // Given - table with no courses initially
        val tableSelection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )

        val initialState = ProductsScreenState(
            selectedTables = listOf(tableSelection),
            selectedTableIndex = 0,
            tableAvailableCourses = mapOf(1 to emptyList())
        )

        // When - after course creation
        val stateAfterCourseCreation = initialState.copy(
            tableAvailableCourses = mapOf(1 to listOf(MealCourse(name = "Course 1"))),
            tableSelectedCourseForNewItems = mapOf(1 to "Course 1")
        )

        // When - getting course ID for new item
        val selectedCourse = stateAfterCourseCreation.getCurrentTableSelectedCourseForNewItems()
        val fallbackCourse = stateAfterCourseCreation.getCurrentTableAvailableCourses().firstOrNull()?.name ?: ""
        val courseIdForItem = selectedCourse.ifEmpty { fallbackCourse }

        // Then - item should get the auto-created course
        assertEquals("Item should be assigned to Course 1", "Course 1", courseIdForItem)
    }

    @Test
    fun `test course assignment to item after auto-creation for global`() {
        // Given - no global courses initially
        val initialState = ProductsScreenState(
            selectedTables = emptyList(),
            selectedTableIndex = -1,
            availableCourses = emptyList()
        )

        // When - after course creation
        val stateAfterCourseCreation = initialState.copy(
            availableCourses = listOf(MealCourse(name = "Course 1")),
            selectedCourseForNewItems = "Course 1"
        )

        // When - getting course ID for new item
        val selectedCourse = stateAfterCourseCreation.getCurrentTableSelectedCourseForNewItems()
        val fallbackCourse = stateAfterCourseCreation.getCurrentTableAvailableCourses().firstOrNull()?.name ?: ""
        val courseIdForItem = selectedCourse.ifEmpty { fallbackCourse }

        // Then - item should get the auto-created course
        assertEquals("Item should be assigned to Course 1", "Course 1", courseIdForItem)
    }

    @Test
    fun `test multiple tables with independent course auto-creation`() {
        // Given - multiple tables, both with no courses
        val table1 = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1, areaId = 1, tableName = "Table 1", areaName = "Area 1"
        )
        val table2 = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 2, areaId = 1, tableName = "Table 2", areaName = "Area 1"
        )

        val initialState = ProductsScreenState(
            selectedTables = listOf(table1, table2),
            selectedTableIndex = 0, // Table 1 selected
            tableAvailableCourses = mapOf(
                1 to emptyList(), // No courses for Table 1
                2 to emptyList()  // No courses for Table 2
            )
        )

        // When - adding item to Table 1 (should create course for Table 1 only)
        val stateAfterTable1CourseCreation = initialState.copy(
            tableAvailableCourses = mapOf(
                1 to listOf(MealCourse(name = "Course 1")), // Course created for Table 1
                2 to emptyList() // Table 2 unchanged
            ),
            tableSelectedCourseForNewItems = mapOf(
                1 to "Course 1", // Course selected for Table 1
                2 to "" // Table 2 unchanged
            )
        )

        // Then - only Table 1 should have course created
        val table1Courses = stateAfterTable1CourseCreation.tableAvailableCourses[1] ?: emptyList()
        val table2Courses = stateAfterTable1CourseCreation.tableAvailableCourses[2] ?: emptyList()

        assertEquals("Table 1 should have course created", 1, table1Courses.size)
        assertEquals("Table 1 course should be Course 1", "Course 1", table1Courses[0].name)
        assertEquals("Table 2 should have no courses", 0, table2Courses.size)

        // When - switching to Table 2 and adding item (should create course for Table 2)
        val stateTable2Selected = stateAfterTable1CourseCreation.copy(selectedTableIndex = 1)
        val table2CoursesCheck = stateTable2Selected.getCurrentTableAvailableCourses()
        val shouldCreateCourseForTable2 = table2CoursesCheck.isEmpty()

        // Then - Table 2 should trigger course creation
        assertTrue("Table 2 should trigger course creation", shouldCreateCourseForTable2)
    }
}
