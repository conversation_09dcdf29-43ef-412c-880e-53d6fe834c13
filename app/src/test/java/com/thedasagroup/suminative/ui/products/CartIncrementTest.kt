package com.thedasagroup.suminative.ui.products

import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.StoreItem
import com.thedasagroup.suminative.data.model.response.options_details.OptionDetails
import com.thedasagroup.suminative.data.model.response.stock.StockItem
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for cart increment functionality
 */
class CartIncrementTest {

    @Test
    fun `test cart should increment quantity when same item added twice`() {
        // Create a sample StockItem
        val stockItem = StockItem(
            id = 1,
            name = "Test Item",
            price = 10.0,
            tax = 1.0,
            stock = 5
        )

        // Create initial cart with one item
        val initialCartItem = Cart(
            storeItem = stockItem.toStoreItem().copy(quantity = 1),
            quantity = 1,
            price = 10.0,
            tax = 1.0,
            netPayable = 11.0
        )

        val initialOrder = Order(
            carts = listOf(initialCartItem),
            netPayable = 11.0,
            tax = 1.0,
            totalPrice = 12.0
        )

        // Simulate adding the same item again
        // In the actual implementation, this would be handled by addItemToCart method
        // Here we're testing the logic that should happen

        val existingCartItemIndex = initialOrder.carts?.indexOfFirst { cartItem ->
            cartItem.storeItem?.id == stockItem.id
        } ?: -1

        assertTrue("Should find existing item in cart", existingCartItemIndex != -1)

        if (existingCartItemIndex != -1) {
            val existingCartItem = initialOrder.carts!![existingCartItemIndex]
            val newQuantity = (existingCartItem.quantity ?: 0) + 1

            assertEquals("New quantity should be 2", 2, newQuantity)
        }
    }

    @Test
    fun `test cart should add new item when different item added`() {
        // Create first item
        val stockItem1 = StockItem(
            id = 1,
            name = "Test Item 1",
            price = 10.0,
            tax = 1.0,
            stock = 5
        )

        // Create second item (different ID)
        val stockItem2 = StockItem(
            id = 2,
            name = "Test Item 2",
            price = 15.0,
            tax = 1.5,
            stock = 3
        )

        // Create initial cart with first item
        val initialCartItem = Cart(
            storeItem = stockItem1.toStoreItem().copy(quantity = 1),
            quantity = 1,
            price = 10.0,
            tax = 1.0,
            netPayable = 11.0
        )

        val initialOrder = Order(
            carts = listOf(initialCartItem),
            netPayable = 11.0,
            tax = 1.0,
            totalPrice = 12.0
        )

        // Check if second item exists in cart
        val existingCartItemIndex = initialOrder.carts?.indexOfFirst { cartItem ->
            cartItem.storeItem?.id == stockItem2.id
        } ?: -1

        assertEquals("Should not find different item in cart", -1, existingCartItemIndex)
    }

    @Test
    fun `test empty cart should add new item`() {
        val stockItem = StockItem(
            id = 1,
            name = "Test Item",
            price = 10.0,
            tax = 1.0,
            stock = 5
        )

        val emptyOrder = Order(
            carts = emptyList(),
            netPayable = 0.0,
            tax = 0.0,
            totalPrice = 0.0
        )

        val existingCartItemIndex = emptyOrder.carts?.indexOfFirst { cartItem ->
            cartItem.storeItem?.id == stockItem.id
        } ?: -1

        assertEquals("Should not find item in empty cart", -1, existingCartItemIndex)
    }

    @Test
    fun `test cart with multiple items should find correct item to increment`() {
        val stockItem1 = StockItem(id = 1, name = "Item 1", price = 10.0)
        val stockItem2 = StockItem(id = 2, name = "Item 2", price = 15.0)
        val stockItem3 = StockItem(id = 3, name = "Item 3", price = 20.0)

        val cartItems = listOf(
            Cart(storeItem = stockItem1.toStoreItem().copy(quantity = 1), quantity = 1),
            Cart(storeItem = stockItem2.toStoreItem().copy(quantity = 2), quantity = 2),
            Cart(storeItem = stockItem3.toStoreItem().copy(quantity = 1), quantity = 1)
        )

        val order = Order(carts = cartItems)

        // Try to find item 2
        val existingCartItemIndex = order.carts?.indexOfFirst { cartItem ->
            cartItem.storeItem?.id == stockItem2.id
        } ?: -1

        assertEquals("Should find item 2 at index 1", 1, existingCartItemIndex)

        if (existingCartItemIndex != -1) {
            val existingItem = order.carts!![existingCartItemIndex]
            assertEquals("Found item should have quantity 2", 2, existingItem.quantity)
        }
    }

    @Test
    fun `test cart should not increment quantity when same item already sent to kitchen`() {
        // Create a sample StockItem
        val stockItem = StockItem(
            id = 1,
            name = "Test Item",
            price = 10.0,
            tax = 1.0,
            stock = 5
        )

        // Create initial cart with one item that has been sent to kitchen
        val sentToKitchenCartItem = Cart(
            storeItem = stockItem.toStoreItem().copy(quantity = 1),
            quantity = 1,
            price = 10.0,
            tax = 1.0,
            netPayable = 11.0,
            sentToKitchen = true // This item has been sent to kitchen
        )

        val initialOrder = Order(
            carts = listOf(sentToKitchenCartItem),
            netPayable = 11.0,
            tax = 1.0,
            totalPrice = 12.0
        )

        // Simulate the logic from addItemToCart method
        // Should NOT find existing item because it's been sent to kitchen
        val existingCartItemIndex = initialOrder.carts?.indexOfFirst { cartItem ->
            cartItem.storeItem?.id == stockItem.id &&
                    !cartItem.sentToKitchen // Don't merge with items already sent to kitchen
        } ?: -1

        assertEquals("Should not find existing item that was sent to kitchen", -1, existingCartItemIndex)
    }

    @Test
    fun `test cart should increment quantity when same item not sent to kitchen`() {
        // Create a sample StockItem
        val stockItem = StockItem(
            id = 1,
            name = "Test Item",
            price = 10.0,
            tax = 1.0,
            stock = 5
        )

        // Create initial cart with one item that has NOT been sent to kitchen
        val notSentToKitchenCartItem = Cart(
            storeItem = stockItem.toStoreItem().copy(quantity = 1),
            quantity = 1,
            price = 10.0,
            tax = 1.0,
            netPayable = 11.0,
            sentToKitchen = false // This item has NOT been sent to kitchen
        )

        val initialOrder = Order(
            carts = listOf(notSentToKitchenCartItem),
            netPayable = 11.0,
            tax = 1.0,
            totalPrice = 12.0
        )

        // Simulate the logic from addItemToCart method
        // Should find existing item because it hasn't been sent to kitchen
        val existingCartItemIndex = initialOrder.carts?.indexOfFirst { cartItem ->
            cartItem.storeItem?.id == stockItem.id &&
                    !cartItem.sentToKitchen // Don't merge with items already sent to kitchen
        } ?: -1

        assertTrue("Should find existing item that was not sent to kitchen", existingCartItemIndex != -1)

        if (existingCartItemIndex != -1) {
            val existingCartItem = initialOrder.carts!![existingCartItemIndex]
            val newQuantity = (existingCartItem.quantity ?: 0) + 1

            assertEquals("New quantity should be 2", 2, newQuantity)
        }
    }

    @Test
    fun `test cart with mixed sent and not sent items should only increment not sent items`() {
        val stockItem = StockItem(id = 1, name = "Item 1", price = 10.0)

        val cartItems = listOf(
            Cart(
                storeItem = stockItem.toStoreItem().copy(quantity = 1),
                quantity = 1,
                sentToKitchen = true // Sent to kitchen
            ),
            Cart(
                storeItem = stockItem.toStoreItem().copy(quantity = 2),
                quantity = 2,
                sentToKitchen = false // Not sent to kitchen
            )
        )

        val order = Order(carts = cartItems)

        // Try to find item that can be incremented (not sent to kitchen)
        val existingCartItemIndex = order.carts?.indexOfFirst { cartItem ->
            cartItem.storeItem?.id == stockItem.id &&
                    !cartItem.sentToKitchen
        } ?: -1

        assertEquals("Should find the item that was not sent to kitchen at index 1", 1, existingCartItemIndex)
    }
}
