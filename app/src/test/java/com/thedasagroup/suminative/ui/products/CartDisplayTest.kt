package com.thedasagroup.suminative.ui.products

import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.ui.reservations.AreaTableSelectionHelper
import org.junit.Test
import org.junit.Assert.*

/**
 * Test class for cart display functionality in ProductsScreenViewModel
 */
class CartDisplayTest {

    @Test
    fun `test cart shows when table is added even if empty`() {
        // Given
        val tableSelection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )
        val initialState = ProductsScreenState(showCart = false)

        // When - simulate adding a table (this would be done by addSelectedTable method)
        val updatedState = initialState.copy(
            selectedTables = listOf(tableSelection),
            selectedTableIndex = 0,
            tableOrders = mapOf(1 to Order()), // Empty order
            showCart = true // This is what addSelectedTable should set
        )

        // Then
        assertTrue(updatedState.showCart)
        assertTrue(updatedState.tableOrders[1]?.carts?.isEmpty() ?: true) // Cart is empty but still shown
    }

    @Test
    fun `test cart shows when existing table is selected`() {
        // Given
        val tableSelection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )
        val initialState = ProductsScreenState(
            selectedTables = listOf(tableSelection),
            selectedTableIndex = 0,
            tableOrders = mapOf(1 to Order()),
            showCart = false
        )

        // When - simulate selecting existing table (this would be done by setSelectedTableIndex)
        val updatedState = initialState.copy(showCart = true)

        // Then
        assertTrue(updatedState.showCart)
    }

    @Test
    fun `test cart shows when table index changes`() {
        // Given
        val table1Selection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )
        val table2Selection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 2,
            areaId = 1,
            tableName = "Table 2",
            areaName = "Main Area"
        )
        val initialState = ProductsScreenState(
            selectedTables = listOf(table1Selection, table2Selection),
            selectedTableIndex = 0,
            tableOrders = mapOf(
                1 to Order(),
                2 to Order()
            ),
            showCart = false
        )

        // When - switch to table 2 (this would be done by setSelectedTableIndex)
        val updatedState = initialState.copy(
            selectedTableIndex = 1,
            showCart = true // This is what setSelectedTableIndex should set
        )

        // Then
        assertTrue(updatedState.showCart)
        assertEquals(1, updatedState.selectedTableIndex)
    }

    @Test
    fun `test cart visibility with multiple tables`() {
        // Given
        val table1Selection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )
        val table2Selection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 2,
            areaId = 1,
            tableName = "Table 2",
            areaName = "Main Area"
        )

        // Test 1: No tables selected - cart should be hidden
        val stateNoTables = ProductsScreenState(showCart = false)
        assertFalse(stateNoTables.showCart)

        // Test 2: One table selected - cart should be shown
        val stateOneTable = ProductsScreenState(
            selectedTables = listOf(table1Selection),
            selectedTableIndex = 0,
            tableOrders = mapOf(1 to Order()),
            showCart = true
        )
        assertTrue(stateOneTable.showCart)

        // Test 3: Multiple tables selected - cart should be shown
        val stateMultipleTables = ProductsScreenState(
            selectedTables = listOf(table1Selection, table2Selection),
            selectedTableIndex = 0,
            tableOrders = mapOf(
                1 to Order(),
                2 to Order()
            ),
            showCart = true
        )
        assertTrue(stateMultipleTables.showCart)
    }

    @Test
    fun `test cart remains visible when switching between tables with empty carts`() {
        // Given
        val table1Selection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )
        val table2Selection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 2,
            areaId = 1,
            tableName = "Table 2",
            areaName = "Main Area"
        )
        val initialState = ProductsScreenState(
            selectedTables = listOf(table1Selection, table2Selection),
            selectedTableIndex = 0,
            tableOrders = mapOf(
                1 to Order(carts = emptyList()), // Empty cart
                2 to Order(carts = emptyList())  // Empty cart
            ),
            showCart = true
        )

        // When - switch to table 2
        val updatedState = initialState.copy(
            selectedTableIndex = 1,
            showCart = true // Should remain true even with empty cart
        )

        // Then
        assertTrue(updatedState.showCart)
        assertTrue(updatedState.tableOrders[2]?.carts?.isEmpty() ?: true)
    }
}
