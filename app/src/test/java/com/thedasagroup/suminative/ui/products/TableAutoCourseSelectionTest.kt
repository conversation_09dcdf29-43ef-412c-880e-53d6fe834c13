package com.thedasagroup.suminative.ui.products

import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.ui.areas.AreaTableSelectionHelper
import org.junit.Test
import org.junit.Assert.*

/**
 * Test class for auto course selection functionality on tables
 */
class TableAutoCourseSelectionTest {

    @Test
    fun `test auto course creation for table when no courses exist`() {
        // Given - table with no courses
        val tableSelection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )

        val initialState = ProductsScreenState(
            selectedTables = listOf(tableSelection),
            selectedTableIndex = 0,
            tableOrders = mapOf(1 to Order(carts = emptyList())),
            tableAvailableCourses = mapOf(1 to emptyList()), // No courses for table
            tableSelectedCourseForNewItems = mapOf(1 to ""),
            tableActiveCourses = mapOf(1 to "")
        )

        // When - checking if courses exist (they don't)
        val availableCourses = initialState.getCurrentTableAvailableCourses()

        // Then - should return empty list, triggering auto-course creation
        assertTrue("Table should have no courses initially", availableCourses.isEmpty())
    }

    @Test
    fun `test createAndSelectFirstCourse for table`() {
        // Given - table with no courses
        val tableSelection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )

        val initialState = ProductsScreenState(
            selectedTables = listOf(tableSelection),
            selectedTableIndex = 0,
            tableOrders = mapOf(1 to Order()),
            tableAvailableCourses = mapOf(1 to emptyList()),
            tableSelectedCourseForNewItems = mapOf(1 to ""),
            tableActiveCourses = mapOf(1 to "")
        )

        // When - simulating createAndSelectFirstCourse logic for table
        val updatedState = initialState.copy(
            tableAvailableCourses = mapOf(1 to listOf(MealCourse(name = "Course 1"))),
            tableSelectedCourseForNewItems = mapOf(1 to "Course 1"),
            tableActiveCourses = mapOf(1 to "Course 1")
        )

        // Then - verify course was created and auto-selected for the table
        assertEquals("Table should have one course", 1, updatedState.tableAvailableCourses[1]?.size)
        assertEquals("Course name should be 'Course 1'", "Course 1", updatedState.tableAvailableCourses[1]?.first()?.name)
        assertEquals("Course should be selected for new items", "Course 1", updatedState.tableSelectedCourseForNewItems[1])
        assertEquals("Course should be active", "Course 1", updatedState.tableActiveCourses[1])
    }

    @Test
    fun `test getCurrentTableAvailableCourses for table with courses`() {
        // Given - table with courses
        val tableSelection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )

        val courses = listOf(MealCourse(name = "Course 1"), MealCourse(name = "Course 2"))
        val state = ProductsScreenState(
            selectedTables = listOf(tableSelection),
            selectedTableIndex = 0,
            tableAvailableCourses = mapOf(1 to courses)
        )

        // When - getting available courses
        val availableCourses = state.getCurrentTableAvailableCourses()

        // Then - should return table-specific courses
        assertEquals("Should return table courses", 2, availableCourses.size)
        assertEquals("First course should be Course 1", "Course 1", availableCourses[0].name)
        assertEquals("Second course should be Course 2", "Course 2", availableCourses[1].name)
    }

    @Test
    fun `test getCurrentTableSelectedCourseForNewItems for table`() {
        // Given - table with selected course
        val tableSelection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )

        val state = ProductsScreenState(
            selectedTables = listOf(tableSelection),
            selectedTableIndex = 0,
            tableSelectedCourseForNewItems = mapOf(1 to "Course 1")
        )

        // When - getting selected course
        val selectedCourse = state.getCurrentTableSelectedCourseForNewItems()

        // Then - should return table-specific selected course
        assertEquals("Should return table selected course", "Course 1", selectedCourse)
    }

    @Test
    fun `test multiple tables with different course states`() {
        // Given - multiple tables with different course configurations
        val table1 = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1, areaId = 1, tableName = "Table 1", areaName = "Area 1"
        )
        val table2 = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 2, areaId = 1, tableName = "Table 2", areaName = "Area 1"
        )

        val state = ProductsScreenState(
            selectedTables = listOf(table1, table2),
            selectedTableIndex = 0, // Table 1 selected
            tableAvailableCourses = mapOf(
                1 to listOf(MealCourse(name = "Course 1")), // Table 1 has courses
                2 to emptyList() // Table 2 has no courses
            ),
            tableSelectedCourseForNewItems = mapOf(
                1 to "Course 1",
                2 to ""
            )
        )

        // When - checking Table 1 (selected)
        val table1Courses = state.getCurrentTableAvailableCourses()
        val table1Selected = state.getCurrentTableSelectedCourseForNewItems()

        // Then - Table 1 should have courses
        assertEquals("Table 1 should have courses", 1, table1Courses.size)
        assertEquals("Table 1 should have selected course", "Course 1", table1Selected)

        // When - switching to Table 2
        val stateTable2 = state.copy(selectedTableIndex = 1)
        val table2Courses = stateTable2.getCurrentTableAvailableCourses()
        val table2Selected = stateTable2.getCurrentTableSelectedCourseForNewItems()

        // Then - Table 2 should have no courses (triggering auto-creation)
        assertTrue("Table 2 should have no courses", table2Courses.isEmpty())
        assertEquals("Table 2 should have no selected course", "", table2Selected)
    }

    @Test
    fun `test fallback to global courses when no table selected`() {
        // Given - global courses but no table selected
        val globalCourses = listOf(MealCourse(name = "Global Course"))
        val state = ProductsScreenState(
            selectedTables = emptyList(),
            selectedTableIndex = -1, // No table selected
            availableCourses = globalCourses,
            selectedCourseForNewItems = "Global Course"
        )

        // When - getting courses
        val availableCourses = state.getCurrentTableAvailableCourses()
        val selectedCourse = state.getCurrentTableSelectedCourseForNewItems()

        // Then - should return global courses
        assertEquals("Should return global courses", 1, availableCourses.size)
        assertEquals("Should return global course name", "Global Course", availableCourses[0].name)
        assertEquals("Should return global selected course", "Global Course", selectedCourse)
    }

    @Test
    fun `test complete auto-course selection flow for table`() {
        // Given - new table with no courses (simulating addSelectedTable result)
        val tableSelection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )

        val initialState = ProductsScreenState(
            selectedTables = listOf(tableSelection),
            selectedTableIndex = 0,
            tableOrders = mapOf(1 to Order(carts = emptyList())),
            tableAvailableCourses = mapOf(1 to emptyList()), // No courses initially
            tableSelectedCourseForNewItems = mapOf(1 to ""),
            tableActiveCourses = mapOf(1 to "")
        )

        // When - checking if auto-course creation should trigger
        val availableCourses = initialState.getCurrentTableAvailableCourses()
        val shouldCreateCourse = availableCourses.isEmpty()

        // Then - should trigger auto-course creation
        assertTrue("Should trigger auto-course creation", shouldCreateCourse)

        // When - simulating auto-course creation (createAndSelectFirstCourse)
        val stateAfterCourseCreation = initialState.copy(
            tableAvailableCourses = mapOf(1 to listOf(MealCourse(name = "Course 1"))),
            tableSelectedCourseForNewItems = mapOf(1 to "Course 1"),
            tableActiveCourses = mapOf(1 to "Course 1")
        )

        // Then - verify course was created and selected
        val coursesAfterCreation = stateAfterCourseCreation.getCurrentTableAvailableCourses()
        val selectedCourseAfterCreation = stateAfterCourseCreation.getCurrentTableSelectedCourseForNewItems()

        assertEquals("Should have one course after creation", 1, coursesAfterCreation.size)
        assertEquals("Course should be named 'Course 1'", "Course 1", coursesAfterCreation[0].name)
        assertEquals("Course should be auto-selected", "Course 1", selectedCourseAfterCreation)

        // When - adding item to cart (should use the auto-selected course)
        val courseIdForNewItem = selectedCourseAfterCreation.ifEmpty {
            coursesAfterCreation.firstOrNull()?.name ?: ""
        }

        // Then - item should be assigned to the auto-selected course
        assertEquals("Item should be assigned to Course 1", "Course 1", courseIdForNewItem)
    }

    @Test
    fun `test table course initialization in addSelectedTable`() {
        // Given - initial state with no tables
        val initialState = ProductsScreenState(
            selectedTables = emptyList(),
            tableAvailableCourses = emptyMap(),
            tableSelectedCourseForNewItems = emptyMap(),
            tableActiveCourses = emptyMap()
        )

        val tableSelection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )

        // When - simulating addSelectedTable logic
        val updatedState = initialState.copy(
            selectedTables = listOf(tableSelection),
            selectedTableIndex = 0,
            tableOrders = mapOf(1 to Order()),
            tableAvailableCourses = mapOf(1 to emptyList()), // Initialized but empty
            tableSelectedCourseForNewItems = mapOf(1 to ""),
            tableActiveCourses = mapOf(1 to "")
        )

        // Then - table should be properly initialized for course auto-creation
        assertTrue("Table should be in selectedTables", updatedState.selectedTables.isNotEmpty())
        assertEquals("Table should be selected", 0, updatedState.selectedTableIndex)
        assertTrue("Table courses should be initialized but empty", updatedState.tableAvailableCourses.containsKey(1))
        assertTrue("Table courses should be empty (ready for auto-creation)", updatedState.tableAvailableCourses[1]?.isEmpty() ?: false)
        assertEquals("Selected course should be empty initially", "", updatedState.tableSelectedCourseForNewItems[1])
    }
}
