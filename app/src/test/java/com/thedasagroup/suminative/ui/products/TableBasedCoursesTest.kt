package com.thedasagroup.suminative.ui.products

import com.thedasagroup.suminative.data.model.response.area_table_selection.AreaTableSelectionHelper
import org.junit.Test
import org.junit.Assert.*

class TableBasedCoursesTest {

    @Test
    fun `test table-specific courses are isolated from global courses`() {
        val state = ProductsScreenState(
            // Global courses
            availableCourses = listOf(
                MealCourse("global_course_1", "Global Course 1", "Global Course 1"),
                MealCourse("global_course_2", "Global Course 2", "Global Course 2")
            ),
            selectedCourseForNewItems = "global_course_1",
            
            // Table-specific courses
            tableAvailableCourses = mapOf(
                1 to listOf(
                    MealCourse("table1_course_1", "Table 1 Course 1", "Table 1 Course 1"),
                    MealCourse("table1_course_2", "Table 1 Course 2", "Table 1 Course 2")
                ),
                2 to listOf(
                    MealCourse("table2_course_1", "Table 2 Course 1", "Table 2 Course 1")
                )
            ),
            tableSelectedCourseForNewItems = mapOf(
                1 to "table1_course_1",
                2 to "table2_course_1"
            ),
            
            // Table selection
            selectedTables = listOf(
                AreaTableSelectionHelper.AreaTableSelection(
                    tableId = 1,
                    tableName = "Table 1",
                    areaId = 1,
                    areaName = "Area 1"
                )
            ),
            selectedTableIndex = 0
        )

        // Test table-specific courses
        val table1Courses = state.getCurrentTableAvailableCourses()
        assertEquals(2, table1Courses.size)
        assertEquals("table1_course_1", table1Courses[0].id)
        assertEquals("table1_course_2", table1Courses[1].id)

        // Test table-specific selected course
        val table1SelectedCourse = state.getCurrentTableSelectedCourseForNewItems()
        assertEquals("table1_course_1", table1SelectedCourse)

        // Test global courses when no table is selected
        val stateNoTable = state.copy(selectedTables = emptyList())
        val globalCourses = stateNoTable.getCurrentTableAvailableCourses()
        assertEquals(2, globalCourses.size)
        assertEquals("global_course_1", globalCourses[0].id)
        assertEquals("global_course_2", globalCourses[1].id)

        val globalSelectedCourse = stateNoTable.getCurrentTableSelectedCourseForNewItems()
        assertEquals("global_course_1", globalSelectedCourse)
    }

    @Test
    fun `test different tables have independent courses`() {
        val state = ProductsScreenState(
            tableAvailableCourses = mapOf(
                1 to listOf(
                    MealCourse("table1_starters", "Starters", "Starters"),
                    MealCourse("table1_mains", "Mains", "Mains")
                ),
                2 to listOf(
                    MealCourse("table2_appetizers", "Appetizers", "Appetizers"),
                    MealCourse("table2_entrees", "Entrees", "Entrees"),
                    MealCourse("table2_desserts", "Desserts", "Desserts")
                )
            ),
            tableSelectedCourseForNewItems = mapOf(
                1 to "table1_starters",
                2 to "table2_appetizers"
            ),
            selectedTables = listOf(
                AreaTableSelectionHelper.AreaTableSelection(
                    tableId = 1,
                    tableName = "Table 1",
                    areaId = 1,
                    areaName = "Area 1"
                ),
                AreaTableSelectionHelper.AreaTableSelection(
                    tableId = 2,
                    tableName = "Table 2",
                    areaId = 1,
                    areaName = "Area 1"
                )
            )
        )

        // Test Table 1 courses
        val stateTable1 = state.copy(selectedTableIndex = 0)
        val table1Courses = stateTable1.getCurrentTableAvailableCourses()
        assertEquals(2, table1Courses.size)
        assertEquals("table1_starters", table1Courses[0].id)
        assertEquals("table1_mains", table1Courses[1].id)
        assertEquals("table1_starters", stateTable1.getCurrentTableSelectedCourseForNewItems())

        // Test Table 2 courses
        val stateTable2 = state.copy(selectedTableIndex = 1)
        val table2Courses = stateTable2.getCurrentTableAvailableCourses()
        assertEquals(3, table2Courses.size)
        assertEquals("table2_appetizers", table2Courses[0].id)
        assertEquals("table2_entrees", table2Courses[1].id)
        assertEquals("table2_desserts", table2Courses[2].id)
        assertEquals("table2_appetizers", stateTable2.getCurrentTableSelectedCourseForNewItems())
    }

    @Test
    fun `test course selection is table-specific`() {
        val state = ProductsScreenState(
            tableAvailableCourses = mapOf(
                1 to listOf(
                    MealCourse("course_1", "Course 1", "Course 1"),
                    MealCourse("course_2", "Course 2", "Course 2")
                ),
                2 to listOf(
                    MealCourse("course_a", "Course A", "Course A"),
                    MealCourse("course_b", "Course B", "Course B")
                )
            ),
            tableSelectedCourseForNewItems = mapOf(
                1 to "course_2",
                2 to "course_a"
            ),
            selectedTables = listOf(
                AreaTableSelectionHelper.AreaTableSelection(
                    tableId = 1,
                    tableName = "Table 1",
                    areaId = 1,
                    areaName = "Area 1"
                ),
                AreaTableSelectionHelper.AreaTableSelection(
                    tableId = 2,
                    tableName = "Table 2",
                    areaId = 1,
                    areaName = "Area 1"
                )
            )
        )

        // Test Table 1 course selection
        val stateTable1 = state.copy(selectedTableIndex = 0)
        assertTrue(stateTable1.isCourseSelected("course_2"))
        assertFalse(stateTable1.isCourseSelected("course_1"))
        assertFalse(stateTable1.isCourseSelected("course_a"))
        assertFalse(stateTable1.isCourseSelected("course_b"))

        // Test Table 2 course selection
        val stateTable2 = state.copy(selectedTableIndex = 1)
        assertTrue(stateTable2.isCourseSelected("course_a"))
        assertFalse(stateTable2.isCourseSelected("course_b"))
        assertFalse(stateTable2.isCourseSelected("course_1"))
        assertFalse(stateTable2.isCourseSelected("course_2"))
    }

    @Test
    fun `test empty table courses fallback to empty list`() {
        val state = ProductsScreenState(
            availableCourses = listOf(
                MealCourse("global_course", "Global Course", "Global Course")
            ),
            selectedCourseForNewItems = "global_course",
            selectedTables = listOf(
                AreaTableSelectionHelper.AreaTableSelection(
                    tableId = 99,
                    tableName = "Table 99",
                    areaId = 1,
                    areaName = "Area 1"
                )
            ),
            selectedTableIndex = 0
        )

        // Table 99 has no courses defined, should return empty list
        val tableCourses = state.getCurrentTableAvailableCourses()
        assertTrue(tableCourses.isEmpty())

        // Selected course should be empty string
        val selectedCourse = state.getCurrentTableSelectedCourseForNewItems()
        assertEquals("", selectedCourse)
    }
}
