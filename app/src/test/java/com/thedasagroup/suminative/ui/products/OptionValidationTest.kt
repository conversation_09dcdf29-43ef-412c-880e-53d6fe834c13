package com.thedasagroup.suminative.ui.products

import com.thedasagroup.suminative.data.model.request.order.OptionSet
import com.thedasagroup.suminative.data.model.response.store_orders.Option
import org.junit.Test
import org.junit.Assert.*

class OptionValidationTest {

    @Test
    fun `test validation passes when no condition 1 option sets exist`() {
        val optionSets = listOf(
            OptionSet(
                id = 1,
                name = "Optional Extras",
                condition = 0, // Not required
                options = mutableListOf(
                    Option(id = 1, name = "Extra Cheese", optionchecked = false),
                    Option(id = 2, name = "Extra Sauce", optionchecked = false)
                )
            )
        )

        val condition1OptionSets = optionSets.filter { it.condition == 1 }
        val areRequiredOptionsSelected = if (condition1OptionSets.isEmpty()) {
            true
        } else {
            condition1OptionSets.all { optionSet ->
                optionSet.options.any { option -> option?.optionchecked == true }
            }
        }

        assertTrue("Should pass validation when no required options exist", areRequiredOptionsSelected)
    }

    @Test
    fun `test validation fails when condition 1 option set has no selection`() {
        val optionSets = listOf(
            OptionSet(
                id = 1,
                name = "<PERSON><PERSON>",
                condition = 1, // Required
                options = mutableListOf(
                    Option(id = 1, name = "Small", optionchecked = false),
                    Option(id = 2, name = "Medium", optionchecked = false),
                    Option(id = 3, name = "Large", optionchecked = false)
                )
            )
        )

        val condition1OptionSets = optionSets.filter { it.condition == 1 }
        val areRequiredOptionsSelected = if (condition1OptionSets.isEmpty()) {
            true
        } else {
            condition1OptionSets.all { optionSet ->
                optionSet.options.any { option -> option?.optionchecked == true }
            }
        }

        assertFalse("Should fail validation when required option is not selected", areRequiredOptionsSelected)
    }

    @Test
    fun `test validation passes when condition 1 option set has selection`() {
        val optionSets = listOf(
            OptionSet(
                id = 1,
                name = "Size",
                condition = 1, // Required
                options = mutableListOf(
                    Option(id = 1, name = "Small", optionchecked = false),
                    Option(id = 2, name = "Medium", optionchecked = true), // Selected
                    Option(id = 3, name = "Large", optionchecked = false)
                )
            )
        )

        val condition1OptionSets = optionSets.filter { it.condition == 1 }
        val areRequiredOptionsSelected = if (condition1OptionSets.isEmpty()) {
            true
        } else {
            condition1OptionSets.all { optionSet ->
                optionSet.options.any { option -> option?.optionchecked == true }
            }
        }

        assertTrue("Should pass validation when required option is selected", areRequiredOptionsSelected)
    }

    @Test
    fun `test validation with multiple condition 1 option sets`() {
        val optionSets = listOf(
            OptionSet(
                id = 1,
                name = "Size",
                condition = 1, // Required
                options = mutableListOf(
                    Option(id = 1, name = "Small", optionchecked = false),
                    Option(id = 2, name = "Medium", optionchecked = true), // Selected
                    Option(id = 3, name = "Large", optionchecked = false)
                )
            ),
            OptionSet(
                id = 2,
                name = "Crust",
                condition = 1, // Required
                options = mutableListOf(
                    Option(id = 4, name = "Thin", optionchecked = false),
                    Option(id = 5, name = "Thick", optionchecked = false) // None selected
                )
            )
        )

        val condition1OptionSets = optionSets.filter { it.condition == 1 }
        val areRequiredOptionsSelected = if (condition1OptionSets.isEmpty()) {
            true
        } else {
            condition1OptionSets.all { optionSet ->
                optionSet.options.any { option -> option?.optionchecked == true }
            }
        }

        assertFalse("Should fail validation when one of multiple required options is not selected", areRequiredOptionsSelected)
    }

    @Test
    fun `test validation passes with multiple condition 1 option sets all selected`() {
        val optionSets = listOf(
            OptionSet(
                id = 1,
                name = "Size",
                condition = 1, // Required
                options = mutableListOf(
                    Option(id = 1, name = "Small", optionchecked = false),
                    Option(id = 2, name = "Medium", optionchecked = true), // Selected
                    Option(id = 3, name = "Large", optionchecked = false)
                )
            ),
            OptionSet(
                id = 2,
                name = "Crust",
                condition = 1, // Required
                options = mutableListOf(
                    Option(id = 4, name = "Thin", optionchecked = true), // Selected
                    Option(id = 5, name = "Thick", optionchecked = false)
                )
            )
        )

        val condition1OptionSets = optionSets.filter { it.condition == 1 }
        val areRequiredOptionsSelected = if (condition1OptionSets.isEmpty()) {
            true
        } else {
            condition1OptionSets.all { optionSet ->
                optionSet.options.any { option -> option?.optionchecked == true }
            }
        }

        assertTrue("Should pass validation when all required options are selected", areRequiredOptionsSelected)
    }

    @Test
    fun `test unselected required option sets identification`() {
        val optionSets = listOf(
            OptionSet(
                id = 1,
                name = "Size",
                condition = 1, // Required
                options = mutableListOf(
                    Option(id = 1, name = "Small", optionchecked = true) // Selected
                )
            ),
            OptionSet(
                id = 2,
                name = "Crust",
                condition = 1, // Required
                options = mutableListOf(
                    Option(id = 4, name = "Thin", optionchecked = false), // Not selected
                    Option(id = 5, name = "Thick", optionchecked = false) // Not selected
                )
            ),
            OptionSet(
                id = 3,
                name = "Extras",
                condition = 0, // Optional
                options = mutableListOf(
                    Option(id = 6, name = "Extra Cheese", optionchecked = false)
                )
            )
        )

        val unselectedRequiredOptionSets = optionSets.filter { optionSet ->
            optionSet.condition == 1 && optionSet.options.none { option -> option?.optionchecked == true }
        }

        assertEquals("Should identify exactly one unselected required option set", 1, unselectedRequiredOptionSets.size)
        assertEquals("Should identify the Crust option set as unselected", "Crust", unselectedRequiredOptionSets.first().name)
    }
}
