package com.thedasagroup.suminative.ui.products

import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.ui.areas.AreaTableSelectionHelper
import org.junit.Test
import org.junit.Assert.*

/**
 * Test class for auto-selection of newly added courses
 */
class NewCourseAutoSelectionTest {

    @Test
    fun `test addNewCourse auto-selects for table-based courses`() {
        // Given - table with existing courses
        val tableSelection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )

        val existingCourses = listOf(MealCourse(name = "Course 1"))
        val initialState = ProductsScreenState(
            selectedTables = listOf(tableSelection),
            selectedTableIndex = 0,
            tableAvailableCourses = mapOf(1 to existingCourses),
            tableSelectedCourseForNewItems = mapOf(1 to "Course 1")
        )

        // When - simulating addNewCourse logic
        val newCourseName = "Custom Course"
        val updatedCourses = existingCourses.toMutableList().apply {
            add(MealCourse(name = newCourseName))
        }

        val updatedState = initialState.copy(
            tableAvailableCourses = mapOf(1 to updatedCourses),
            tableSelectedCourseForNewItems = mapOf(1 to newCourseName) // Should auto-select new course
        )

        // Then - new course should be auto-selected
        assertEquals("New course should be auto-selected", newCourseName, updatedState.tableSelectedCourseForNewItems[1])
        assertEquals("Should have 2 courses", 2, updatedState.tableAvailableCourses[1]?.size)
        assertEquals("New course should be in list", newCourseName, updatedState.tableAvailableCourses[1]?.last()?.name)
    }

    @Test
    fun `test addNewCourse auto-selects for global courses`() {
        // Given - global courses (no table selected)
        val existingCourses = listOf(MealCourse(name = "Course 1"))
        val initialState = ProductsScreenState(
            selectedTables = emptyList(),
            selectedTableIndex = -1,
            availableCourses = existingCourses,
            selectedCourseForNewItems = "Course 1"
        )

        // When - simulating addNewCourse logic
        val newCourseName = "Custom Course"
        val updatedCourses = existingCourses.toMutableList().apply {
            add(MealCourse(name = newCourseName))
        }

        val updatedState = initialState.copy(
            availableCourses = updatedCourses,
            selectedCourseForNewItems = newCourseName // Should auto-select new course
        )

        // Then - new course should be auto-selected
        assertEquals("New course should be auto-selected", newCourseName, updatedState.selectedCourseForNewItems)
        assertEquals("Should have 2 courses", 2, updatedState.availableCourses.size)
        assertEquals("New course should be in list", newCourseName, updatedState.availableCourses.last().name)
    }

    @Test
    fun `test addNumberedCourse auto-selects for table-based courses`() {
        // Given - table with existing courses
        val tableSelection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )

        val existingCourses = listOf(MealCourse(name = "Course 1"))
        val initialState = ProductsScreenState(
            selectedTables = listOf(tableSelection),
            selectedTableIndex = 0,
            tableAvailableCourses = mapOf(1 to existingCourses),
            tableSelectedCourseForNewItems = mapOf(1 to "Course 1")
        )

        // When - simulating addNumberedCourse logic
        val newCourseName = "Course 2" // Next numbered course
        val updatedCourses = existingCourses.toMutableList().apply {
            add(MealCourse(name = newCourseName))
        }

        val updatedState = initialState.copy(
            tableAvailableCourses = mapOf(1 to updatedCourses),
            tableSelectedCourseForNewItems = mapOf(1 to newCourseName) // Should auto-select new course
        )

        // Then - new numbered course should be auto-selected
        assertEquals("New numbered course should be auto-selected", newCourseName, updatedState.tableSelectedCourseForNewItems[1])
        assertEquals("Should have 2 courses", 2, updatedState.tableAvailableCourses[1]?.size)
        assertEquals("New course should be Course 2", "Course 2", updatedState.tableAvailableCourses[1]?.last()?.name)
    }

    @Test
    fun `test addNumberedCourse auto-selects for global courses`() {
        // Given - global courses with existing course
        val existingCourses = listOf(MealCourse(name = "Course 1"))
        val initialState = ProductsScreenState(
            selectedTables = emptyList(),
            selectedTableIndex = -1,
            availableCourses = existingCourses,
            selectedCourseForNewItems = "Course 1"
        )

        // When - simulating addNumberedCourse logic
        val newCourseName = "Course 2"
        val updatedCourses = existingCourses.toMutableList().apply {
            add(MealCourse(name = newCourseName))
        }

        val updatedState = initialState.copy(
            availableCourses = updatedCourses,
            selectedCourseForNewItems = newCourseName // Should auto-select new course
        )

        // Then - new numbered course should be auto-selected
        assertEquals("New numbered course should be auto-selected", newCourseName, updatedState.selectedCourseForNewItems)
        assertEquals("Should have 2 courses", 2, updatedState.availableCourses.size)
        assertEquals("New course should be Course 2", "Course 2", updatedState.availableCourses.last().name)
    }

    @Test
    fun `test addDefaultCourses auto-selects first new course for table`() {
        // Given - table with no courses
        val tableSelection = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1,
            areaId = 1,
            tableName = "Table 1",
            areaName = "Main Area"
        )

        val initialState = ProductsScreenState(
            selectedTables = listOf(tableSelection),
            selectedTableIndex = 0,
            tableAvailableCourses = mapOf(1 to emptyList()),
            tableSelectedCourseForNewItems = mapOf(1 to "")
        )

        // When - simulating addDefaultCourses logic
        val defaultCourses = listOf(
            MealCourse(name = "Starters"),
            MealCourse(name = "Mains"),
            MealCourse(name = "Desserts")
        )

        val updatedState = initialState.copy(
            tableAvailableCourses = mapOf(1 to defaultCourses),
            tableSelectedCourseForNewItems = mapOf(1 to "Starters") // Should auto-select first new course
        )

        // Then - first default course should be auto-selected
        assertEquals("First default course should be auto-selected", "Starters", updatedState.tableSelectedCourseForNewItems[1])
        assertEquals("Should have 3 default courses", 3, updatedState.tableAvailableCourses[1]?.size)
        assertEquals("First course should be Starters", "Starters", updatedState.tableAvailableCourses[1]?.first()?.name)
    }

    @Test
    fun `test addDefaultCourses auto-selects first new course for global`() {
        // Given - no global courses
        val initialState = ProductsScreenState(
            selectedTables = emptyList(),
            selectedTableIndex = -1,
            availableCourses = emptyList(),
            selectedCourseForNewItems = ""
        )

        // When - simulating addDefaultCourses logic
        val defaultCourses = listOf(
            MealCourse(name = "Starters"),
            MealCourse(name = "Mains"),
            MealCourse(name = "Desserts")
        )

        val updatedState = initialState.copy(
            availableCourses = defaultCourses,
            selectedCourseForNewItems = "Starters" // Should auto-select first new course
        )

        // Then - first default course should be auto-selected
        assertEquals("First default course should be auto-selected", "Starters", updatedState.selectedCourseForNewItems)
        assertEquals("Should have 3 default courses", 3, updatedState.availableCourses.size)
        assertEquals("First course should be Starters", "Starters", updatedState.availableCourses.first().name)
    }

    @Test
    fun `test multiple tables with independent course selection`() {
        // Given - multiple tables
        val table1 = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 1, areaId = 1, tableName = "Table 1", areaName = "Area 1"
        )
        val table2 = AreaTableSelectionHelper.AreaTableSelection(
            tableId = 2, areaId = 1, tableName = "Table 2", areaName = "Area 1"
        )

        val initialState = ProductsScreenState(
            selectedTables = listOf(table1, table2),
            selectedTableIndex = 0, // Table 1 selected
            tableAvailableCourses = mapOf(
                1 to listOf(MealCourse(name = "Course 1")),
                2 to listOf(MealCourse(name = "Course A"))
            ),
            tableSelectedCourseForNewItems = mapOf(
                1 to "Course 1",
                2 to "Course A"
            )
        )

        // When - adding new course to Table 1 (currently selected)
        val newCourseForTable1 = "Custom Course"
        val updatedCoursesTable1 = initialState.tableAvailableCourses[1]!!.toMutableList().apply {
            add(MealCourse(name = newCourseForTable1))
        }

        val updatedState = initialState.copy(
            tableAvailableCourses = mapOf(
                1 to updatedCoursesTable1,
                2 to initialState.tableAvailableCourses[2]!! // Table 2 unchanged
            ),
            tableSelectedCourseForNewItems = mapOf(
                1 to newCourseForTable1, // Table 1 should select new course
                2 to "Course A" // Table 2 unchanged
            )
        )

        // Then - only Table 1 should have new course selected
        assertEquals("Table 1 should select new course", newCourseForTable1, updatedState.tableSelectedCourseForNewItems[1])
        assertEquals("Table 2 should keep original selection", "Course A", updatedState.tableSelectedCourseForNewItems[2])
        assertEquals("Table 1 should have 2 courses", 2, updatedState.tableAvailableCourses[1]?.size)
        assertEquals("Table 2 should have 1 course", 1, updatedState.tableAvailableCourses[2]?.size)
    }
}
