package com.thedasagroup.suminative.ui.reservations

import com.thedasagroup.suminative.data.model.response.reservations.Area
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for area auto-selection functionality
 */
class AreaTableSelectionTest {

    @Test
    fun `test should auto-select when only one area available`() {
        // Create a single area
        val singleArea = Area(
            id = 1,
            description = "Main Dining Area",
            storeId = 158
        )
        
        val areas = listOf(singleArea)
        
        // Verify that there's only one area
        assertEquals("Should have exactly one area", 1, areas.size)
        
        // Verify the area details
        assertEquals("Area ID should be 1", 1, areas.first().id)
        assertEquals("Area name should be Main Dining Area", "Main Dining Area", areas.first().description)
    }

    @Test
    fun `test should not auto-select when multiple areas available`() {
        // Create multiple areas
        val area1 = Area(
            id = 1,
            description = "Main Dining Area",
            storeId = 158
        )
        
        val area2 = Area(
            id = 2,
            description = "Outdoor Seating",
            storeId = 158
        )
        
        val areas = listOf(area1, area2)
        
        // Verify that there are multiple areas
        assertEquals("Should have exactly two areas", 2, areas.size)
        assertTrue("Should have more than one area", areas.size > 1)
    }

    @Test
    fun `test should handle empty areas list`() {
        val areas = emptyList<Area>()
        
        // Verify empty list
        assertEquals("Should have no areas", 0, areas.size)
        assertTrue("Areas list should be empty", areas.isEmpty())
    }

    @Test
    fun `test area properties are correctly set`() {
        val area = Area(
            id = 5,
            description = "VIP Section",
            storeId = 158
        )

        // Verify all properties
        assertEquals("Area ID should be 5", 5, area.id)
        assertEquals("Area description should be VIP Section", "VIP Section", area.description)
        assertEquals("Store ID should be 158", 158, area.storeId)
    }

    @Test
    fun `test table filtering excludes selected tables`() {
        // Create sample tables
        val table1 = com.thedasagroup.suminative.data.model.response.reservations.Table(
            id = 1,
            tableName = "Table 1",
            seatingCapacity = 4
        )

        val table2 = com.thedasagroup.suminative.data.model.response.reservations.Table(
            id = 2,
            tableName = "Table 2",
            seatingCapacity = 6
        )

        val table3 = com.thedasagroup.suminative.data.model.response.reservations.Table(
            id = 3,
            tableName = "Table 3",
            seatingCapacity = 2
        )

        val allTables = listOf(table1, table2, table3)
        val excludedTableIds = listOf(1, 3) // Exclude tables 1 and 3

        // Filter out excluded tables
        val availableTables = allTables.filter { table ->
            table.id !in excludedTableIds
        }

        // Verify filtering
        assertEquals("Should have 1 available table", 1, availableTables.size)
        assertEquals("Available table should be Table 2", "Table 2", availableTables.first().tableName)
        assertEquals("Available table ID should be 2", 2, availableTables.first().id)
    }

    @Test
    fun `test all tables excluded returns empty list`() {
        val table1 = com.thedasagroup.suminative.data.model.response.reservations.Table(
            id = 1,
            tableName = "Table 1",
            seatingCapacity = 4
        )

        val table2 = com.thedasagroup.suminative.data.model.response.reservations.Table(
            id = 2,
            tableName = "Table 2",
            seatingCapacity = 6
        )

        val allTables = listOf(table1, table2)
        val excludedTableIds = listOf(1, 2) // Exclude all tables

        val availableTables = allTables.filter { table ->
            table.id !in excludedTableIds
        }

        assertTrue("Should have no available tables", availableTables.isEmpty())
    }

    @Test
    fun `test no excluded tables returns all tables`() {
        val table1 = com.thedasagroup.suminative.data.model.response.reservations.Table(
            id = 1,
            tableName = "Table 1",
            seatingCapacity = 4
        )

        val table2 = com.thedasagroup.suminative.data.model.response.reservations.Table(
            id = 2,
            tableName = "Table 2",
            seatingCapacity = 6
        )

        val allTables = listOf(table1, table2)
        val excludedTableIds = emptyList<Int>() // No exclusions

        val availableTables = allTables.filter { table ->
            table.id !in excludedTableIds
        }

        assertEquals("Should have all tables available", 2, availableTables.size)
        assertEquals("Should contain Table 1", "Table 1", availableTables[0].tableName)
        assertEquals("Should contain Table 2", "Table 2", availableTables[1].tableName)
    }

    @Test
    fun `test auto-selection logic conditions`() {
        // Test case 1: Single area (should auto-select)
        val singleAreaList = listOf(
            Area(id = 1, description = "Main Area", storeId = 158)
        )
        assertTrue("Single area should trigger auto-selection", singleAreaList.size == 1)

        // Test case 2: Multiple areas (should not auto-select)
        val multipleAreasList = listOf(
            Area(id = 1, description = "Main Area", storeId = 158),
            Area(id = 2, description = "Patio", storeId = 158)
        )
        assertFalse("Multiple areas should not trigger auto-selection", multipleAreasList.size == 1)

        // Test case 3: Empty list (should not auto-select)
        val emptyAreasList = emptyList<Area>()
        assertFalse("Empty list should not trigger auto-selection", emptyAreasList.size == 1)
    }
}
