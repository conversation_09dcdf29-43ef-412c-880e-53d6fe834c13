package com.thedasagroup.suminative.domain.table_sync

import com.thedasagroup.suminative.ui.products.CourseStatusQueue
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * Unit tests for CreateOrUpdateOrderForTableUseCase
 * Specifically testing course status mapping logic
 */
class CreateOrUpdateOrderForTableUseCaseTest {

    @Test
    fun `test course status queue logic`() {
        // Test the core logic that our fix addresses
        val courseStatusQueue = CourseStatusQueue(
            goQueue = listOf("Starters"),
            preparingQueue = listOf("Mains"),
            completedCourses = listOf("Desserts")
        )

        // Test GO status
        assertTrue(courseStatusQueue.isInGoQueue("Starters"))
        assertFalse(courseStatusQueue.isInGoQueue("Mains"))
        assertFalse(courseStatusQueue.isInGoQueue("Desserts"))

        // Test PREPARING status
        assertFalse(courseStatusQueue.isInPreparingQueue("Starters"))
        assertTrue(courseStatusQueue.isInPreparingQueue("Mains"))
        assertFalse(courseStatusQueue.isInPreparingQueue("Desserts"))

        // Test COMPLETED status
        assertFalse(courseStatusQueue.isCompleted("Starters"))
        assertFalse(courseStatusQueue.isCompleted("Mains"))
        assertTrue(courseStatusQueue.isCompleted("Desserts"))
    }

    @Test
    fun `test course status mapping logic`() {
        val courseStatusQueue = CourseStatusQueue(
            goQueue = listOf("Starters"),
            preparingQueue = listOf("Mains"),
            completedCourses = listOf("Desserts")
        )

        // Test the mapping logic that was fixed
        val startersStatus = when {
            courseStatusQueue.isInGoQueue("Starters") -> 0
            courseStatusQueue.isInPreparingQueue("Starters") -> 1
            courseStatusQueue.isCompleted("Starters") -> 2
            else -> 0
        }
        assertEquals(0, startersStatus, "Starters should map to status 0 (GO)")

        val mainsStatus = when {
            courseStatusQueue.isInGoQueue("Mains") -> 0
            courseStatusQueue.isInPreparingQueue("Mains") -> 1
            courseStatusQueue.isCompleted("Mains") -> 2
            else -> 0
        }
        assertEquals(1, mainsStatus, "Mains should map to status 1 (PREPARING)")

        val dessertsStatus = when {
            courseStatusQueue.isInGoQueue("Desserts") -> 0
            courseStatusQueue.isInPreparingQueue("Desserts") -> 1
            courseStatusQueue.isCompleted("Desserts") -> 2
            else -> 0
        }
        assertEquals(2, dessertsStatus, "Desserts should map to status 2 (COMPLETE)")

        // Test unknown course defaults to 0
        val unknownStatus = when {
            courseStatusQueue.isInGoQueue("Unknown") -> 0
            courseStatusQueue.isInPreparingQueue("Unknown") -> 1
            courseStatusQueue.isCompleted("Unknown") -> 2
            else -> 0
        }
        assertEquals(0, unknownStatus, "Unknown course should default to status 0")
    }
}
