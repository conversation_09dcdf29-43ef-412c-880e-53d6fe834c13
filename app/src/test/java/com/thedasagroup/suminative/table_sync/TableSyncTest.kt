package com.thedasagroup.suminative.table_sync

import com.thedasagroup.suminative.data.model.request.table_sync.OrderCourse
import com.thedasagroup.suminative.data.model.request.table_sync.SyncOrderRequest
import com.thedasagroup.suminative.data.model.request.table_sync.UpdateOrderRequest
import com.thedasagroup.suminative.data.model.response.table_sync.SyncOrderData
import com.thedasagroup.suminative.data.model.response.table_sync.SyncOrderResponse
import com.thedasagroup.suminative.data.model.response.table_sync.SyncOrderCourse
import com.thedasagroup.suminative.data.model.response.table_sync.TableResponse
import kotlinx.serialization.json.Json
import org.junit.Test
import org.junit.Assert.*

/**
 * Unit tests for Table Sync data models
 * Tests serialization/deserialization of request and response models
 */
class TableSyncTest {

    private val json = Json {
        ignoreUnknownKeys = true
        isLenient = true
    }

    @Test
    fun `test SyncOrderRequest serialization`() {
        // Create test request
        val orderCourses = listOf(
            OrderCourse(
                coursesName = "Starters",
                cartJson = "{\"items\":[{\"id\":101,\"name\":\"Soup\",\"qty\":1,\"price\":5.5}]}"
            ),
            OrderCourse(
                coursesName = "Mains",
                cartJson = "{\"items\":[{\"id\":202,\"name\":\"Burger\",\"qty\":2,\"price\":12.0}]}"
            )
        )

        val request = SyncOrderRequest(
            tableId = 47,
            customerId = 345,
            businessId = 104,
            netPayable = 47.5,
            orderCourses = orderCourses
        )

        // Serialize to JSON
        val jsonString = json.encodeToString(SyncOrderRequest.serializer(), request)
        
        // Verify JSON contains expected fields
        assertTrue(jsonString.contains("\"tableId\":47"))
        assertTrue(jsonString.contains("\"customerId\":345"))
        assertTrue(jsonString.contains("\"businessId\":104"))
        assertTrue(jsonString.contains("\"netPayable\":47.5"))
        assertTrue(jsonString.contains("\"coursesName\":\"Starters\""))
        assertTrue(jsonString.contains("\"coursesName\":\"Mains\""))
    }

    @Test
    fun `test SyncOrderResponse deserialization`() {
        // Sample response JSON (matching the API response structure)
        val responseJson = """
        {
            "success": true,
            "message": "Order saved for table 47",
            "data": {
                "id": 7,
                "tableId": 47,
                "orderCourses": [
                    {
                        "id": 13,
                        "coursesName": "Starters",
                        "cartJson": "{\"items\":[{\"id\":101,\"name\":\"Soup\",\"qty\":1,\"price\":5.5}]}"
                    },
                    {
                        "id": 14,
                        "coursesName": "Mains",
                        "cartJson": "{\"items\":[{\"id\":202,\"name\":\"Burger\",\"qty\":2,\"price\":12.0}]}"
                    }
                ],
                "netPayable": 47.5,
                "customerId": 345,
                "businessId": 104
            },
            "statusCode": 200
        }
        """.trimIndent()

        // Deserialize from JSON
        val response = json.decodeFromString(SyncOrderResponse.serializer(), responseJson)

        // Verify response fields
        assertTrue(response.success)
        assertEquals("Order saved for table 47", response.message)
        assertEquals(200, response.statusCode)
        
        // Verify data fields
        assertNotNull(response.data)
        assertEquals(7, response.data?.id)
        assertEquals(47, response.data?.tableId)
        assertEquals(47.5, response.data?.netPayable, 0.01)
        assertEquals(345, response.data?.customerId)
        assertEquals(104, response.data?.businessId)
        
        // Verify order courses
        assertEquals(2, response.data?.orderCourses?.size)
        
        val startersCourse = response.data?.orderCourses?.find { it.coursesName == "Starters" }
        assertNotNull(startersCourse)
        assertEquals(13, startersCourse?.id)
        assertTrue(startersCourse?.cartJson?.contains("Soup") == true)
        
        val mainsCourse = response.data?.orderCourses?.find { it.coursesName == "Mains" }
        assertNotNull(mainsCourse)
        assertEquals(14, mainsCourse?.id)
        assertTrue(mainsCourse?.cartJson?.contains("Burger") == true)
    }

    @Test
    fun `test OrderCourse serialization`() {
        val orderCourse = OrderCourse(
            coursesName = "Desserts",
            cartJson = "{\"items\":[{\"id\":301,\"name\":\"Ice Cream\",\"qty\":1,\"price\":8.0}]}"
        )

        val jsonString = json.encodeToString(OrderCourse.serializer(), orderCourse)
        
        assertTrue(jsonString.contains("\"coursesName\":\"Desserts\""))
        assertTrue(jsonString.contains("\"cartJson\":\"{"))
        assertTrue(jsonString.contains("Ice Cream"))
    }

    @Test
    fun `test SyncOrderData with empty order courses`() {
        val syncOrderData = SyncOrderData(
            id = 1,
            tableId = 10,
            orderCourses = emptyList(),
            netPayable = 0.0,
            customerId = 100,
            businessId = 200
        )

        val jsonString = json.encodeToString(SyncOrderData.serializer(), syncOrderData)
        val deserializedData = json.decodeFromString(SyncOrderData.serializer(), jsonString)

        assertEquals(syncOrderData.id, deserializedData.id)
        assertEquals(syncOrderData.tableId, deserializedData.tableId)
        assertEquals(0, deserializedData.orderCourses.size)
        assertEquals(syncOrderData.netPayable, deserializedData.netPayable, 0.01)
    }

    @Test
    fun `test UpdateOrderRequest serialization`() {
        // Create test update request
        val orderCourses = listOf(
            OrderCourse(
                coursesName = "Starters",
                cartJson = "{\"items\":[{\"id\":101,\"name\":\"Soup\",\"qty\":1,\"price\":5.5}]}"
            ),
            OrderCourse(
                coursesName = "Mains",
                cartJson = "{\"items\":[{\"id\":202,\"name\":\"Burger\",\"qty\":2,\"price\":12.0}]}"
            )
        )

        val updateRequest = UpdateOrderRequest(
            tableId = 12,
            customerId = 345,
            businessId = 104,
            netPayable = 47.5,
            orderCourses = orderCourses
        )

        // Serialize to JSON
        val jsonString = json.encodeToString(UpdateOrderRequest.serializer(), updateRequest)

        // Verify JSON contains expected fields
        assertTrue(jsonString.contains("\"tableId\":12"))
        assertTrue(jsonString.contains("\"customerId\":345"))
        assertTrue(jsonString.contains("\"businessId\":104"))
        assertTrue(jsonString.contains("\"netPayable\":47.5"))
        assertTrue(jsonString.contains("\"coursesName\":\"Starters\""))
        assertTrue(jsonString.contains("\"coursesName\":\"Mains\""))
    }

    @Test
    fun `test TableResponse deserialization`() {
        // Sample table response JSON (matching the API response structure)
        val responseJson = """
        {
            "id": 47,
            "storeId": 158,
            "areaId": 10,
            "tableName": "TAKE-OUT1 ",
            "seatingCapacity": 1,
            "tableDetailsJson": "{\"shape\":\"SQUARE\",\"color\":\"#2ECC71\",\"position\":{\"row\":1,\"col\":3}}",
            "occupied": false,
            "reserved": false,
            "netPayable": 0.0
        }
        """.trimIndent()

        // Deserialize from JSON
        val response = json.decodeFromString(TableResponse.serializer(), responseJson)

        // Verify response fields
        assertEquals(47, response.id)
        assertEquals(158, response.storeId)
        assertEquals(10, response.areaId)
        assertEquals("TAKE-OUT1 ", response.tableName)
        assertEquals(1, response.seatingCapacity)
        assertFalse(response.occupied)
        assertFalse(response.reserved)
        assertEquals(0.0, response.netPayable, 0.01)
        assertTrue(response.tableDetailsJson.contains("SQUARE"))
        assertTrue(response.tableDetailsJson.contains("#2ECC71"))
    }

    @Test
    fun `test SyncOrderResponse for delete operation`() {
        // Sample delete response JSON (matching the API response structure)
        val responseJson = """
        {
            "success": false,
            "message": "Order couldn't be deleted for table 12",
            "data": null,
            "statusCode": 500
        }
        """.trimIndent()

        // Deserialize from JSON
        val response = json.decodeFromString(SyncOrderResponse.serializer(), responseJson)

        // Verify response fields
        assertFalse(response.success)
        assertEquals("Order couldn't be deleted for table 12", response.message)
        assertNull(response.data)
        assertEquals(500, response.statusCode)
    }

    @Test
    fun `test UpdateOrderRequest with empty order courses`() {
        val updateRequest = UpdateOrderRequest(
            tableId = 1,
            customerId = 100,
            businessId = 200,
            netPayable = 0.0,
            orderCourses = emptyList()
        )

        val jsonString = json.encodeToString(UpdateOrderRequest.serializer(), updateRequest)
        val deserializedRequest = json.decodeFromString(UpdateOrderRequest.serializer(), jsonString)

        assertEquals(updateRequest.tableId, deserializedRequest.tableId)
        assertEquals(updateRequest.customerId, deserializedRequest.customerId)
        assertEquals(updateRequest.businessId, deserializedRequest.businessId)
        assertEquals(updateRequest.netPayable, deserializedRequest.netPayable, 0.01)
        assertEquals(0, deserializedRequest.orderCourses.size)
    }
}
