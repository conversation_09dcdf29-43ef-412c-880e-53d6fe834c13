package com.thedasagroup.suminative.ui.user_profile

import android.content.Intent
import android.os.Build
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.runtime.getValue
import com.afollestad.materialdialogs.MaterialDialog
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.compose.collectAsState
import com.airbnb.mvrx.viewModel
import com.thedasagroup.suminative.ui.MainActivity
import com.thedasagroup.suminative.ui.common.CommonState
import com.thedasagroup.suminative.ui.common.CommonViewModel
import com.thedasagroup.suminative.ui.common.SuccessDialog
import com.thedasagroup.suminative.ui.service.Actions
import com.thedasagroup.suminative.ui.service.EndlessSocketService
import com.thedasagroup.suminative.ui.service.ServiceState
import com.thedasagroup.suminative.ui.service.getServiceState
import com.thedasagroup.suminative.ui.service.log
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme

class SelectUserProfileActivity : AppCompatActivity(), MavericksView {

    private val selectUserProfileViewModel: SelectUserProfileViewModel by viewModel()
    private val commonViewModel : CommonViewModel by viewModel()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            SumiNativeTheme {
                val showSuccessDialog by commonViewModel.collectAsState(CommonState::showSuccessDialog)
                val successDialogMessage by commonViewModel.collectAsState(CommonState::successDialogMessage)
                SelectUserProfileScreen(
                    viewModel = selectUserProfileViewModel,
                    onLoginSuccess = {response ->
                        commonViewModel.updateShowSuccessDialog(true)
                        commonViewModel.updateSuccessDialogMessage(message = response.message ?: "Login Success" )
                    },
                    onLoginError = { error ->
                        MaterialDialog(this@SelectUserProfileActivity).show {
                            message(text = error)
                            positiveButton(text = "Ok") {
                                it.dismiss()
                            }
                        }
                    },
                    onBackClick = {
                        finish()
                    }
                )
                SuccessDialog(
                    isVisible = showSuccessDialog,
                    message = successDialogMessage,
                    onDismiss = {
                        commonViewModel.updateShowSuccessDialog(false)
                        val intent = Intent(this, MainActivity::class.java)
                        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
                        startActivity(intent)
                    }
                )
            }
        }

//        // Check if user is already logged in and has a store selected
//        if (selectUserProfileViewModel.prefs.loginResponse?.user?.id != null) {
//            if (selectUserProfileViewModel.prefs.store != null) {
//                val intent = Intent(this, MainActivity::class.java)
//                intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
//                startActivity(intent)
//                scheduleJob()
//            }
//        } else {
//            // If no login response, go back to login
//            val intent = Intent(this, LoginActivity::class.java)
//            intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
//            startActivity(intent)
//        }
//
//        // Check if store is not selected, this screen should only be shown when store is selected
//        if (selectUserProfileViewModel.prefs.store == null) {
//            finish()
//        }
    }

    override fun invalidate() {
        // Required by MavericksView
    }

    private fun scheduleJob() {
//        actionOnService(Actions.START)
    }

    private fun actionOnService(action: Actions) {
        if (getServiceState(this) == ServiceState.STOPPED && action == Actions.STOP) return
        Intent(this, EndlessSocketService::class.java).also {
            it.action = action.name
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                log("Starting the service in >=26 Mode")
                startForegroundService(it)
                return
            }
            log("Starting the service in < 26 Mode")
            startService(it)
        }
    }
} 