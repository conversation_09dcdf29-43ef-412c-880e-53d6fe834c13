package com.thedasagroup.suminative.ui.payment

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Backspace
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.thedasagroup.suminative.ButtonColorDarkGreen


@Composable
fun NumberPad(
    onNumberClick: (String) -> Unit,
    onDecimalClick: () -> Unit,
    onBackspaceClick: () -> Unit,
    onConfirmClick: () -> Unit,
    isConfirmEnabled: Boolean
) {
    // Center the entire keypad layout
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // Column 1: Number pad
        Column(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Row 1: 1, 2, 3
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                NumberButton(
                    number = "1",
                    letters = "",
                    onClick = { onNumberClick("1") }
                )
                NumberButton(
                    number = "2",
                    letters = "",
                    onClick = { onNumberClick("2") }
                )
                NumberButton(
                    number = "3",
                    letters = "",
                    onClick = { onNumberClick("3") }
                )
            }

            // Row 2: 4, 5, 6
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                NumberButton(
                    number = "4",
                    letters = "",
                    onClick = { onNumberClick("4") }
                )
                NumberButton(
                    number = "5",
                    letters = "",
                    onClick = { onNumberClick("5") }
                )
                NumberButton(
                    number = "6",
                    letters = "",
                    onClick = { onNumberClick("6") }
                )
            }

            // Row 3: 7, 8, 9
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                NumberButton(
                    number = "7",
                    letters = "",
                    onClick = { onNumberClick("7") }
                )
                NumberButton(
                    number = "8",
                    letters = "",
                    onClick = { onNumberClick("8") }
                )
                NumberButton(
                    number = "9",
                    letters = "",
                    onClick = { onNumberClick("9") }
                )
            }

            // Row 4: Decimal, 0, Backspace
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Decimal button
                NumberButton(
                    number = ".",
                    letters = "",
                    onClick = { onDecimalClick() }
                )

                NumberButton(
                    number = "0",
                    letters = "",
                    onClick = { onNumberClick("0") }
                )

                // Backspace button
                Box(
                    modifier = Modifier
                        .size(width = 120.dp, height = 120.dp)
                        .background(
                            Color(ButtonColorDarkGreen),
                            RoundedCornerShape(8.dp)
                        )
                        .border(
                            1.dp,
                            Color.Gray.copy(alpha = 0.2f),
                            RoundedCornerShape(8.dp)
                        )
                        .clickable { onBackspaceClick() },
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Backspace,
                        contentDescription = "Backspace",
                        tint = Color.White,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun NumberButton(
    number: String,
    letters: String,
    onClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .size(width = 120.dp, height = 120.dp)
            .background(
                Color(ButtonColorDarkGreen),
                RoundedCornerShape(8.dp)
            )
            .border(
                1.dp,
                Color.Gray.copy(alpha = 0.2f),
                RoundedCornerShape(8.dp)
            )
            .clickable { onClick() },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = number,
            fontSize = 40.sp,
            fontWeight = FontWeight.Bold,
            color = Color.White,
            textAlign = TextAlign.Center
        )
    }
}