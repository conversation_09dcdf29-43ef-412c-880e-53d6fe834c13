//package com.thedasagroup.suminative.ui.products
//
//import androidx.compose.foundation.ExperimentalFoundationApi
//import androidx.compose.foundation.background
//import androidx.compose.foundation.border
//import androidx.compose.foundation.layout.Arrangement
//import androidx.compose.foundation.layout.Column
//import androidx.compose.foundation.layout.Row
//import androidx.compose.foundation.layout.Spacer
//import androidx.compose.foundation.layout.fillMaxSize
//import androidx.compose.foundation.layout.fillMaxWidth
//import androidx.compose.foundation.layout.height
//import androidx.compose.foundation.layout.padding
//import androidx.compose.foundation.layout.size
//import androidx.compose.foundation.layout.width
//import androidx.compose.foundation.layout.wrapContentWidth
//import androidx.compose.foundation.lazy.LazyColumn
//import androidx.compose.foundation.lazy.items
//import androidx.compose.foundation.shape.CircleShape
//import androidx.compose.foundation.shape.RoundedCornerShape
//import androidx.compose.material.Card
//import androidx.compose.material.Icon
//import androidx.compose.material.IconButton
//import androidx.compose.material.MaterialTheme
//import androidx.compose.material.Scaffold
//import androidx.compose.material.Text
//import androidx.compose.material.TextButton
//import androidx.compose.material.TextField
//import androidx.compose.material3.CircularProgressIndicator
//import androidx.compose.material3.ExperimentalMaterial3Api
//import androidx.compose.runtime.Composable
//import androidx.compose.runtime.getValue
//import androidx.compose.runtime.mutableStateOf
//import androidx.compose.runtime.remember
//import androidx.compose.runtime.setValue
//import androidx.compose.ui.Alignment
//import androidx.compose.ui.Modifier
//import androidx.compose.ui.graphics.Color
//import androidx.compose.ui.res.painterResource
//import androidx.compose.ui.text.TextStyle
//import androidx.compose.ui.text.font.FontStyle
//import androidx.compose.ui.text.font.FontWeight
//import androidx.compose.ui.text.style.TextAlign
//import androidx.compose.ui.text.style.TextDecoration
//import androidx.compose.ui.unit.dp
//import androidx.compose.ui.unit.sp
//import com.airbnb.mvrx.Loading
//import com.airbnb.mvrx.compose.collectAsState
//import com.thedasagroup.suminative.R
//import com.thedasagroup.suminative.data.model.request.order.Cart
//import com.thedasagroup.suminative.data.model.request.order.Order
//import com.thedasagroup.suminative.data.model.request.order.StoreItem
//import com.thedasagroup.suminative.ui.theme.fontNunito
//import com.thedasagroup.suminative.ui.theme.fontPoppins
//import com.thedasagroup.suminative.ui.utils.transformDecimal
//import ir.kaaveh.sdpcompose.sdp
//import androidx.compose.ui.window.Dialog
//
//
//@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
//@Composable
//fun CartScreenFigma(
//    order: Order,
//    onRemoveItem: (Cart) -> Unit,
//    closeCart: () -> Unit,
//    placeOrderCash: () -> Unit,
//    placeOrderCard: () -> Unit,
//    onUpdateStock: (Int, StoreItem, Cart) -> Unit,
//    onUpdateNotes: (Cart, String) -> Unit,
//    productsScreenViewModel: ProductsScreenViewModel
//) {
//    Scaffold(
//        bottomBar = {
//            OrderTotalSticky(
//                order = order,
//                placeOrderCard = placeOrderCard,
//                placeOrderCash = placeOrderCash,
//                productsScreenViewModel = productsScreenViewModel
//            )
//        }
//    ) { _ ->
//        Card(
//            modifier = Modifier
//                .fillMaxSize()
//                .padding(top = 30.sdp, bottom = 140.dp),
//            shape = RoundedCornerShape(16.dp)
//        ) {
//
//            LazyColumn(
//                modifier = Modifier
//                    .fillMaxSize()
//                    .background(color = Color(0xFFf7f2fa))
//                    .padding(8.dp),
//                horizontalAlignment = Alignment.CenterHorizontally
//            ) {
//                item {
//                    Spacer(modifier = Modifier.height(12.dp))
//                    Row(
//                        modifier = Modifier
//                            .fillMaxWidth()
//                            .padding(end = 2.dp),
//                        horizontalArrangement = Arrangement.SpaceBetween,
//                        verticalAlignment = Alignment.CenterVertically
//                    ) {
//                        Text(
//                            modifier = Modifier.padding(start = 145.dp),
//                            text = "Your Cart",
//                            fontFamily = fontPoppins,
//                            color = Color(0xFF223263),
//                            fontSize = 16.sp,
//                            fontWeight = FontWeight.Bold
//                        )
//                        Spacer(modifier = Modifier.width(8.dp))
//                        IconButton(
//                            modifier = Modifier.padding(5.dp), onClick = {
//                                closeCart()
//                            }) {
//                            Icon(
//                                painter = painterResource(R.drawable.collapse),
//                                contentDescription = "Collapse",
//                                tint = MaterialTheme.colors.primary,
//                                modifier = Modifier.size(24.dp),
//                            )
//                        }
//                    }
//                    Spacer(modifier = Modifier.height(12.dp))
//                }
//                items(order.carts ?: mutableListOf()) { cartItem ->
//                    CartItemCard(
//                        cartItem = cartItem,
//                        onRemoveItem = onRemoveItem,
//                        onUpdateStock = onUpdateStock,
//                        onUpdateNotes = onUpdateNotes
//                    )
//                }
//                item {
//                    CartSummery(order = order)
//                }
//            }
//        }
//    }
//}
//
//@Composable
//fun StockUpdateCounterCart(
//    initialStock: Int = 1, onStockChange: (Int) -> Unit
//) {
//    var stock by remember { mutableStateOf(initialStock) }
//
//    Row(
//        modifier = Modifier
//            .padding(8.dp)
//            .wrapContentWidth()
//            .border(
//                width = 1.dp,
//                color = Color(0xeFFbf0ff),
//                shape = RoundedCornerShape(1.dp)
//            ),
//        verticalAlignment = Alignment.CenterVertically,
//        horizontalArrangement = Arrangement.spacedBy(8.dp)
//    ) {
//        androidx.compose.material3.IconButton(onClick = {
//            if (stock > 1) {
//                stock--
//                onStockChange(stock)
//            }
//        }) {
//            androidx.compose.material3.Icon(
//                modifier = Modifier
//                    .background(Color.White)
//                    .padding(8.dp),
//                painter = painterResource(id = R.drawable.cart_minus),
//                contentDescription = "Decrease"
//            )
//        }
//
//        Text(
//            text = stock.toString(), style = TextStyle(
//                fontFamily = fontPoppins,
//                fontWeight = FontWeight.Normal,
//                fontSize = 12.sp
//            ), modifier = Modifier
//                .background(Color(0xFFebf0ff))
//                .padding(8.dp)
//                .width(48.dp),
//            textAlign = TextAlign.Center
//        )
//
//        androidx.compose.material3.IconButton(onClick = {
//            stock++
//            onStockChange(stock)
//        }) {
//            androidx.compose.material3.Icon(
//                modifier = Modifier
//                    .background(Color.White)
//                    .padding(8.dp),
//                painter = painterResource(id = R.drawable.cart_plus),
//                contentDescription = "Decrease"
//            )
//        }
//    }
//}
//
//@Composable
//fun CartSummery(
//    order: Order
//) {
//    Card(
//        modifier = Modifier
//            .fillMaxWidth()
//            .padding(start = 15.dp, end = 10.dp, top = 20.dp, bottom = 20.dp),
//    ) {
//        Column(modifier = Modifier.padding(16.dp)) {
//            TotalCartFigma(
//                title = "Items (${order.carts?.size ?: 0})",
//                value = "£ ${order.netPayable?.transformDecimal()}",
//                isBold = false,
//                style = TextStyle(
//                    fontFamily = fontPoppins,
//                    fontSize = 14.sp,
//                    fontWeight = FontWeight.Normal,
//                )
//            )
//            Spacer(modifier = Modifier.height(12.dp))
//            TotalCartFigma(
//                title = "Taxes",
//                value = "£ ${order.tax?.transformDecimal()}",
//                isBold = false,
//                style = TextStyle(
//                    fontFamily = fontPoppins,
//                    fontSize = 14.sp,
//                    fontWeight = FontWeight.Normal,
//                )
//            )
//            Spacer(modifier = Modifier.height(12.dp))
//            TotalCartFigma(
//                title = "Total Payable",
//                value = "£ ${order.totalPrice?.transformDecimal()}",
//                isBold = true,
//                style = TextStyle(
//                    fontFamily = fontPoppins,
//                    fontSize = 14.sp,
//                    fontWeight = FontWeight.Bold,
//                    color = Color(0xFF40bfff)
//                )
//            )
//        }
//    }
//}
//
//
//@Composable
//fun OrderTotalSticky(
//    productsScreenViewModel: ProductsScreenViewModel,
//    order: Order,
//    placeOrderCash: () -> Unit,
//    placeOrderCard: () -> Unit,
//) {
//    val ordersResponse by productsScreenViewModel.collectAsState(ProductsScreenState::orderResponse)
//    Card(
//        modifier = Modifier
//            .fillMaxWidth()
//            .padding(start = 15.dp, end = 10.dp, top = 20.dp, bottom = 20.dp),
//    ) {
//        Column(modifier = Modifier.padding(16.dp)) {
//            TotalCartFigma(
//                title = "Total Payable",
//                value = "£ ${order.totalPrice?.transformDecimal()}",
//                isBold = true,
//                style = TextStyle(
//                    fontFamily = fontPoppins,
//                    fontSize = 14.sp,
//                    fontWeight = FontWeight.Bold,
//                    color = Color(0xFF40bfff)
//                )
//            )
//            Spacer(modifier = Modifier.height(5.dp))
//            Text(
//                modifier = Modifier
//                    .padding(start = 2.dp)
//                    .width(170.dp),
//                text = "Summary",
//                style = TextStyle(
//                    fontFamily = fontPoppins,
//                    fontWeight = FontWeight.Normal,
//                    fontSize = 10.sp,
//                    fontStyle = FontStyle.Normal,
//                    textDecoration = TextDecoration.Underline
//                )
//            )
//            Spacer(modifier = Modifier.height(5.dp))
//            Row(
//                modifier = Modifier.fillMaxWidth(),
//                horizontalArrangement = Arrangement.Center,
//                verticalAlignment = Alignment.CenterVertically
//            ) {
//                if (ordersResponse is Loading) {
//                    CircularProgressIndicator(color = Color.Blue)
//                } else {
//                    TextButton(
//                        onClick = {
//                            placeOrderCard()
//                        },
//                        modifier = Modifier
//                            .background(color = Color.Green)
//                            .padding(5.dp),
//                        shape = CircleShape
//                    ) {
//                        Text(
//                            text = "Card", color = Color.Black,
//                            style = TextStyle(
//                                fontFamily = fontPoppins,
//                                fontSize = 14.sp,
//                                fontWeight = FontWeight.Bold
//                            )
//                        )
//                    }
//                    Spacer(modifier = Modifier.width(20.dp))
//                    TextButton(
//                        onClick = {
//                            placeOrderCash()
//                        },
//                        modifier = Modifier
//                            .background(color = Color.Red)
//                            .padding(5.dp),
//                        shape = CircleShape
//                    ) {
//                        Text(
//                            text = "Cash", color = Color.Black,
//                            style = TextStyle(
//                                fontFamily = fontPoppins,
//                                fontSize = 14.sp,
//                                fontWeight = FontWeight.Bold
//                            )
//                        )
//                    }
//                }
//            }
//        }
//    }
//}
//
//@Composable
//fun CartItemCard(
//    cartItem: Cart,
//    onRemoveItem: (Cart) -> Unit,
//    onUpdateStock: (Int, StoreItem, Cart) -> Unit,
//    onUpdateNotes: (Cart, String) -> Unit
//) {
//    var showNotesDialog by remember { mutableStateOf(false) }
//    var itemNotes by remember { mutableStateOf(cartItem.notes ?: "") }
//
//    if (showNotesDialog) {
//        OrderNotesDialog(
//            initialNotes = itemNotes,
//            onDismiss = { showNotesDialog = false },
//            onConfirm = { notes ->
//                itemNotes = notes
//                val updateCartItem = cartItem.copy(notes = notes)
//                onUpdateNotes(updateCartItem, notes)
//                showNotesDialog = false
//            }
//        )
//    }
//
//    Card(
//        modifier = Modifier
//            .fillMaxWidth()
//            .padding(start = 15.dp, end = 10.dp, top = 20.dp, bottom = 20.dp),
//    ) {
//        Column(modifier = Modifier.padding(16.dp)) {
//            Row(
//                modifier = Modifier
//                    .fillMaxWidth()
//                    .padding(end = 10.dp),
//                horizontalArrangement = Arrangement.SpaceBetween,
//                verticalAlignment = Alignment.CenterVertically
//            ) {
//                Column(modifier = Modifier) {
//                    Text(
//                        text = cartItem.storeItem?.name ?: "",
//                        fontFamily = fontNunito,
//                        fontWeight = FontWeight.Bold,
//                        fontSize = 16.sp
//                    )
//                    Spacer(modifier = Modifier.padding(5.dp))
//                    TextButton(
//                        onClick = { showNotesDialog = true },
//                        colors = androidx.compose.material.ButtonDefaults.textButtonColors(
//                            contentColor = Color(0xFF3F51B5)
//                        )
//                    ) {
//                        Text(
//                            "Add Note",
//                        )
//                    }
//                }
//                IconButton(
//                    modifier = Modifier.width(24.dp), onClick = {
//                        onRemoveItem(cartItem)
//                    },) {
//                    Icon(
//                        painter = painterResource(R.drawable.trash),
//                        contentDescription = "Remove",
//                        modifier = Modifier.size(24.dp),
//                    )
//                }
//            }
//            Spacer(modifier = Modifier.height(16.dp))
//            cartItem.storeItem?.let { storeItem ->
//                TotalCartFigma(
//                    title = "☐ ${storeItem?.quantity} x ${storeItem?.name}",
//                    value = "£ ${((storeItem?.price ?: 0.0) * (storeItem?.quantity ?: 0)).transformDecimal()}",
//                    isBold = true
//                )
//                Spacer(modifier = Modifier.height(16.dp))
//                storeItem?.extras?.forEach { element ->
//                    TotalCartFigma(
//                        title = "☐ ${element.quantity} x ${element.name}",
//                        value = "£ ${((storeItem.price ?: 0.0) * (element.quantity ?: 0)).transformDecimal()}",
//                    )
//                    Spacer(modifier = Modifier.padding(4.dp))
//                }
//                storeItem?.optionSets?.forEach { element ->
//                    element.options?.forEach { option ->
//                        if ((option?.quantity ?: 0) > 0) {
//                            TotalCartFigma(
//                                title = "☐ ${option?.quantity} x ${option?.name}",
//                                value = "£ ${((option?.price ?: 0.0) * (option?.quantity ?: 0) * (storeItem.quantity ?: 0)).transformDecimal()}"
//                            )
//                            Spacer(modifier = Modifier.padding(4.dp))
//                        }
//                    }
//                }
//            }
//            Spacer(modifier = Modifier.height(16.dp))
//            // Display notes if they exist
//            if (!itemNotes.isNullOrEmpty()) {
//                Spacer(modifier = Modifier.height(8.dp))
//                Card(
//                    modifier = Modifier.fillMaxWidth(),
//                    backgroundColor = Color(0xFFF5F5F5)
//                ) {
//                    Column(modifier = Modifier.padding(8.dp)) {
//                        Text(
//                            text = "Notes:",
//                            style = TextStyle(
//                                fontFamily = fontPoppins,
//                                fontSize = 12.sp,
//                                fontWeight = FontWeight.Bold
//                            )
//                        )
//                        Text(
//                            text = itemNotes,
//                            style = TextStyle(
//                                fontFamily = fontPoppins,
//                                fontSize = 12.sp,
//                                fontStyle = FontStyle.Italic
//                            ),
//                            modifier = Modifier.padding(top = 4.dp)
//                        )
//                    }
//                }
//            }
//            Spacer(modifier = Modifier.height(16.dp))
//            Row(
//                modifier = Modifier
//                    .fillMaxWidth()
//                    .padding(end = 10.dp),
//                horizontalArrangement = Arrangement.SpaceBetween,
//                verticalAlignment = Alignment.CenterVertically
//            ) {
//                StockUpdateCounterCart { st ->
//                    onUpdateStock(st, cartItem.storeItem ?: StoreItem(), cartItem)
//                }
//                Text(
//                    modifier = Modifier.width(70.dp),
//                    text = "£ ${cartItem.netPayable?.transformDecimal() ?: ""}",
//                    style = TextStyle(
//                        fontFamily = fontPoppins,
//                        fontSize = 14.sp,
//                        fontWeight = FontWeight.Bold,
//                        color = Color(0xFF40bfff)
//                    ),
//                    fontWeight = FontWeight.Bold
//                )
//            }
//        }
//    }
//}
//
//@Composable
//fun OrderNotesDialog(
//    initialNotes: String,
//    onDismiss: () -> Unit,
//    onConfirm: (String) -> Unit
//) {
//    var notes by remember { mutableStateOf(initialNotes) }
//
//    Dialog(onDismissRequest = {
//        onDismiss()
//    }) {
//        Card(
//            modifier = Modifier
//                .fillMaxWidth()
//                .padding(16.dp),
//            shape = RoundedCornerShape(16.dp)
//        ) {
//            Column(
//                modifier = Modifier
//                    .fillMaxWidth()
//                    .padding(16.dp),
//                horizontalAlignment = Alignment.CenterHorizontally
//            ) {
//                Text(
//                    text = "Add Order Notes",
//                    style = TextStyle(
//                        fontFamily = fontPoppins,
//                        fontSize = 16.sp,
//                        fontWeight = FontWeight.Bold
//                    ),
//                    modifier = Modifier.padding(bottom = 16.dp)
//                )
//
//                TextField(
//                    value = notes,
//                    onValueChange = { notes = it },
//                    modifier = Modifier
//                        .fillMaxWidth()
//                        .height(120.dp),
//                    placeholder = { Text("Enter notes for this item...") },
//                    maxLines = 5
//                )
//
//                Spacer(modifier = Modifier.height(16.dp))
//
//                Row(
//                    modifier = Modifier.fillMaxWidth(),
//                    horizontalArrangement = Arrangement.End
//                ) {
//                    TextButton(onClick = onDismiss) {
//                        Text("Cancel")
//                    }
//
//                    Spacer(modifier = Modifier.width(8.dp))
//
//                    TextButton(
//                        onClick = { onConfirm(notes) },
//                        colors = androidx.compose.material.ButtonDefaults.textButtonColors(
//                            contentColor = Color(0xFF40bfff)
//                        )
//                    ) {
//                        Text("Save")
//                    }
//                }
//            }
//        }
//    }
//}
//
//@Composable
//fun TotalCartFigma(
//    title: String, value: String, style: TextStyle = TextStyle(
//        fontFamily = fontNunito,
//        fontSize = 14.sp,
//        fontWeight = FontWeight.Normal,
//    ), isBold: Boolean = false
//) {
//    Row(
//        modifier = Modifier
//            .fillMaxWidth()
//            .padding(4.dp),
//        horizontalArrangement = Arrangement.SpaceBetween
//    ) {
//        androidx.compose.material3.Text(
//            modifier = Modifier.width(180.dp),
//            text = title,
//            style = style,
//            fontWeight = if (isBold) FontWeight.Bold else FontWeight.Normal
//        )
//        androidx.compose.material3.Text(
//            modifier = Modifier.width(80.dp),
//            text = value,
//            style = style,
//            fontWeight = if (isBold) FontWeight.Bold else FontWeight.Normal
//        )
//    }
//}
