package com.thedasagroup.suminative.ui.local_orders

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CloudDone
import androidx.compose.material.icons.filled.CloudOff
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material.icons.filled.Sync
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.compose.collectAsState
import com.airbnb.mvrx.compose.mavericksViewModel
import com.thedasagroup.suminative.database.OrderEntity
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LocalOrdersScreen(
    viewModel: LocalOrdersViewModel = mavericksViewModel()
) {
    val state by viewModel.collectAsState()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF5F5F5))
            .padding(16.dp)
    ) {
        // Header
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Local Orders",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF2E3A59)
            )
            
            IconButton(
                onClick = {
//                    viewModel.refreshOrders()
                }
            ) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = "Refresh",
                    tint = Color(0xFF2E3A59)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Filter Chips
        LazyRow(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier.fillMaxWidth()
        ) {
            items(OrderFilter.values()) { filter ->
                FilterChip(
                    filter = filter,
                    isSelected = state.selectedFilter == filter,
                    onClick = {
                        when (filter) {
                            OrderFilter.ALL -> viewModel.loadAllOrders()
                            OrderFilter.PENDING -> viewModel.loadOrdersByStatus("PENDING")
                            OrderFilter.ACCEPTED -> viewModel.loadOrdersByStatus("ACCEPTED")
                            OrderFilter.PREPARING -> viewModel.loadOrdersByStatus("PREPARING")
                            OrderFilter.READY -> viewModel.loadOrdersByStatus("READY")
                            OrderFilter.COMPLETED -> viewModel.loadOrdersByStatus("COMPLETED")
                            OrderFilter.CANCELLED -> viewModel.loadOrdersByStatus("CANCELLED")
                            OrderFilter.SYNCED -> {
                                viewModel.loadSyncedOrders()
                            }
                            OrderFilter.UNSYNCED -> {
                                viewModel.loadUnsyncedOrders()
                            }
                        }
                    }
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Orders List
        when (val ordersAsync = state.orders) {
            is Loading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        color = Color(0xFF2E3A59)
                    )
                }
            }
            is Success -> {
                if (ordersAsync.invoke().isEmpty()) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "No Orders Found",
                                fontSize = 18.sp,
                                fontWeight = FontWeight.Medium,
                                color = Color.Gray
                            )
                            Text(
                                text = "Create some orders to see them here",
                                fontSize = 14.sp,
                                color = Color.Gray
                            )
                        }
                    }
                } else {
                    LazyColumn(
                        verticalArrangement = Arrangement.spacedBy(12.dp),
                        modifier = Modifier.fillMaxSize()
                    ) {
                        items(ordersAsync.invoke()) { order ->
                            OrderCard(
                                order = order,
                                onStatusUpdate = { status ->
//                                    viewModel.updateOrderStatus(order.id, status)
                                },
                                onMarkComplete = {
//                                    viewModel.markOrderComplete(order.id)
                                },
                                onSyncOrder = {
                                    // TODO: Add sync functionality
                                    // viewModel.syncOrder(order.id)
                                }
                            )
                        }
                    }
                }
            }
            else -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "Failed to load orders",
                        color = Color.Red
                    )
                }
            }
        }
    }
}

@Composable
fun FilterChip(
    filter: OrderFilter,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val backgroundColor = if (isSelected) Color(0xFF2E3A59) else Color.White
    val textColor = if (isSelected) Color.White else Color(0xFF2E3A59)
    
    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(20.dp))
            .background(backgroundColor)
            .border(
                width = 1.dp,
                color = Color(0xFF2E3A59),
                shape = RoundedCornerShape(20.dp)
            )
            .clickable { onClick() }
            .padding(horizontal = 16.dp, vertical = 8.dp)
    ) {
        Text(
            text = when (filter) {
                OrderFilter.SYNCED -> "Synced"
                OrderFilter.UNSYNCED -> "Unsynced"
                else -> filter.name.lowercase().replaceFirstChar { it.uppercase() }
            },
            color = textColor,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
fun OrderCard(
    order: OrderEntity,
    onStatusUpdate: (String) -> Unit,
    onMarkComplete: () -> Unit,
    onSyncOrder: () -> Unit
) {
    var showStatusMenu by remember { mutableStateOf(false) }
    val dateFormatter = SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault())
    val isSynced = order.synced == 1L
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp)),
        colors = CardDefaults.cardColors(
            containerColor = Color.White
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 4.dp
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // Order Header with Sync Status
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Order #${order.orderId.take(8)}",
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color(0xFF2E3A59)
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        // Sync Status Icon
                        Icon(
                            imageVector = if (isSynced) Icons.Default.CloudDone else Icons.Default.CloudOff,
                            contentDescription = if (isSynced) "Synced" else "Not Synced",
                            tint = if (isSynced) Color(0xFF4CAF50) else Color(0xFFFF9800),
                            modifier = Modifier.size(16.dp)
                        )
                    }
                    
                    Text(
                        text = dateFormatter.format(Date(order.createdAt)),
                        fontSize = 12.sp,
                        color = Color.Gray
                    )
                }
                
                OrderStatusChip(status = order.status)
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Customer Info
            if (!order.customerName.isNullOrEmpty()) {
                Row {
                    Text(
                        text = "Customer: ",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color.Gray
                    )
                    Text(
                        text = order.customerName,
                        fontSize = 14.sp,
                        color = Color(0xFF2E3A59)
                    )
                }
            }
            
            // Order Type & Table
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = "Type: ${order.orderType}",
                    fontSize = 14.sp,
                    color = Color.Gray
                )
                
                if (order.tableNumber != null) {
                    Text(
                        text = "Table: ${order.tableNumber}",
                        fontSize = 14.sp,
                        color = Color.Gray
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(12.dp))
            
            // Total & Payment with Sync Status
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column {
                    Text(
                        text = "Total: $${String.format("%.2f", order.total)}",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color(0xFF2E3A59)
                    )
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Payment: ${order.paymentStatus}",
                            fontSize = 12.sp,
                            color = Color.Gray
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = if (isSynced) "Synced" else "Not Synced",
                            fontSize = 12.sp,
                            color = if (isSynced) Color(0xFF4CAF50) else Color(0xFFFF9800),
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
                
                // Action Buttons
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // Sync Button (only show for unsynced orders)
                    if (!isSynced) {
                        IconButton(
                            onClick = onSyncOrder,
                            modifier = Modifier.size(32.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Sync,
                                contentDescription = "Sync Order",
                                tint = Color(0xFF2196F3),
                                modifier = Modifier.size(20.dp)
                            )
                        }
                    }
                    
                    if (order.status != "COMPLETED" && order.status != "CANCELLED") {
                        OutlinedButton(
                            onClick = { showStatusMenu = true },
                            colors = ButtonDefaults.outlinedButtonColors(
                                contentColor = Color(0xFF2E3A59)
                            )
                        ) {
                            Text("Update Status", fontSize = 12.sp)
                        }
                        
                        if (order.status != "COMPLETED") {
                            Button(
                                onClick = onMarkComplete,
                                colors = ButtonDefaults.buttonColors(
                                    containerColor = Color(0xFF4CAF50)
                                )
                            ) {
                                Text("Complete", fontSize = 12.sp)
                            }
                        }
                    }
                }
            }
            
            // Status Update Menu
            if (showStatusMenu) {
                DropdownMenu(
                    expanded = showStatusMenu,
                    onDismissRequest = { showStatusMenu = false }
                ) {
                    val statuses = listOf("PENDING", "ACCEPTED", "PREPARING", "READY", "CANCELLED")
                    statuses.forEach { status ->
                        DropdownMenuItem(
                            text = { Text(status) },
                            onClick = {
                                onStatusUpdate(status)
                                showStatusMenu = false
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun OrderStatusChip(status: String) {
    val backgroundColor = when (status) {
        "PENDING" -> Color(0xFFFFC107)
        "ACCEPTED" -> Color(0xFF2196F3)
        "PREPARING" -> Color(0xFFFF9800)
        "READY" -> Color(0xFF9C27B0)
        "COMPLETED" -> Color(0xFF4CAF50)
        "CANCELLED" -> Color(0xFFF44336)
        else -> Color.Gray
    }
    
    Box(
        modifier = Modifier
            .clip(RoundedCornerShape(12.dp))
            .background(backgroundColor.copy(alpha = 0.1f))
            .padding(horizontal = 12.dp, vertical = 4.dp)
    ) {
        Text(
            text = status,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium,
            color = backgroundColor,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
    }
} 