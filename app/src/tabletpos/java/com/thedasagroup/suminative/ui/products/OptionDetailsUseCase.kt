package com.thedasagroup.suminative.ui.products

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.model.request.option_details.GetOptionDetailsRequest
import com.thedasagroup.suminative.data.model.request.order.OptionSet
import com.thedasagroup.suminative.data.model.request.stock.ChangeStockRequest
import com.thedasagroup.suminative.data.model.request.stock.GetPagedStockItemsRequest
import com.thedasagroup.suminative.data.model.response.options_details.OptionDetails
import com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse
import com.thedasagroup.suminative.data.model.response.store_orders.Option
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.StockRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

class OptionDetailsUseCase(private val stockRepository: StockRepository) {
    suspend operator fun invoke(itemId: Int): StateFlow<Async<OptionDetails>> {
        return stockRepository.getOptionDetails(
            request = GetOptionDetailsRequest(itemId = itemId)
        )

        /*val optionDetails = OptionDetails(
            command = "command",
            optionSets = mutableListOf(
                OptionSet(
                    id = 0, name = "name", options = mutableListOf(
                        Option(
                            id = 0, name = "name", price = 0.0
                        ),
                        Option(
                            id = 0, name = "name", price = 0.0
                        ),
                        Option(
                            id = 0, name = "name", price = 0.0
                        )
                    )
                ),
                OptionSet(
                    id = 0, name = "name", options = mutableListOf(
                        Option(
                            id = 0, name = "name", price = 0.0
                        ),
                        Option(
                            id = 0, name = "name", price = 0.0
                        ),
                        Option(
                            id = 0, name = "name", price = 0.0
                        )
                    )
                )
            ),
            paymentId = 0,
            paymentResponseJson = "paymentResponseJson",
            socketId = "socketId",
            success = false
        )*/
//        return MutableStateFlow(Success(optionDetails))
    }
}