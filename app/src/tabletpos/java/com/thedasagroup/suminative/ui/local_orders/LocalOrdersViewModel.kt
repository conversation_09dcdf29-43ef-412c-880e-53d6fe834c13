package com.thedasagroup.suminative.ui.local_orders

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.MavericksViewModelFactory
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.hilt.AssistedViewModelFactory
import com.airbnb.mvrx.hilt.hiltMavericksViewModelFactory
import com.thedasagroup.suminative.database.OrderEntity
import com.thedasagroup.suminative.domain.orders.GetLocalOrdersUseCase
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

class LocalOrdersViewModel @AssistedInject constructor(
    @Assisted state: LocalOrdersState,
    private val getLocalOrdersUseCase: GetLocalOrdersUseCase
) : MavericksViewModel<LocalOrdersState>(state) {

    init {
        loadAllOrders()
    }

    fun loadAllOrders() {
        viewModelScope.launch {
            setState { copy(orders = Loading(), selectedFilter = OrderFilter.ALL) }
            
            getLocalOrdersUseCase.getAllOrders()
                .catch { throwable ->
                    setState { copy(orders = Fail(throwable)) }
                }
                .collectLatest { ordersList ->
                    setState { 
                        copy(orders = Success(ordersList))
                    }
                }
        }
    }

    fun loadOrdersByStatus(status: String) {
        viewModelScope.launch {
            setState { 
                copy(
                    orders = Loading(), 
                    selectedFilter = OrderFilter.valueOf(status.uppercase())
                ) 
            }
            
            getLocalOrdersUseCase.getOrdersByStatus(status)
                .catch { throwable ->
                    setState { copy(orders = Fail(throwable)) }
                }
                .collectLatest { ordersList ->
                    setState { 
                        copy(orders = Success(ordersList))
                    }
                }
        }
    }


    fun loadUnsyncedOrders() {
        viewModelScope.launch {
            setState {
                copy(
                    orders = Loading(),
                    selectedFilter = OrderFilter.valueOf(OrderFilter.UNSYNCED.name.uppercase())
                )
            }

            getLocalOrdersUseCase.getUnsyncedOrders()
                .catch { throwable ->
                    setState { copy(orders = Fail(throwable)) }
                }
                .collectLatest { ordersList ->
                    setState {
                        copy(orders = Success(ordersList))
                    }
                }
        }
    }

    fun loadSyncedOrders() {
        viewModelScope.launch {
            setState {
                copy(
                    orders = Loading(),
                    selectedFilter = OrderFilter.valueOf(OrderFilter.SYNCED.name.uppercase())
                )
            }

            getLocalOrdersUseCase.getSyncedOrders()
                .catch { throwable ->
                    setState { copy(orders = Fail(throwable)) }
                }
                .collectLatest { ordersList ->
                    setState {
                        copy(orders = Success(ordersList))
                    }
                }
        }
    }

    fun loadPendingOrders() {
        viewModelScope.launch {
            setState { copy(orders = Loading(), selectedFilter = OrderFilter.PENDING) }
            
            getLocalOrdersUseCase.getPendingOrders()
                .catch { throwable ->
                    setState { copy(orders = Fail(throwable)) }
                }
                .collectLatest { ordersList ->
                    setState { 
                        copy(orders = Success(ordersList))
                    }
                }
        }
    }

//    fun updateOrderStatus(orderId: Long, status: String) {
//        viewModelScope.launch {
//            try {
//                getLocalOrdersUseCase.updateOrderStatus(orderId, status)
//                // Refresh the current view based on selected filter
//                when (withState(this@LocalOrdersViewModel) { it.selectedFilter }) {
//                    OrderFilter.ALL -> loadAllOrders()
//                    OrderFilter.PENDING -> loadPendingOrders()
//                    else -> loadOrdersByStatus(withState(this@LocalOrdersViewModel) { it.selectedFilter.name })
//                }
//            } catch (e: Exception) {
//                // Handle error if needed
//            }
//        }
//    }
//
//    fun markOrderComplete(orderId: Long) {
//        viewModelScope.launch {
//            try {
//                getLocalOrdersUseCase.markOrderComplete(orderId)
//                // Refresh the current view
//                when (withState(this@LocalOrdersViewModel) { it.selectedFilter }) {
//                    OrderFilter.ALL -> loadAllOrders()
//                    OrderFilter.PENDING -> loadPendingOrders()
//                    else -> loadOrdersByStatus(withState(this@LocalOrdersViewModel) { it.selectedFilter.name })
//                }
//            } catch (e: Exception) {
//                // Handle error if needed
//            }
//        }
//    }
//
//    fun refreshOrders() {
//        when (withState(this) { it.selectedFilter }) {
//            OrderFilter.ALL -> loadAllOrders()
//            OrderFilter.PENDING -> loadPendingOrders()
//            else -> loadOrdersByStatus(withState(this) { it.selectedFilter.name })
//        }
//    }

    @AssistedFactory
    interface Factory : AssistedViewModelFactory<LocalOrdersViewModel, LocalOrdersState> {
        override fun create(state: LocalOrdersState): LocalOrdersViewModel
    }

    companion object :
        MavericksViewModelFactory<LocalOrdersViewModel, LocalOrdersState> by hiltMavericksViewModelFactory()

}

data class LocalOrdersState(
    val orders: Async<List<OrderEntity>> = Uninitialized,
    val filteredOrders: Async<List<OrderEntity>> = Uninitialized,
    val pendingOrders: Async<List<OrderEntity>> = Uninitialized,
    val selectedFilter: OrderFilter = OrderFilter.ALL
) : MavericksState

enum class OrderFilter {
    ALL, PENDING, ACCEPTED, PREPARING, READY, COMPLETED, CANCELLED, SYNCED, UNSYNCED
} 