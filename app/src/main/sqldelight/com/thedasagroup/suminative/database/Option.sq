CREATE TABLE OptionEntity (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    optionId INTEGER, -- Remote server ID
    optionSetId INTEGER NOT NULL, -- Link to OptionSetEntity.optionSetId
    productId INTEGER NOT NULL, -- Link to ProductEntity.productId
    name TEXT,
    price REAL NOT NULL DEFAULT 0.0,
    displayOrder INTEGER,
    status INTEGER,
    quantity INTEGER DEFAULT 0,
    initialQuantity INTEGER DEFAULT 1,
    storeId INTEGER NOT NULL,
    createdAt INTEGER NOT NULL, -- Local timestamp
    updatedAt INTEGER NOT NULL, -- Local timestamp
    synced INTEGER NOT NULL DEFAULT 0, -- 0 = not synced, 1 = synced
    
    FOREIGN KEY(optionSetId) REFERENCES OptionSetEntity(optionSetId),
    FOREIGN KEY(productId) REFERENCES ProductEntity(productId)
);

-- Indexes for better performance
CREATE UNIQUE INDEX idx_option_id_unique ON OptionEntity(optionId);
CREATE INDEX idx_option_optionset ON OptionEntity(optionSetId);
CREATE INDEX idx_option_product ON OptionEntity(productId);
CREATE INDEX idx_option_store ON OptionEntity(storeId);
CREATE INDEX idx_option_synced ON OptionEntity(synced);
CREATE INDEX idx_option_display_order ON OptionEntity(displayOrder);

-- Queries for Option operations

insertOption:
INSERT INTO OptionEntity (
    optionId, optionSetId, productId, name, price, displayOrder,
    status, quantity, initialQuantity, storeId, createdAt, updatedAt, synced
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

insertOrReplaceOption:
INSERT OR REPLACE INTO OptionEntity (
    optionId, optionSetId, productId, name, price, displayOrder,
    status, quantity, initialQuantity, storeId, createdAt, updatedAt, synced
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

getOptionsByOptionSet:
SELECT * FROM OptionEntity WHERE optionSetId = ? ORDER BY displayOrder ASC;

getOptionsByProduct:
SELECT * FROM OptionEntity WHERE productId = ? ORDER BY displayOrder ASC;

getOptionsByStore:
SELECT * FROM OptionEntity WHERE storeId = ? ORDER BY displayOrder ASC;

getOptionById:
SELECT * FROM OptionEntity WHERE optionId = ?;

updateOption:
UPDATE OptionEntity 
SET name = ?, price = ?, displayOrder = ?, status = ?,
    quantity = ?, initialQuantity = ?, updatedAt = ?, synced = ?
WHERE optionId = ?;

deleteOptionsByOptionSet:
DELETE FROM OptionEntity WHERE optionSetId = ?;

deleteOptionsByProduct:
DELETE FROM OptionEntity WHERE productId = ?;

deleteAllOptions:
DELETE FROM OptionEntity;

-- Count queries
countOptionsByOptionSet:
SELECT COUNT(*) FROM OptionEntity WHERE optionSetId = ?;

countOptionsByProduct:
SELECT COUNT(*) FROM OptionEntity WHERE productId = ?;

countTotalOptions:
SELECT COUNT(*) FROM OptionEntity; 