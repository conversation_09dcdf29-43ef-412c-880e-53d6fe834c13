CREATE TABLE OrderEntity (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    orderId TEXT NOT NULL UNIQUE,
    customerId INTEGER,
    customerName TEXT,
    customerPhone TEXT,
    orderType TEXT NOT NULL, -- 'DINE_IN', 'TAKEAWAY', 'DELIVERY'
    status TEXT NOT NULL, -- 'PENDING', 'ACCEPTED', 'PREPARING', 'READY', 'COMPLETED', 'CANCELLED'
    subtotal REAL NOT NULL DEFAULT 0.0,
    tax REAL NOT NULL DEFAULT 0.0,
    discount REAL NOT NULL DEFAULT 0.0,
    total REAL NOT NULL DEFAULT 0.0,
    paymentMethod TEXT, -- 'CASH', 'CARD', 'DIGITAL'
    paymentStatus TEXT NOT NULL DEFAULT 'PENDING', -- 'PENDING', 'PAID', 'PARTIAL', 'REFUNDED'
    notes TEXT,
    tableNumber INTEGER,
    deliveryAddress TEXT,
    createdAt INTEGER NOT NULL, -- timestamp
    updatedAt INTEGER NOT NULL, -- timestamp
    completedAt INTEGER, -- timestamp
    storeId INTEGER NOT NULL,
    waiterId INTEGER,
    waiterName TEXT,
    synced INTEGER NOT NULL DEFAULT 0 -- 0 = not synced, 1 = synced
);

CREATE INDEX idx_order_status ON OrderEntity(status);
CREATE INDEX idx_order_store ON OrderEntity(storeId);
CREATE INDEX idx_order_created ON OrderEntity(createdAt);
CREATE INDEX idx_order_type ON OrderEntity(orderType);
CREATE INDEX idx_order_synced ON OrderEntity(synced);

-- Queries for Order operations
insertOrder:
INSERT INTO OrderEntity (
    orderId, customerId, customerName, customerPhone, orderType, status,
    subtotal, tax, discount, total, paymentMethod, paymentStatus,
    notes, tableNumber, deliveryAddress, createdAt, updatedAt,
    storeId, waiterId, waiterName, synced
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

getAllOrders:
SELECT * FROM OrderEntity ORDER BY createdAt DESC;

getOrderById:
SELECT * FROM OrderEntity WHERE id = ?;

getOrderByOrderId:
SELECT * FROM OrderEntity WHERE orderId = ?;

getOrdersByStatus:
SELECT * FROM OrderEntity WHERE status = ? ORDER BY createdAt DESC;

getOrdersByStore:
SELECT * FROM OrderEntity WHERE storeId = ? ORDER BY createdAt DESC;

getOrdersByDateRange:
SELECT * FROM OrderEntity 
WHERE createdAt >= ? AND createdAt <= ? 
ORDER BY createdAt DESC;

getPendingOrders:
SELECT * FROM OrderEntity 
WHERE status IN ('PENDING', 'ACCEPTED', 'PREPARING') 
ORDER BY createdAt ASC;

getTodaysOrders:
SELECT * FROM OrderEntity 
WHERE createdAt >= ? AND storeId = ?
ORDER BY createdAt DESC;

getUnsyncedOrders:
SELECT * FROM OrderEntity 
WHERE synced = 0 
ORDER BY createdAt ASC;

getSyncedOrders:
SELECT * FROM OrderEntity 
WHERE synced = 1 
ORDER BY createdAt DESC;

updateOrderStatus:
UPDATE OrderEntity 
SET status = ?, updatedAt = ?
WHERE id = ?;

updateOrderPayment:
UPDATE OrderEntity 
SET paymentMethod = ?, paymentStatus = ?, updatedAt = ?
WHERE id = ?;

markOrderComplete:
UPDATE OrderEntity 
SET status = 'COMPLETED', completedAt = ?, updatedAt = ?
WHERE id = ?;

markOrderSynced:
UPDATE OrderEntity 
SET synced = 1, updatedAt = ?
WHERE id = ?;

markOrderUnsynced:
UPDATE OrderEntity 
SET synced = 0, updatedAt = ?
WHERE id = ?;

markOrdersSyncedByOrderIds:
UPDATE OrderEntity 
SET synced = 1, updatedAt = ?
WHERE orderId IN ?;

deleteOrder:
DELETE FROM OrderEntity WHERE id = ?;

getOrderCount:
SELECT COUNT(*) FROM OrderEntity;

getUnsyncedOrderCount:
SELECT COUNT(*) FROM OrderEntity WHERE synced = 0;

getTotalSalesByDateRange:
SELECT SUM(total) FROM OrderEntity 
WHERE createdAt >= ? AND createdAt <= ? 
AND paymentStatus = 'PAID' AND storeId = ?; 