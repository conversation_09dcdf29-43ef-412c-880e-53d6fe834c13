CREATE TABLE CategoryEntity (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    storeId INTEGER NOT NULL,
    sortOrder INTEGER NOT NULL DEFAULT 0,
    isActive INTEGER NOT NULL DEFAULT 1,
    createdAt INTEGER NOT NULL,
    updatedAt INTEGER NOT NULL,
    syncedAt INTEGER DEFAULT NULL,
    UNIQUE(name, storeId)
);

-- Create index for faster queries
CREATE INDEX idx_category_store_id ON CategoryEntity(storeId);
CREATE INDEX idx_category_sort_order ON CategoryEntity(storeId, sortOrder);
CREATE INDEX idx_category_active ON CategoryEntity(storeId, isActive);

-- Insert a new category
insertCategory:
INSERT INTO CategoryEntity (
    name,
    storeId,
    sortOrder,
    isActive,
    createdAt,
    updatedAt
) VALUES (?, ?, ?, ?, ?, ?);

-- Get all categories for a store ordered by sortOrder
getCategoriesByStore:
SELECT * FROM CategoryEntity
WHERE storeId = ? AND isActive = 1
ORDER BY sortOrder ASC, name ASC;

-- Get all categories for a store (including inactive)
getAllCategoriesByStore:
SELECT * FROM CategoryEntity
WHERE storeId = ?
ORDER BY sortOrder ASC, name ASC;

-- Get category by name and store
getCategoryByNameAndStore:
SELECT * FROM CategoryEntity
WHERE name = ? AND storeId = ? LIMIT 1;

-- Get category by id
getCategoryById:
SELECT * FROM CategoryEntity
WHERE id = ? LIMIT 1;

-- Update category sort order
updateCategorySortOrder:
UPDATE CategoryEntity
SET sortOrder = ?, updatedAt = ?
WHERE id = ?;

-- Update category active status
updateCategoryActiveStatus:
UPDATE CategoryEntity
SET isActive = ?, updatedAt = ?
WHERE id = ?;

-- Update category sync timestamp
updateCategorySyncedAt:
UPDATE CategoryEntity
SET syncedAt = ?, updatedAt = ?
WHERE id = ?;

-- Delete category by id
deleteCategoryById:
DELETE FROM CategoryEntity WHERE id = ?;

-- Delete categories by store
deleteCategoriesByStore:
DELETE FROM CategoryEntity WHERE storeId = ?;

-- Get categories count by store
getCategoriesCountByStore:
SELECT COUNT(*) FROM CategoryEntity
WHERE storeId = ? AND isActive = 1;

-- Get category sort order by name
getCategorySortOrderByName:
SELECT sortOrder FROM CategoryEntity
WHERE name = ? AND storeId = ? AND isActive = 1;

-- Update multiple categories sort order
updateCategoriesSortOrder:
UPDATE CategoryEntity
SET sortOrder = ?, updatedAt = ?
WHERE name = ? AND storeId = ?;

-- Bulk insert or replace categories
insertOrReplaceCategory:
INSERT OR REPLACE INTO CategoryEntity (
    name,
    storeId,
    sortOrder,
    isActive,
    createdAt,
    updatedAt
) VALUES (?, ?, ?, ?, ?, ?);

-- Get unsynced categories (categories added locally but not yet synced)
getUnsyncedCategories:
SELECT * FROM CategoryEntity
WHERE syncedAt IS NULL
ORDER BY createdAt ASC;

-- Mark categories as synced
markCategoriesSynced:
UPDATE CategoryEntity
SET syncedAt = ?, updatedAt = ?
WHERE storeId = ?; 