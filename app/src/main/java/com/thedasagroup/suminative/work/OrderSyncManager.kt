package com.thedasagroup.suminative.work

import android.content.Context
import android.util.Log
import androidx.work.BackoffPolicy
import androidx.work.Constraints
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.ExistingWorkPolicy
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkInfo
import androidx.work.WorkManager
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class OrderSyncManager @Inject constructor(
    private val context: Context
) {
    private val workManager = WorkManager.getInstance(context)
    
    companion object {
        private const val TAG = "OrderSyncManager"
        private const val PERIODIC_SYNC_WORK_NAME = "periodic_order_sync"
        private const val MANUAL_SYNC_WORK_NAME = "manual_order_sync"
        
        // Default sync interval - every 30 minutes
        private const val DEFAULT_SYNC_INTERVAL_MINUTES = 30L
    }

    /**
     * Schedule periodic order synchronization
     * This will run automatically in the background at regular intervals
     */
    fun schedulePeriodicSync(intervalMinutes: Long = DEFAULT_SYNC_INTERVAL_MINUTES) {
        Log.i(TAG, "Scheduling periodic order sync with interval: $intervalMinutes minutes")
        
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .setRequiresBatteryNotLow(false)
            .build()

        val periodicWorkRequest = PeriodicWorkRequestBuilder<SyncOrdersWorker>(
            intervalMinutes, TimeUnit.MINUTES,
            15, TimeUnit.MINUTES // Flex interval - can run 15 minutes before the interval
        )
            .setConstraints(constraints)
            .setBackoffCriteria(
                BackoffPolicy.EXPONENTIAL,
                15, TimeUnit.MINUTES
            )
            .addTag("order_sync")
            .addTag("periodic")
            .build()

        workManager.enqueueUniquePeriodicWork(
            PERIODIC_SYNC_WORK_NAME,
            ExistingPeriodicWorkPolicy.KEEP,
            periodicWorkRequest
        )
        
        Log.i(TAG, "Periodic order sync scheduled successfully")
    }

    /**
     * Cancel periodic order synchronization
     */
    fun cancelPeriodicSync() {
        Log.i(TAG, "Cancelling periodic order sync")
        workManager.cancelUniqueWork(PERIODIC_SYNC_WORK_NAME)
    }

    /**
     * Trigger immediate order synchronization
     * This runs once immediately and doesn't affect the periodic schedule
     */
    fun triggerImmediateSync() {
        Log.i(TAG, "Triggering immediate order sync")
        
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()

        val immediateWorkRequest = OneTimeWorkRequestBuilder<SyncOrdersWorker>()
            .setConstraints(constraints)
            .setBackoffCriteria(
                BackoffPolicy.EXPONENTIAL,
                10, TimeUnit.SECONDS
            )
            .addTag("order_sync")
            .addTag("manual")
            .build()

        workManager.enqueueUniqueWork(
            MANUAL_SYNC_WORK_NAME,
            ExistingWorkPolicy.REPLACE,
            immediateWorkRequest
        )
        
        Log.i(TAG, "Immediate order sync triggered")
    }

    /**
     * Get the status of periodic sync work
     */
    fun getPeriodicSyncStatus(): Flow<List<WorkInfo>> {
        return workManager.getWorkInfosForUniqueWorkFlow(PERIODIC_SYNC_WORK_NAME)
    }

    /**
     * Get the status of manual sync work
     */
    fun getManualSyncStatus(): Flow<List<WorkInfo>> {
        return workManager.getWorkInfosForUniqueWorkFlow(MANUAL_SYNC_WORK_NAME)
    }

    /**
     * Check if periodic sync is currently running
     */
    fun isPeriodicSyncRunning(): Flow<Boolean> {
        return getPeriodicSyncStatus().map { workInfos ->
            workInfos.any { it.state == WorkInfo.State.RUNNING }
        }
    }

    /**
     * Check if manual sync is currently running
     */
    fun isManualSyncRunning(): Flow<Boolean> {
        return getManualSyncStatus().map { workInfos ->
            workInfos.any { it.state == WorkInfo.State.RUNNING }
        }
    }

    /**
     * Get the last sync result from the most recent completed work
     */
    fun getLastSyncResult(): Flow<SyncWorkResult?> {
        return getManualSyncStatus().map { workInfos ->
            workInfos.lastOrNull { it.state == WorkInfo.State.SUCCEEDED || it.state == WorkInfo.State.FAILED }
                ?.let { workInfo ->
                    SyncWorkResult(
                        isSuccess = workInfo.state == WorkInfo.State.SUCCEEDED,
                        syncedCount = workInfo.outputData.getInt(SyncOrdersWorker.KEY_SYNCED_COUNT, 0),
                        failedCount = workInfo.outputData.getInt(SyncOrdersWorker.KEY_FAILED_COUNT, 0),
                        totalCount = workInfo.outputData.getInt(SyncOrdersWorker.KEY_TOTAL_COUNT, 0),
                        errorMessage = workInfo.outputData.getString(SyncOrdersWorker.KEY_ERROR_MESSAGE)
                    )
                }
        }
    }

    /**
     * Cancel all order sync work (both periodic and manual)
     */
    fun cancelAllSync() {
        Log.i(TAG, "Cancelling all order sync work")
        workManager.cancelAllWorkByTag("order_sync")
    }

    /**
     * Check if the worker is currently scheduled or running
     */
    fun isSyncActive(): Flow<Boolean> {
        return workManager.getWorkInfosByTagFlow("order_sync").map { workInfos ->
            workInfos.any { 
                it.state == WorkInfo.State.RUNNING || 
                it.state == WorkInfo.State.ENQUEUED 
            }
        }
    }
}

/**
 * Data class to represent sync work result
 */
data class SyncWorkResult(
    val isSuccess: Boolean,
    val syncedCount: Int,
    val failedCount: Int,
    val totalCount: Int,
    val errorMessage: String? = null
) 