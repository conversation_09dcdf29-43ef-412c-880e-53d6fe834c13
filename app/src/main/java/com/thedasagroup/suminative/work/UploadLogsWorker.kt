package com.thedasagroup.suminative.work

import android.annotation.SuppressLint
import android.content.Context
import android.util.Log
import androidx.hilt.work.HiltWorker
import androidx.work.Worker
import androidx.work.WorkerParameters
import com.thedasagroup.suminative.data.repo.LogsRepository
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import kotlinx.coroutines.runBlocking
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.atomic.AtomicInteger

@HiltWorker
class UploadLogsWorker @AssistedInject constructor(private val logsRepository: LogsRepository,
                                                   @Assisted appContext : Context,
                                                   @Assisted params : WorkerParameters) : Worker(appContext, params) {

    private val TAG = "UploadLogsWorker"
    
    override fun doWork(): Result {
        try {
            // Get the directory where logs are stored
            val diskPath: String = applicationContext.cacheDir.absolutePath
            val folderPath = diskPath + File.separatorChar + "logger"
            val logDirectory = File(folderPath)
            if (!logDirectory.exists() || !logDirectory.isDirectory) {
                Log.e(TAG, "Log directory doesn't exist or is not a directory: ${logDirectory.absolutePath}")
//                Timber.e("Log directory doesn't exist or is not a directory: ${logDirectory.absolutePath}")
                return Result.failure()
            }

            // Upload all log files
            val uploadSuccess = uploadLogFiles(logDirectory)

            if (uploadSuccess) {
                Log.i(TAG, "Successfully uploaded all log files")
//                Timber.i("Successfully uploaded all log files")
                return Result.success()
            } else {
                Log.e(TAG, "Failed to upload some log files")
//                Timber.e("Failed to upload some log files")
                return Result.failure()
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error in doWork: ${e.message}", e)
//            Timber.e(e, "Error in doWork: ${e.message}")
            return Result.failure()
        }
    }
    
    @SuppressLint("SuspiciousIndentation")
    private fun uploadLogFiles(directory: File): Boolean {
        try {
            // List all log files in the directory
            val logFiles = directory.listFiles { file ->
                file.isFile && (file.name.endsWith(".csv"))
            } ?: return true // No files to upload is considered success
            
            if (logFiles.isEmpty()) {
                Log.w(TAG, "No log files found to upload in directory: ${directory.absolutePath}")
//                Timber.w("No log files found to upload in directory: ${directory.absolutePath}")
                return true // No files to upload is considered success
            }
            
            // Get the current date and time for the folder name
            val dateFormat = SimpleDateFormat("yyyy-MM-dd_HH-mm-ss", Locale.getDefault())
            val folderName = dateFormat.format(Date())
            val deviceId = "${android.os.Build.MANUFACTURER}_${android.os.Build.MODEL}"
            
            // Use a counter to track uploads
            val totalFiles = logFiles.size
            val successCounter = AtomicInteger(0)
            val failureCounter = AtomicInteger(0)
            
            // Create a list to collect files that were successfully uploaded
            val successfullyUploadedFiles = mutableListOf<File>()
            
            // Use runBlocking since Worker.doWork can't be suspended
            runBlocking {
                // Upload each file
                logFiles.forEach { file ->
                    try {

                        val result = logsRepository.uploadLogFile(file, deviceId)
                        Log.i(TAG, "Successfully uploaded ${file.name}")
                        file.delete()

//                        if (result.isSuccess) {

//                            Timber.i("Successfully uploaded ${file.name}")

//                            val dateFormat = SimpleDateFormat("yyyyMMdd_HH", Locale.getDefault())
//                            val todayDateStr = dateFormat.format(Date())
//                            if(!file.name.contains(todayDateStr)){
//
//                            }
                            // Increment the success counter
                            successCounter.incrementAndGet()
//                        } else {
//                            val exception = result.exceptionOrNull() ?: Exception("Unknown error")
//                            Log.e(TAG, "Failed to upload ${file.name}: ${exception.message}")
//                            Timber.e(exception, "Failed to upload ${file.name}")
//
//                            // Increment the failure counter
//                            failureCounter.incrementAndGet()
//                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error processing file ${file.name}: ${e.message}", e)
//                        Timber.e(e, "Error processing file ${file.name}: ${e.message}")
                        failureCounter.incrementAndGet()
                    }
                }
            }

            if(failureCounter.get() == 0){
                deleteSuccessfullyUploadedFiles(logFiles.toList())
            }
            // Return true if all files were uploaded successfully
            return failureCounter.get() == 0
            
        } catch (e: Exception) {
            Log.e(TAG, "Error in uploadLogFiles: ${e.message}", e)
//            Timber.e(e, "Error in uploadLogFiles: ${e.message}")
            return false
        }
    }
    
    private fun deleteSuccessfullyUploadedFiles(files: List<File>) {
        Log.i(TAG, "Processing ${files.size} successfully uploaded files")
//        Timber.i("Processing ${files.size} successfully uploaded files")
        val filesToDelete = files
        
        filesToDelete.forEach { file ->
            try {
                if (file.exists() && file.delete()) {
                    Log.i(TAG, "Successfully deleted file: ${file.name}")
//                    Timber.i("Successfully deleted file: ${file.name}")
                } else {
                    Log.w(TAG, "Failed to delete file: ${file.name}")
//                    Timber.w("Failed to delete file: ${file.name}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error deleting file ${file.name}: ${e.message}", e)
//                Timber.e(e, "Error deleting file ${file.name}: ${e.message}")
            }
        }
    }
}