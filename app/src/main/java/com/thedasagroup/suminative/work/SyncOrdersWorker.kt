package com.thedasagroup.suminative.work

import android.content.Context
import android.util.Log
import androidx.hilt.work.HiltWorker
import androidx.work.CoroutineWorker
import androidx.work.Data
import androidx.work.WorkerParameters
import com.thedasagroup.suminative.domain.orders.SyncOrdersUseCase
import com.thedasagroup.suminative.domain.orders.SyncResult2
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import kotlinx.coroutines.flow.firstOrNull

@HiltWorker
class SyncOrdersWorker @AssistedInject constructor(
    private val syncOrdersUseCase: SyncOrdersUseCase,
    @Assisted appContext: Context,
    @Assisted params: WorkerParameters
) : CoroutineWorker(appContext, params) {

    companion object {
        private const val TAG = "SyncOrdersWorker"
        const val WORK_NAME = "sync_orders_work"
        
        // Output data keys
        const val KEY_SYNCED_COUNT = "synced_count"
        const val KEY_FAILED_COUNT = "failed_count"
        const val KEY_TOTAL_COUNT = "total_count"
        const val KEY_ERROR_MESSAGE = "error_message"
    }

    override suspend fun doWork(): Result {
        try {
            Log.i(TAG, "Starting order synchronization...")

            // Use the existing SyncOrdersUseCase to sync all unsynced orders
            val syncResult = syncOrdersUseCase.syncAllUnsyncedOrders().firstOrNull()

            return when {
                syncResult == null -> {
                    Log.e(TAG, "Sync operation returned null result")
                    createFailureResult("Sync operation failed - null result")
                }
                
                syncResult.totalCount == 0 -> {
                    Log.i(TAG, "No unsynced orders found")
                    createSuccessResult(syncResult, "No orders to sync")
                }
                
                syncResult.failedCount == 0 -> {
                    Log.i(TAG, "Successfully synced all ${syncResult.syncedCount} orders")
                    createSuccessResult(syncResult, "All orders synced successfully")
                }
                
                syncResult.syncedCount > 0 -> {
                    Log.w(TAG, "Partial sync: ${syncResult.syncedCount} succeeded, ${syncResult.failedCount} failed")
                    createPartialSuccessResult(syncResult, "Partial sync completed")
                }
                
                else -> {
                    Log.e(TAG, "All ${syncResult.failedCount} orders failed to sync")
                    createFailureResult("All orders failed to sync", syncResult)
                }
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error during order synchronization: ${e.message}", e)
            return createFailureResult("Sync failed with exception: ${e.message}")
        }
    }

    private fun createSuccessResult(syncResult: SyncResult2, message: String): Result {
        val outputData = Data.Builder()
            .putInt(KEY_SYNCED_COUNT, syncResult.syncedCount)
            .putInt(KEY_FAILED_COUNT, syncResult.failedCount)
            .putInt(KEY_TOTAL_COUNT, syncResult.totalCount)
            .build()

        Log.i(TAG, message)
        return Result.success(outputData)
    }

    private fun createPartialSuccessResult(syncResult: SyncResult2, message: String): Result {
        val outputData = Data.Builder()
            .putInt(KEY_SYNCED_COUNT, syncResult.syncedCount)
            .putInt(KEY_FAILED_COUNT, syncResult.failedCount)
            .putInt(KEY_TOTAL_COUNT, syncResult.totalCount)
            .build()

        Log.w(TAG, message)
        // Return success for partial sync since some orders were synced
        return Result.success(outputData)
    }

    private fun createFailureResult(errorMessage: String, syncResult: SyncResult2? = null): Result {
        val outputDataBuilder = Data.Builder()
            .putString(KEY_ERROR_MESSAGE, errorMessage)

        syncResult?.let { result ->
            outputDataBuilder
                .putInt(KEY_SYNCED_COUNT, result.syncedCount)
                .putInt(KEY_FAILED_COUNT, result.failedCount)
                .putInt(KEY_TOTAL_COUNT, result.totalCount)
        }

        Log.e(TAG, errorMessage)
        return Result.failure(outputDataBuilder.build())
    }
} 