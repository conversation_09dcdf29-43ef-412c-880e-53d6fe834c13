package com.thedasagroup.suminative.data.model.request.change_status

import com.thedasagroup.suminative.data.model.request.login.BankDetail
import com.thedasagroup.suminative.data.model.request.login.Brand
import com.thedasagroup.suminative.data.model.request.login.Business
import com.thedasagroup.suminative.data.model.request.login.Category
import com.thedasagroup.suminative.data.model.request.login.ChangePassword
import com.thedasagroup.suminative.data.model.request.login.Cms
import com.thedasagroup.suminative.data.model.request.login.Conversation
import com.thedasagroup.suminative.data.model.request.login.Customer
import com.thedasagroup.suminative.data.model.request.login.DeliveryAddress
import com.thedasagroup.suminative.data.model.request.login.DeliverySettingRange
import com.thedasagroup.suminative.data.model.request.login.DeliverySettings
import com.thedasagroup.suminative.data.model.request.login.Extra
import com.thedasagroup.suminative.data.model.request.login.ExtraItemRelation
import com.thedasagroup.suminative.data.model.request.login.FeedbackComplain
import com.thedasagroup.suminative.data.model.request.login.ItemStoreRelation
import com.thedasagroup.suminative.data.model.request.login.Option
import com.thedasagroup.suminative.data.model.request.login.OptionSet
import com.thedasagroup.suminative.data.model.request.login.Order
import com.thedasagroup.suminative.data.model.request.login.OrderStatus
import com.thedasagroup.suminative.data.model.request.login.PaymentData
import com.thedasagroup.suminative.data.model.request.login.PromoCodes
import com.thedasagroup.suminative.data.model.request.login.Store
import com.thedasagroup.suminative.data.model.request.login.StoreItem
import com.thedasagroup.suminative.data.model.request.login.StoreSetting
import com.thedasagroup.suminative.data.model.request.login.SupportDetail
import com.thedasagroup.suminative.data.model.request.login.UiSettings
import com.thedasagroup.suminative.data.model.request.login.User
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ChangeStatusRequest(
    @SerialName("appType") val appType: String? = null,
    @SerialName("bankDetail") val bankDetail: BankDetail? = null,
    @SerialName("brand") val brand: Brand? = null,
    @SerialName("business") val business: Business? = null,
    @SerialName("businessId") val businessId: Int? = null,
    @SerialName("category") val category: Category? = null,
    @SerialName("changePassword") val changePassword: ChangePassword? = null,
    @SerialName("cms") val cms: Cms? = null,
    @SerialName("cmsId") val cmsId: Int? = null,
    @SerialName("command") val command: String? = null,
    @SerialName("conversation") val conversation: Conversation? = null,
    @SerialName("customer") val customer: Customer? = null,
    @SerialName("deliveryAddress") val deliveryAddress: DeliveryAddress? = null,
    @SerialName("deliverySettingId") val deliverySettingId: Int? = null,
    @SerialName("deliverySettingRange") val deliverySettingRange: DeliverySettingRange? = null,
    @SerialName("deliverySettingRangeId") val deliverySettingRangeId: Int? = null,
    @SerialName("deliverySettings") val deliverySettings: DeliverySettings? = null,
    @SerialName("email") val email: String? = null,
    @SerialName("endDate") val endDate: String? = null,
    @SerialName("extra") val extra: Extra? = null,
    @SerialName("extraItemRelation") val extraItemRelation: ExtraItemRelation? = null,
    @SerialName("feedbackComplain") val feedbackComplain: FeedbackComplain? = null,
    @SerialName("itemId") val itemId: Int? = null,
    @SerialName("itemStoreRelation") val itemStoreRelation: ItemStoreRelation? = null,
    @SerialName("loggedUserId") val loggedUserId: Int? = null,
    @SerialName("option") val option: Option? = null,
    @SerialName("optionId") val optionId: Int? = null,
    @SerialName("optionSet") val optionSet: OptionSet? = null,
    @SerialName("optionSetId") val optionSetId: Int? = null,
    @SerialName("order") val order: Order? = null,
    @SerialName("orderId") val orderId: Int? = null,
    @SerialName("orderStatus") val orderStatus: OrderStatus? = null,
    @SerialName("pandaOrderId") val pandaOrderId: String? = null,
    @SerialName("paymentData") val paymentData: PaymentData? = null,
    @SerialName("phoneNumber") val phoneNumber: String? = null,
    @SerialName("promoCodes") val promoCodes: PromoCodes? = null,
    @SerialName("startDate") val startDate: String? = null,
    @SerialName("store") val store: Store? = null,
    @SerialName("storeAdminId") val storeAdminId: Int? = null,
    @SerialName("storeId") val storeId: Int? = null,
    @SerialName("storeItem") val storeItem: StoreItem? = null,
    @SerialName("storeSetting") val storeSetting: StoreSetting? = null,
    @SerialName("supportDetail") val supportDetail: SupportDetail? = null,
    @SerialName("uiSettings") val uiSettings: UiSettings? = null,
    @SerialName("user") val user: User? = null
)