package com.thedasagroup.suminative.data.repo

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.database.DatabaseManager
import com.thedasagroup.suminative.data.model.response.stock.StockItem
import com.thedasagroup.suminative.database.ProductEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext

class ProductRepository(
    private val databaseManager: DatabaseManager
) : BaseRepository() {

    private val productQueries = databaseManager.database.productQueries

    suspend fun insertProduct(stockItem: StockItem): StateFlow<Async<Boolean>> {
        val flow = MutableStateFlow<Async<Boolean>>(Loading())
        
        withContext(Dispatchers.IO) {
            try {
                val currentTime = System.currentTimeMillis()
                productQueries.insertProduct(
                    productId = stockItem.id?.toLong(),
                    name = stockItem.name ?: "",
                    description = stockItem.description,
                    additionalInfo = stockItem.additionalInfo,
                    category = stockItem.category,
                    categoryId = stockItem.categoryId?.toLong(),
                    price = stockItem.price ?: 0.0,
                    billAmount = stockItem.billAmount ?: 0.0,
                    tax = stockItem.tax ?: 0.0,
                    vat = if (stockItem.vat == true) 1L else 0L,
                    pic = stockItem.pic,
                    stock = stockItem.stock?.toLong() ?: 0L,
                    ingredients = stockItem.ingredients,
                    preparationTime = stockItem.preparationTime,
                    servingSize = stockItem.servingSize,
                    dailyCapacity = stockItem.dailyCapacity?.toLong() ?: 0L,
                    discountType = stockItem.discountType?.toLong() ?: 0L,
                    discountedAmount = stockItem.discountedAmount?.toDoubleOrNull() ?: 0.0,
                    brandId = stockItem.brandId?.toLong(),
                    businessId = stockItem.businessId?.toLong(),
                    storeId = stockItem.storeId?.toLong() ?: 0L,
                    unitId = stockItem.unitId?.toLong(),
                    createdBy = stockItem.createdBy?.toLong(),
                    createdOn = stockItem.createdOn,
                    modifiedBy = stockItem.modifiedBy?.toLong(),
                    modifiedOn = stockItem.modifiedOn,
                    createdAt = currentTime,
                    updatedAt = currentTime,
                    synced = 1L
                )
                flow.value = Success(true)
            } catch (e: Exception) {
                flow.value = Success(false)
            }
        }
        
        return flow
    }

    suspend fun insertMultipleProducts(stockItems: List<StockItem>): StateFlow<Async<Boolean>> {
        val flow = MutableStateFlow<Async<Boolean>>(Loading())
        
        withContext(Dispatchers.IO) {
            try {
                productQueries.transaction {
                    stockItems.forEach { stockItem ->
                        val currentTime = System.currentTimeMillis()
                        productQueries.insertOrReplaceProduct(
                            productId = stockItem.id?.toLong(),
                            name = stockItem.name ?: "",
                            description = stockItem.description,
                            additionalInfo = stockItem.additionalInfo,
                            category = stockItem.category,
                            categoryId = stockItem.categoryId?.toLong(),
                            price = stockItem.price ?: 0.0,
                            billAmount = stockItem.billAmount ?: 0.0,
                            tax = stockItem.tax ?: 0.0,
                            vat = if (stockItem.vat == true) 1L else 0L,
                            pic = stockItem.pic,
                            stock = stockItem.stock?.toLong() ?: 0L,
                            ingredients = stockItem.ingredients,
                            preparationTime = stockItem.preparationTime,
                            servingSize = stockItem.servingSize,
                            dailyCapacity = stockItem.dailyCapacity?.toLong() ?: 0L,
                            discountType = stockItem.discountType?.toLong() ?: 0L,
                            discountedAmount = stockItem.discountedAmount?.toDoubleOrNull() ?: 0.0,
                            brandId = stockItem.brandId?.toLong(),
                            businessId = stockItem.businessId?.toLong(),
                            storeId = stockItem.storeId?.toLong() ?: 0L,
                            unitId = stockItem.unitId?.toLong(),
                            createdBy = stockItem.createdBy?.toLong(),
                            createdOn = stockItem.createdOn,
                            modifiedBy = stockItem.modifiedBy?.toLong(),
                            modifiedOn = stockItem.modifiedOn,
                            createdAt = currentTime,
                            updatedAt = currentTime,
                            synced = 1L
                        )
                    }
                }
                flow.value = Success(true)
            } catch (e: Exception) {
                flow.value = Success(false)
            }
        }
        
        return flow
    }

    suspend fun getProductsByStore(storeId: Int): StateFlow<Async<List<ProductEntity>>> {
        val flow = MutableStateFlow<Async<List<ProductEntity>>>(Loading())
        
        withContext(Dispatchers.IO) {
            try {
                val products = productQueries.getProductsByStore(storeId.toLong()).executeAsList()
                flow.value = Success(products)
            } catch (e: Exception) {
                flow.value = Success(emptyList())
            }
        }
        
        return flow
    }

    suspend fun getInStockProductsByStore(storeId: Int): StateFlow<Async<List<ProductEntity>>> {
        val flow = MutableStateFlow<Async<List<ProductEntity>>>(Loading())
        
        withContext(Dispatchers.IO) {
            try {
                val products = productQueries.getInStockProductsByStore(storeId.toLong()).executeAsList()
                flow.value = Success(products)
            } catch (e: Exception) {
                flow.value = Success(emptyList())
            }
        }
        
        return flow
    }

    suspend fun updateProductStock(productId: Int, stock: Int): StateFlow<Async<Boolean>> {
        val flow = MutableStateFlow<Async<Boolean>>(Loading())
        
        withContext(Dispatchers.IO) {
            try {
                productQueries.updateProductStockByProductId(
                    stock = stock.toLong(),
                    updatedAt = System.currentTimeMillis(),
                    productId = productId.toLong()
                )
                flow.value = Success(true)
            } catch (e: Exception) {
                flow.value = Success(false)
            }
        }
        
        return flow
    }

    fun ProductEntity.toStockItem(): StockItem {
        return StockItem(
            id = this.productId?.toInt(),
            name = this.name,
            description = this.description,
            additionalInfo = this.additionalInfo,
            category = this.category,
            categoryId = this.categoryId?.toInt(),
            price = this.price,
            billAmount = this.billAmount,
            tax = this.tax,
            vat = this.vat == 1L,
            pic = this.pic,
            stock = this.stock?.toInt(),
            ingredients = this.ingredients,
            preparationTime = this.preparationTime,
            servingSize = this.servingSize,
            dailyCapacity = this.dailyCapacity?.toInt(),
            discountType = this.discountType?.toInt(),
            discountedAmount = this.discountedAmount?.toString(),
            brandId = this.brandId?.toInt(),
            businessId = this.businessId?.toInt(),
            storeId = this.storeId?.toInt(),
            unitId = this.unitId?.toInt(),
            createdBy = this.createdBy?.toInt(),
            createdOn = this.createdOn,
            modifiedBy = this.modifiedBy?.toInt(),
            modifiedOn = this.modifiedOn
        )
    }
} 