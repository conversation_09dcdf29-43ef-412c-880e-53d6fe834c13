package com.thedasagroup.suminative.data.repo

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.api.BASE_DOMAIN
import com.thedasagroup.suminative.data.api.BASE_URL
import com.thedasagroup.suminative.data.api.GET_ALL_ORDERS
import com.thedasagroup.suminative.data.api.GET_PENDING_ORDERS
import com.thedasagroup.suminative.data.api.GET_SCHEDULED_ORDERS
import com.thedasagroup.suminative.data.api.apiClient
import com.thedasagroup.suminative.data.model.request.change_status.ChangeStatusRequest
import com.thedasagroup.suminative.data.model.request.login.OrderRequest
import com.thedasagroup.suminative.data.model.request.notification.NotificationRequest
import com.thedasagroup.suminative.data.model.request.pagination.GetPagedOrderRequest
import com.thedasagroup.suminative.data.model.request.pagination.OrderItem
import com.thedasagroup.suminative.data.model.request.pagination.OrderResponse
import com.thedasagroup.suminative.data.model.response.change_status.ChangeStatusResponse
import com.thedasagroup.suminative.data.model.response.close_open_store.CloseOpenStoreResponse
import com.thedasagroup.suminative.data.model.response.notification.NotificationResponse
import com.thedasagroup.suminative.data.model.response.store_orders.OrdersResponse
import io.ktor.client.call.body
import io.ktor.client.request.parameter
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.contentType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext

class OrdersRepository : BaseRepository() {
    suspend fun getOrders(request: OrderRequest): StateFlow<Async<OrdersResponse>> {
        val flow = MutableStateFlow<Async<OrdersResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val ordersResponse = apiClient.post(urlString = BASE_URL) {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }.body<OrdersResponse>()
                return@safeApiCall Success(ordersResponse)
            }
            flow.value = response
        }
        return flow
    }

    suspend fun getPagedPendingOrders(request: GetPagedOrderRequest): StateFlow<Async<OrderResponse>> {
        val flow = MutableStateFlow<Async<OrderResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val orderResponse2 = safeApiCall {
                val response = apiClient.post(urlString = GET_PENDING_ORDERS) {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }
                val orderResponse = when (response.status.value) {
                    200 -> {
                        Success(OrderResponse(
                            orders = response.body<OrderResponse>().orders ?: arrayListOf(),
                            success = true
                        ))
                    }
                    204 -> {
                        Success(OrderResponse(
                            orders = arrayListOf(),
                            success = true
                        ))
                    }
                    else -> {
                        Success(OrderResponse(
                            orders = arrayListOf(),
                            success = false
                        ))
                    }
                }
                return@safeApiCall orderResponse
            }
            flow.value = orderResponse2
        }
        return flow
    }

    suspend fun getPagedAllOrders(request: GetPagedOrderRequest): StateFlow<Async<OrderResponse>> {
        val flow = MutableStateFlow<Async<OrderResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val orderResponse2 = safeApiCall {
                val response = apiClient.post(urlString = GET_ALL_ORDERS) {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }
                val orderResponse = when (response.status.value) {
                    200 -> {
                        Success(OrderResponse(
                            orders = response.body<OrderResponse>().orders ?: arrayListOf(),
                            success = true
                        ))
                    }
                    204 -> {
                        Success(OrderResponse(
                            orders = arrayListOf(),
                            success = true
                        ))
                    }
                    else -> {
                        Success(OrderResponse(
                            orders = arrayListOf(),
                            success = false
                        ))
                    }
                }
                return@safeApiCall orderResponse
            }
            flow.value = orderResponse2
        }
        return flow
    }

    suspend fun getPagedScheduleOrders(request: GetPagedOrderRequest): StateFlow<Async<OrderResponse>> {
        val flow = MutableStateFlow<Async<OrderResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val orderResponse2 = safeApiCall {
                val response = apiClient.post(urlString = GET_SCHEDULED_ORDERS) {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }
                val orderResponse = when (response.status.value) {
                    200 -> {
                        Success(OrderResponse(
                            orders = response.body<OrderResponse>().orders ?: arrayListOf(),
                            success = true
                        ))
                    }
                    204 -> {
                        Success(OrderResponse(
                            orders = arrayListOf(),
                            success = true
                        ))
                    }
                    else -> {
                        Success(OrderResponse(
                            orders = arrayListOf(),
                            success = false
                        ))
                    }
                }
                return@safeApiCall orderResponse
            }
            flow.value = orderResponse2
        }
        return flow
    }

    suspend fun changeStatus(request: ChangeStatusRequest): StateFlow<Async<ChangeStatusResponse>> {
        val flow = MutableStateFlow<Async<ChangeStatusResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val ordersResponse = apiClient.post(urlString = BASE_URL) {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }.body<ChangeStatusResponse>()
                return@safeApiCall Success(ordersResponse)
            }
            flow.value = response
        }
        return flow
    }

    suspend fun closeOpenStore(storeId : String, closed : Boolean): StateFlow<Async<CloseOpenStoreResponse>> {
        val flow = MutableStateFlow<Async<CloseOpenStoreResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val response = apiClient.post(urlString = "$BASE_DOMAIN/BackendDASA-1.0.0/store/close") {
                    contentType(ContentType.Application.Json)
                    parameter("storeId", storeId)
                    parameter("closed", closed)
                }.body<CloseOpenStoreResponse>()
                return@safeApiCall Success(response)
            }
            flow.value = response
        }
        return flow
    }

    suspend fun isClosed(storeId : String): StateFlow<Async<CloseOpenStoreResponse>> {
        val flow = MutableStateFlow<Async<CloseOpenStoreResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val response = apiClient.post(urlString = "$BASE_DOMAIN/BackendDASA-1.0.0/store/isClosed") {
                    contentType(ContentType.Application.Json)
                    parameter("storeId", storeId)
                }.body<CloseOpenStoreResponse>()
                return@safeApiCall Success(response)
            }
            flow.value = response
        }
        return flow
    }

    suspend fun addNotification(request: NotificationRequest): StateFlow<Async<NotificationResponse>> {
        val flow = MutableStateFlow<Async<NotificationResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val response = apiClient.post(urlString = "$BASE_DOMAIN/BackendDASA-1.0.0/notifications/add") {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }.body<NotificationResponse>()
                return@safeApiCall Success(response)
            }
            flow.value = response
        }
        return flow
    }
}