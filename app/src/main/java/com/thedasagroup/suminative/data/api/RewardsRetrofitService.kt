package com.thedasagroup.suminative.data.api

import com.thedasagroup.suminative.data.model.response.rewards.GetAllCustomersResponse
import com.thedasagroup.suminative.data.model.response.rewards.GetCustomersResponse
import com.thedasagroup.suminative.data.model.response.rewards.RewardsOverviewResponse
import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Query

/**
 * Retrofit service interface for Rewards API endpoints
 *
 * This service provides methods to interact with the rewards API using Retrofit.
 */
interface RewardsRetrofitService {

    /**
     * Get user points for a specific user and business
     *
     * @param userId The user ID
     * @param businessId The business ID
     * @return Response containing the user's points as a Double
     */
    @GET("BackendDASA-1.0.0/api/rewards/points")
    suspend fun getUserPoints(
        @Query("userId") userId: Int,
        @Query("businessId") businessId: Int
    ): Response<Double>

    /**
     * Get rewards overview including user points and available reward items
     *
     * @param userId The user ID
     * @param businessId The business ID
     * @return Response containing the rewards overview
     */
    @GET("BackendDASA-1.0.0/api/rewards/overview")
    suspend fun getRewardsOverview(
        @Query("userId") userId: Int,
        @Query("businessId") businessId: Int
    ): Response<RewardsOverviewResponse>

    @GET("BackendDASA-1.0.0/api/customer/getAllCustomers")
    suspend fun getAllCustomers(
        @Query("customerId") customerId: Int,
        @Query("businessId") businessId: Int
    ): Response<GetAllCustomersResponse>

    @GET("BackendDASA-1.0.0/api/customer/getCustomer")
    suspend fun getCustomer(
        @Query("customerId") customerId: Int,
        @Query("businessId") businessId: Int
    ): Response<GetCustomersResponse>
}
