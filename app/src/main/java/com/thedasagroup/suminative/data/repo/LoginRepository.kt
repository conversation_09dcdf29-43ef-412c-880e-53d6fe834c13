package com.thedasagroup.suminative.data.repo

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.api.BASE_URL
import com.thedasagroup.suminative.data.api.GET_POS_SETTINGS
import com.thedasagroup.suminative.data.api.GET_STORE_CONFIGURATIONS
import com.thedasagroup.suminative.data.api.LOGIN
import com.thedasagroup.suminative.data.api.apiClient
import com.thedasagroup.suminative.data.model.request.login.LoginRequest
import com.thedasagroup.suminative.data.model.request.login2.LoginRequest2
import com.thedasagroup.suminative.data.model.request.store_configurations.StoreConfigurationsRequest
import com.thedasagroup.suminative.data.model.request.store_settings.GetPosSettingsRequest
import com.thedasagroup.suminative.data.model.request.store_settings.StoreSettingsRequest
import com.thedasagroup.suminative.data.model.response.login.LoginResponse
import com.thedasagroup.suminative.data.model.response.store_configurations.StoreConfigurationsResponse
import com.thedasagroup.suminative.data.model.response.store_settings.StoreSettingsResponse
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.contentType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext

class LoginRepository : BaseRepository() {
    suspend fun login(loginRequest: LoginRequest): StateFlow<Async<LoginResponse>> {
        val flow = MutableStateFlow<Async<LoginResponse>>(Loading())
        withContext(Dispatchers.IO){
            val response = safeApiCall {
                val apiResponse =
                    apiClient.post(urlString = BASE_URL) {
                        contentType(ContentType.Application.Json)
                        setBody(loginRequest)
                    }
                val loginResponse : Async<LoginResponse> = when (apiResponse.status.value) {
                    204 -> {
                        return@safeApiCall Fail(error = Throwable("Invalid Username or Password"))
                    }
                    200 -> {
                        Success(apiResponse.body<LoginResponse>())
                    }
                    else -> {
                        Fail(error = Throwable("Unknown Error"))
                    }
                }
                return@safeApiCall loginResponse
            }
            flow.value = response
        }
        return flow
    }

    suspend fun login(loginRequest: LoginRequest2): StateFlow<Async<LoginResponse>> {
        val flow = MutableStateFlow<Async<LoginResponse>>(Loading())
        withContext(Dispatchers.IO){
            val response = safeApiCall {
                val apiResponse =
                    apiClient.post(urlString = LOGIN) {
                        contentType(ContentType.Application.Json)
                        setBody(loginRequest)
                    }
                val loginResponse : Async<LoginResponse> = when (apiResponse.status.value) {
                    204 -> {
                        return@safeApiCall Fail(error = Throwable("Invalid Username or Password"))
                    }
                    200 -> {
                        Success(apiResponse.body<LoginResponse>())
                    }
                    else -> {
                        Fail(error = Throwable("Unknown Error"))
                    }
                }
                return@safeApiCall loginResponse
            }
            flow.value = response
        }
        return flow
    }

    suspend fun getStoreSettings(storeSettingsRequest: StoreSettingsRequest): StateFlow<Async<StoreSettingsResponse>> {
        val flow = MutableStateFlow<Async<StoreSettingsResponse>>(Loading())
        withContext(Dispatchers.IO){
            val response = safeApiCall {
                val loginResponse =
                    apiClient.post(urlString = BASE_URL) {
                        contentType(ContentType.Application.Json)
                        setBody(storeSettingsRequest)
                    }.body<StoreSettingsResponse>()
                return@safeApiCall Success(loginResponse)
            }
            flow.value = response
        }
        return flow
    }

    suspend fun getPOSSettings(storeSettingsRequest: GetPosSettingsRequest): StateFlow<Async<StoreSettingsResponse>> {
        val flow = MutableStateFlow<Async<StoreSettingsResponse>>(Loading())
        withContext(Dispatchers.IO){
            val response = safeApiCall {
                val loginResponse =
                    apiClient.post(urlString = GET_POS_SETTINGS) {
                        contentType(ContentType.Application.Json)
                        setBody(storeSettingsRequest)
                    }.body<StoreSettingsResponse>()
                return@safeApiCall Success(loginResponse)
            }
            flow.value = response
        }
        return flow
    }

    suspend fun getStoreConfigurations(storeConfigurationsRequest: StoreConfigurationsRequest): StateFlow<Async<StoreConfigurationsResponse>> {
        val flow = MutableStateFlow<Async<StoreConfigurationsResponse>>(Loading())
        withContext(Dispatchers.IO){
            val response = safeApiCall {
                val apiResponse = apiClient.get(urlString = "$GET_STORE_CONFIGURATIONS/${storeConfigurationsRequest.storeId}")
                val storeConfigurationsResponse: Async<StoreConfigurationsResponse> = when (apiResponse.status.value) {
                    200 -> {
                        Success(apiResponse.body<StoreConfigurationsResponse>())
                    }
                    404 -> {
                        Fail(error = Throwable("Store configurations not found"))
                    }
                    else -> {
                        Fail(error = Throwable("Failed to fetch store configurations"))
                    }
                }
                return@safeApiCall storeConfigurationsResponse
            }
            flow.value = response
        }
        return flow
    }

}