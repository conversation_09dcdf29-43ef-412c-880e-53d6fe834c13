package com.thedasagroup.suminative.data.api

import android.util.Log
import com.pluto.plugins.network.ktor.PlutoKtorInterceptor
import io.ktor.client.HttpClient
import io.ktor.client.engine.android.Android
import io.ktor.client.plugins.DefaultRequest
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.defaultRequest
import io.ktor.client.plugins.logging.LogLevel
import io.ktor.client.plugins.logging.Logger
import io.ktor.client.plugins.logging.Logging
import io.ktor.client.plugins.observer.ResponseObserver
import io.ktor.client.request.accept
import io.ktor.client.request.header
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.contentType
import io.ktor.serialization.kotlinx.json.json
import kotlinx.serialization.json.Json
import com.thedasagroup.suminative.BuildConfig
import io.ktor.client.statement.bodyAsText
import kotlinx.serialization.ExperimentalSerializationApi
import timber.log.Timber

@OptIn(ExperimentalSerializationApi::class)
val logApiClient = HttpClient(Android) {

    install(ContentNegotiation) {
        json(
            Json {
                prettyPrint = true
                isLenient = true
                useAlternativeNames = true
                ignoreUnknownKeys = true
                encodeDefaults = true
                explicitNulls = false
            }
        )
    }

    install(PlutoKtorInterceptor)

    install(HttpTimeout) {
        requestTimeoutMillis = 3 * 60000
        connectTimeoutMillis = 3 * 60000
        socketTimeoutMillis = 3 * 60000
    }

//    install(Logging) {
//        logger = object : Logger {
//            override fun log(message: String) {
//                runCatching {
//                    if(BuildConfig.DEBUG){
//                        Log.v("Logger Ktor =>", message)
//                    }
//                    Timber.tag("Logger Ktor =>").v(message)
//                }
//            }
//        }
//        level = LogLevel.ALL
//    }
//
//    install(ResponseObserver) {
//        onResponse { response ->
//            runCatching{
//                if(BuildConfig.DEBUG) {
//                    Log.d("HTTP status:", "${response.status.value}")
//                }
//                if(response.status.value != 200){
//                    Timber.tag("HTTP status:").v("Error Response: ${response.status.value} - ${response.status.description}")
//                    Timber.tag("HTTP status:").v("Headers: ${response.headers}")
//                    Timber.tag("HTTP status:").v("Body: ${response.bodyAsText()}")
//                }
//                else {
//                    Timber.tag("HTTP status:").v("${response.status.value}")
//                    Timber.tag("HTTP Response Time:").v("${response.requestTime}")
//                }
//            }
//        }
//    }

    install(DefaultRequest) {
        header(HttpHeaders.ContentType, ContentType.Application.Json)
    }

    defaultRequest {
        contentType(ContentType.Application.Json)
        accept(ContentType.Application.Json)
    }
}