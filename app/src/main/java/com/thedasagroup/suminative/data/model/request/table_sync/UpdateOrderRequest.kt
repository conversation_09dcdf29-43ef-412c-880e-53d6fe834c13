package com.thedasagroup.suminative.data.model.request.table_sync

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Request model for updating order for table
 * Matches the CURL request structure for PUT /api/tables/sync/order/{tableId}
 * Note: This uses the same structure as SyncOrderRequest but for PUT operations
 */
@Serializable
data class UpdateOrderRequest(
    @SerialName("tableId")
    val tableId: Int,
    @SerialName("customerId")
    val customerId: Int,
    @SerialName("businessId")
    val businessId: Int,
    @SerialName("netPayable")
    val netPayable: Double,
    @SerialName("orderCourses")
    val orderCourses: List<OrderCourse>,
    @SerialName("goQueue")
    val goQueue: String,
    @SerialName("preparingQueue")
    val preparingQueue: String,
    @SerialName("completeQueue")
    val completeQueue: String,
    @SerialName("deviceId")
    val deviceId: String
)
