package com.thedasagroup.suminative.data.database

import app.cash.sqldelight.coroutines.asFlow
import app.cash.sqldelight.coroutines.mapToList
import app.cash.sqldelight.coroutines.mapToOneOrNull
import com.thedasagroup.suminative.database.CategoryEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class CategoryRepository @Inject constructor(
    private val databaseManager: DatabaseManager
) {
    private val database = databaseManager.database
    private val categoryQueries = database.categoryQueries

    // Category operations
    suspend fun insertCategory(category: LocalCategory): Unit = withContext(Dispatchers.IO) {
        categoryQueries.insertCategory(
            name = category.name,
            storeId = category.storeId,
            sortOrder = category.sortOrder,
            isActive = if (category.isActive) 1L else 0L,
            createdAt = category.createdAt,
            updatedAt = category.updatedAt
        )
    }

    suspend fun insertOrReplaceCategory(category: LocalCategory): Unit = withContext(Dispatchers.IO) {
        categoryQueries.insertOrReplaceCategory(
            name = category.name,
            storeId = category.storeId,
            sortOrder = category.sortOrder,
            isActive = if (category.isActive) 1L else 0L,
            createdAt = category.createdAt,
            updatedAt = category.updatedAt
        )
    }

    // Get categories for a store ordered by sort order
    fun getCategoriesByStoreFlow(storeId: Long): Flow<List<CategoryEntity>> {
        return categoryQueries.getCategoriesByStore(storeId).asFlow().mapToList(Dispatchers.IO)
    }

    suspend fun getCategoriesByStore(storeId: Long): List<CategoryEntity> = withContext(Dispatchers.IO) {
        categoryQueries.getCategoriesByStore(storeId).executeAsList()
    }

    // Get all categories including inactive
    fun getAllCategoriesByStoreFlow(storeId: Long): Flow<List<CategoryEntity>> {
        return categoryQueries.getAllCategoriesByStore(storeId).asFlow().mapToList(Dispatchers.IO)
    }

    suspend fun getAllCategoriesByStore(storeId: Long): List<CategoryEntity> = withContext(Dispatchers.IO) {
        categoryQueries.getAllCategoriesByStore(storeId).executeAsList()
    }

    // Get category by name and store
    suspend fun getCategoryByNameAndStore(name: String, storeId: Long): CategoryEntity? = withContext(Dispatchers.IO) {
        categoryQueries.getCategoryByNameAndStore(name, storeId).executeAsOneOrNull()
    }

    // Get category by id
    suspend fun getCategoryById(id: Long): CategoryEntity? = withContext(Dispatchers.IO) {
        categoryQueries.getCategoryById(id).executeAsOneOrNull()
    }

    // Update category sort order
    suspend fun updateCategorySortOrder(id: Long, sortOrder: Long): Unit = withContext(Dispatchers.IO) {
        categoryQueries.updateCategorySortOrder(sortOrder, System.currentTimeMillis(), id)
    }

    // Update category active status
    suspend fun updateCategoryActiveStatus(id: Long, isActive: Boolean): Unit = withContext(Dispatchers.IO) {
        categoryQueries.updateCategoryActiveStatus(
            if (isActive) 1L else 0L,
            System.currentTimeMillis(),
            id
        )
    }

    // Get category sort order by name
    suspend fun getCategorySortOrderByName(name: String, storeId: Long): Long? = withContext(Dispatchers.IO) {
        categoryQueries.getCategorySortOrderByName(name, storeId).executeAsOneOrNull()
    }

    // Update multiple categories sort order
    suspend fun updateCategoriesSortOrder(categories: List<Pair<String, Long>>, storeId: Long): Unit = withContext(Dispatchers.IO) {
        categories.forEach { (categoryName, sortOrder) ->
            categoryQueries.updateCategoriesSortOrder(
                sortOrder = sortOrder,
                updatedAt = System.currentTimeMillis(),
                name = categoryName,
                storeId = storeId
            )
        }
    }

    // Sync categories from API response
    suspend fun syncCategoriesFromApi(categories: List<String>, storeId: Long): Unit = withContext(Dispatchers.IO) {
        val currentTime = System.currentTimeMillis()
        
        // Insert or update categories with their sort order
        categories.forEachIndexed { index, categoryName ->
            val category = LocalCategory(
                name = categoryName,
                storeId = storeId,
                sortOrder = index.toLong(),
                isActive = true,
                createdAt = currentTime,
                updatedAt = currentTime
            )
            insertOrReplaceCategory(category)
        }
        
        // Mark categories as synced
        markCategoriesSynced(storeId)
    }

    // Delete category
    suspend fun deleteCategoryById(id: Long): Unit = withContext(Dispatchers.IO) {
        categoryQueries.deleteCategoryById(id)
    }

    // Delete all categories for a store
    suspend fun deleteCategoriesByStore(storeId: Long): Unit = withContext(Dispatchers.IO) {
        categoryQueries.deleteCategoriesByStore(storeId)
    }

    // Get categories count
    suspend fun getCategoriesCountByStore(storeId: Long): Long = withContext(Dispatchers.IO) {
        categoryQueries.getCategoriesCountByStore(storeId).executeAsOneOrNull() ?: 0L
    }

    // Get unsynced categories
    suspend fun getUnsyncedCategories(): List<CategoryEntity> = withContext(Dispatchers.IO) {
        categoryQueries.getUnsyncedCategories().executeAsList()
    }

    // Mark categories as synced
    suspend fun markCategoriesSynced(storeId: Long): Unit = withContext(Dispatchers.IO) {
        categoryQueries.markCategoriesSynced(System.currentTimeMillis(), updatedAt = System.currentTimeMillis(), storeId = storeId)
    }

    // Update category sync timestamp
    suspend fun updateCategorySyncedAt(id: Long): Unit = withContext(Dispatchers.IO) {
        categoryQueries.updateCategorySyncedAt(System.currentTimeMillis(), updatedAt = System.currentTimeMillis(), id)
    }

    // Helper method to get category names in sorted order
    suspend fun getCategoryNamesSorted(storeId: Long): List<String> = withContext(Dispatchers.IO) {
        getCategoriesByStore(storeId).map { it.name }
    }

    // Check if categories exist for store
    suspend fun hasCategoriesForStore(storeId: Long): Boolean = withContext(Dispatchers.IO) {
        getCategoriesCountByStore(storeId) > 0
    }
}

// Data class for local category operations
data class LocalCategory(
    val name: String,
    val storeId: Long,
    val sortOrder: Long = 0L,
    val isActive: Boolean = true,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) 