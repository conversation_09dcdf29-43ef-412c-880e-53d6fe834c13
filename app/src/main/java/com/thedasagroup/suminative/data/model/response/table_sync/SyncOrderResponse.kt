package com.thedasagroup.suminative.data.model.response.table_sync

import com.thedasagroup.suminative.data.model.response.store_orders.Cart
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json

/**
 * Response model for table sync order API
 * Matches both POST and GET response structures
 */
@Serializable
data class SyncOrderResponse(
    @SerialName("success")
    val success: <PERSON><PERSON><PERSON>,
    @SerialName("message")
    val message: String,
    @SerialName("data")
    val data: SyncOrderData?,
    @SerialName("statusCode")
    val statusCode: Int
)

/**
 * Sync order data model
 */
@Serializable
data class SyncOrderData(
    @SerialName("id")
    val id: Int,
    @SerialName("tableId")
    val tableId: Int,
    @SerialName("orderCourses")
    val orderCourses: List<SyncOrderCourse>,
    @SerialName("netPayable")
    val netPayable: Double,
    @SerialName("customerId")
    val customerId: Int,
    @SerialName("businessId")
    val businessId: Int,
    @SerialName("goQueue")
    val goQueue: String? = null,
    @SerialName("preparingQueue")
    val preparingQueue: String?= null,
    @SerialName("completeQueue")
    val completeQueue: String? = null
)

/**
 * Sync order course data model for response
 */
@Serializable
data class SyncOrderCourse(
    @SerialName("id")
    val id: Int? = 0,
    @SerialName("coursesName")
    val coursesName: String? = "Course 1",
    @SerialName("courseStatus")
    val courseStatus : Int? = 0 ,
    @SerialName("cartJson")
    val cartJson: String? = null,
    @SerialName("courseSortOrder")
    val sortOrder : Int = 0
) {
    fun getCart(): List<com.thedasagroup.suminative.data.model.request.order.Cart> {
        return cartJson?.let {
            val json = Json {
                prettyPrint = true
                isLenient = true
                useAlternativeNames = true
                ignoreUnknownKeys = true
                encodeDefaults = true
                explicitNulls = false
            }
            json.decodeFromString(it)
        } ?: emptyList()
    }
}
