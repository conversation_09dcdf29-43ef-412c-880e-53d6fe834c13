package com.thedasagroup.suminative.data.repo

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.api.COURSES_NOTIFICATION
import com.thedasagroup.suminative.data.api.PRINT_BILL
import com.thedasagroup.suminative.data.api.SEND_TO_KITCHEN
import com.thedasagroup.suminative.data.api.apiClient
import com.thedasagroup.suminative.data.model.request.print.CoursesNotificationRequest
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.print.PrintBillRequest
import com.thedasagroup.suminative.data.model.request.print.SendToKitchenRequest
import com.thedasagroup.suminative.data.model.response.courses_notification.CoursesNotificationErrorResponse
import com.thedasagroup.suminative.data.model.response.courses_notification.CoursesNotificationResponse
import io.ktor.client.call.body
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.contentType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json

class PrintRepository : BaseRepository() {

    /**
     * Send courses notification to printer
     * @param request The courses notification request containing storeId, courseName, tableName, and cartJson
     * @return StateFlow with the API response
     */
    suspend fun sendCoursesNotification(
        request: CoursesNotificationRequest
    ): StateFlow<Async<CoursesNotificationResponse>> {
        val flow = MutableStateFlow<Async<CoursesNotificationResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val httpResponse = apiClient.post(urlString = COURSES_NOTIFICATION) {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }

                return@safeApiCall when (httpResponse.status.value) {
                    in 200..299 -> {
                        Success(httpResponse.body<CoursesNotificationResponse>())
                    }
                    500 -> {
                        // Handle the specific 500 error response format
                        val errorResponse = httpResponse.body<CoursesNotificationErrorResponse>()
                        Fail(
                            error = Throwable(
                                errorResponse.message ?: "Internal Server Error"
                            )
                        )
                    }
                    else -> {
                        Fail(
                            error = Throwable("HTTP ${httpResponse.status.value}: ${httpResponse.status.description}")
                        )
                    }
                }
            }
            flow.value = response
        }
        return flow
    }


    suspend fun printBill(
        request: PrintBillRequest
    ): StateFlow<Async<CoursesNotificationResponse>> {
        val flow = MutableStateFlow<Async<CoursesNotificationResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val httpResponse = apiClient.post(urlString = PRINT_BILL) {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }

                return@safeApiCall when (httpResponse.status.value) {
                    in 200..299 -> {
                        Success(httpResponse.body<CoursesNotificationResponse>())
                    }
                    500 -> {
                        // Handle the specific 500 error response format
                        val errorResponse = httpResponse.body<CoursesNotificationErrorResponse>()
                        Fail(
                            error = Throwable(
                                errorResponse.message ?: "Internal Server Error"
                            )
                        )
                    }
                    else -> {
                        Fail(
                            error = Throwable("HTTP ${httpResponse.status.value}: ${httpResponse.status.description}")
                        )
                    }
                }
            }
            flow.value = response
        }
        return flow
    }


    suspend fun sendToKitchen(
        request: SendToKitchenRequest
    ): StateFlow<Async<CoursesNotificationResponse>> {
        val flow = MutableStateFlow<Async<CoursesNotificationResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val httpResponse = apiClient.post(urlString = SEND_TO_KITCHEN) {
                    contentType(ContentType.Application.Json)
                    setBody(request.listItems)
                }

                return@safeApiCall when (httpResponse.status.value) {
                    in 200..299 -> {
                        Success(httpResponse.body<CoursesNotificationResponse>())
                    }
                    500 -> {
                        // Handle the specific 500 error response format
                        val errorResponse = httpResponse.body<CoursesNotificationErrorResponse>()
                        Fail(
                            error = Throwable(
                                errorResponse.message ?: "Internal Server Error"
                            )
                        )
                    }
                    else -> {
                        Fail(
                            error = Throwable("HTTP ${httpResponse.status.value}: ${httpResponse.status.description}")
                        )
                    }
                }
            }
            flow.value = response
        }
        return flow
    }

    /**
     * Send multiple courses to kitchen at once
     * @param requests List of SendToKitchenRequest objects for multiple courses
     * @return StateFlow with the API response
     */
    suspend fun sendMultipleCoursesToKitchen(
        requests: List<SendToKitchenRequest>
    ): StateFlow<Async<CoursesNotificationResponse>> {
        val flow = MutableStateFlow<Async<CoursesNotificationResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val httpResponse = apiClient.post(urlString = SEND_TO_KITCHEN) {
                    contentType(ContentType.Application.Json)
                    setBody(requests)
                }

                return@safeApiCall when (httpResponse.status.value) {
                    in 200..299 -> {
                        Success(httpResponse.body<CoursesNotificationResponse>())
                    }
                    500 -> {
                        // Handle the specific 500 error response format
                        val errorResponse = httpResponse.body<CoursesNotificationErrorResponse>()
                        Fail(
                            error = Throwable(
                                errorResponse.message ?: "Internal Server Error"
                            )
                        )
                    }
                    else -> {
                        Fail(
                            error = Throwable("HTTP ${httpResponse.status.value}: ${httpResponse.status.description}")
                        )
                    }
                }
            }
            flow.value = response
        }
        return flow
    }

    /**
     * Send courses notification to printer (convenience method)
     * @param storeId The store ID
     * @param courseName The name of the course (e.g., "Starters", "Mains", "Desserts")
     * @param tableName The table name (e.g., "T-12")
     * @param cartJson The cart data as JSON string
     * @return StateFlow with the API response
     */
    suspend fun sendCoursesNotification(
        storeId: Int,
        courseName: String,
        tableName: String,
        cartJson: String
    ): StateFlow<Async<CoursesNotificationResponse>> {
        val request = CoursesNotificationRequest(
            storeId = storeId,
            courseName = courseName,
            tableName = tableName,
            cartJson = cartJson
        )
        return sendCoursesNotification(request)
    }

    /**
     * Send courses notification to printer with Cart list (convenience method)
     * @param storeId The store ID
     * @param courseName The name of the course (e.g., "Starters", "Mains", "Desserts")
     * @param tableName The table name (e.g., "T-12")
     * @param carts The list of cart items to be converted to JSON
     * @return StateFlow with the API response
     */
    suspend fun sendCoursesNotification(
        storeId: Int,
        courseName: String,
        tableName: String,
        carts: List<Cart>
    ): StateFlow<Async<CoursesNotificationResponse>> {
        val cartJson = convertCartsToJson(carts)
        return sendCoursesNotification(storeId, courseName, tableName, cartJson)
    }

    /**
     * Convert a list of Cart objects to JSON string
     * @param carts The list of cart items
     * @return JSON string representation of the carts
     */
    fun convertCartsToJson(carts: List<Cart>): String {
        val json = Json {
            prettyPrint = false
            isLenient = true
            useAlternativeNames = true
            ignoreUnknownKeys = true
            encodeDefaults = true
            explicitNulls = false
        }
        return json.encodeToString(carts)
    }
}