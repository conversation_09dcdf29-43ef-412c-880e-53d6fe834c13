package com.thedasagroup.suminative.data.model.response.store_orders

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Customer(
    @SerialName("businessId")
    val businessId: Int? = null,
    @SerialName("deliveryAddresses")
    val deliveryAddresses: List<DeliveryAddress>? = mutableListOf(),
    @SerialName("dob")
    val dob: String? = null,
    @SerialName("email")
    val email: String? = null,
    @SerialName("facebookToken")
    val facebookToken: String? = null,
    @SerialName("gender")
    val gender: String? = null,
    @SerialName("googleToken")
    val googleToken: String? = null,
    @SerialName("id")
    val id: Int? = null,
    @SerialName("isEmailVerified")
    val isEmailVerified: Int? = null,
    @SerialName("isMobileVerified")
    val isMobileVerified: Int? = null,
    @SerialName("isSocialUser")
    val isSocialUser: Int? = null,
    @SerialName("name")
    val name: String? = null,
    @SerialName("password")
    val password: String? = null,
    @SerialName("phone")
    val phone: String? = null,
    @SerialName("pic")
    val pic: String? = null,
    @SerialName("status")
    val status: Int? = null,
    @SerialName("storeId")
    val storeId: Int? = null,
    @SerialName("storeToken")
    val stripToken: String? = null,
    @SerialName("type")
    val type: Int? = null,
)