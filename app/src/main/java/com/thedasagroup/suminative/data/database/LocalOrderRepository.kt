package com.thedasagroup.suminative.data.database

import app.cash.sqldelight.coroutines.asFlow
import app.cash.sqldelight.coroutines.mapToList
import app.cash.sqldelight.coroutines.mapToOneOrNull
import com.thedasagroup.suminative.database.OrderEntity
import com.thedasagroup.suminative.database.OrderItemEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class LocalOrderRepository @Inject constructor(
    private val databaseManager: DatabaseManager
) {
    private val database = databaseManager.database
    private val orderQueries = database.orderQueries
    private val orderItemQueries = database.orderItemQueries

    // Order operations
    suspend fun insertOrder(order: LocalOrder): Unit = withContext(Dispatchers.IO) {
        orderQueries.insertOrder(
            orderId = order.orderId,
            customerId = order.customerId,
            customerName = order.customerName,
            customerPhone = order.customerPhone,
            orderType = order.orderType,
            status = order.status,
            subtotal = order.subtotal,
            tax = order.tax,
            discount = order.discount,
            total = order.total,
            paymentMethod = order.paymentMethod,
            paymentStatus = order.paymentStatus,
            notes = order.notes,
            tableNumber = order.tableNumber,
            deliveryAddress = order.deliveryAddress,
            createdAt = order.createdAt,
            updatedAt = order.updatedAt,
            storeId = order.storeId,
            waiterId = order.waiterId,
            waiterName = order.waiterName,
            synced = if (order.synced) 1L else 0L
        )
    }

    fun getAllOrdersFlow(): Flow<List<OrderEntity>> {
        return orderQueries.getAllOrders().asFlow().mapToList(Dispatchers.IO)
    }

    suspend fun getOrderById(id: Long): OrderEntity? = withContext(Dispatchers.IO) {
        orderQueries.getOrderById(id).executeAsOneOrNull()
    }

    suspend fun getOrderByOrderId(orderId: String): OrderEntity? = withContext(Dispatchers.IO) {
        orderQueries.getOrderByOrderId(orderId).executeAsOneOrNull()
    }

    fun getOrdersByStatusFlow(status: String): Flow<List<OrderEntity>> {
        return orderQueries.getOrdersByStatus(status).asFlow().mapToList(Dispatchers.IO)
    }

    fun getOrdersByStoreFlow(storeId: Long): Flow<List<OrderEntity>> {
        return orderQueries.getOrdersByStore(storeId).asFlow().mapToList(Dispatchers.IO)
    }

    fun getPendingOrdersFlow(): Flow<List<OrderEntity>> {
        return orderQueries.getPendingOrders().asFlow().mapToList(Dispatchers.IO)
    }

    // Sync-related methods
    fun getUnsyncedOrdersFlow(): Flow<List<OrderEntity>> {
        return orderQueries.getUnsyncedOrders().asFlow().mapToList(Dispatchers.IO)
    }

    suspend fun getUnsyncedOrders(): List<OrderEntity> = withContext(Dispatchers.IO) {
        orderQueries.getUnsyncedOrders().executeAsList()
    }

    fun getSyncedOrdersFlow(): Flow<List<OrderEntity>> {
        return orderQueries.getSyncedOrders().asFlow().mapToList(Dispatchers.IO)
    }

    suspend fun getUnsyncedOrderCount(): Long = withContext(Dispatchers.IO) {
        orderQueries.getUnsyncedOrderCount().executeAsOneOrNull() ?: 0L
    }

    suspend fun markOrderSynced(id: Long): Unit = withContext(Dispatchers.IO) {
        orderQueries.markOrderSynced(System.currentTimeMillis(), id)
    }

    suspend fun markOrderUnsynced(id: Long): Unit = withContext(Dispatchers.IO) {
        orderQueries.markOrderUnsynced(System.currentTimeMillis(), id)
    }

    suspend fun markOrdersSyncedByOrderIds(orderIds: List<String>): Unit = withContext(Dispatchers.IO) {
        orderQueries.markOrdersSyncedByOrderIds(System.currentTimeMillis(), orderIds)
    }

    // Existing methods
    suspend fun updateOrderStatus(id: Long, status: String): Unit = withContext(Dispatchers.IO) {
        orderQueries.updateOrderStatus(status, System.currentTimeMillis(), id)
    }

    suspend fun updateOrderPayment(
        id: Long,
        paymentMethod: String,
        paymentStatus: String
    ): Unit = withContext(Dispatchers.IO) {
        orderQueries.updateOrderPayment(paymentMethod, paymentStatus, System.currentTimeMillis(), id)
    }

    suspend fun markOrderComplete(id: Long): Unit = withContext(Dispatchers.IO) {
        val now = System.currentTimeMillis()
        orderQueries.markOrderComplete(now, now, id)
    }

    suspend fun deleteOrder(id: Long): Unit = withContext(Dispatchers.IO) {
        orderQueries.deleteOrder(id)
    }

//    suspend fun getTotalSales(startTime: Long, endTime: Long, storeId: Long): Double = withContext(Dispatchers.IO) {
//        orderQueries.getTotalSalesByDateRange(startTime, endTime, storeId).executeAsOneOrNull() ?: 0.0
//    }

    // Order Item operations
    suspend fun insertOrderItem(orderItem: LocalOrderItem): Unit = withContext(Dispatchers.IO) {
        orderItemQueries.insertOrderItem(
            orderId = orderItem.orderId,
            itemId = orderItem.itemId,
            itemName = orderItem.itemName,
            itemDescription = orderItem.itemDescription,
            quantity = orderItem.quantity,
            unitPrice = orderItem.unitPrice,
            totalPrice = orderItem.totalPrice,
            category = orderItem.category,
            modifiers = orderItem.modifiers,
            notes = orderItem.notes,
            createdAt = orderItem.createdAt
        )
    }

    fun getOrderItemsByOrderIdFlow(orderId: String): Flow<List<OrderItemEntity>> {
        return orderItemQueries.getOrderItemsByOrderId(orderId).asFlow().mapToList(Dispatchers.IO)
    }

    suspend fun getOrderItemsByOrderId(orderId: String): List<OrderItemEntity> = withContext(Dispatchers.IO) {
        orderItemQueries.getOrderItemsByOrderId(orderId).executeAsList()
    }

    suspend fun getOrderItemById(id: Long): OrderItemEntity? = withContext(Dispatchers.IO) {
        orderItemQueries.getOrderItemById(id).executeAsOneOrNull()
    }

    suspend fun updateOrderItemQuantity(
        id: Long,
        quantity: Long,
        totalPrice: Double,
        modifiers: String?
    ): Unit = withContext(Dispatchers.IO) {
        orderItemQueries.updateOrderItemQuantity(quantity, totalPrice, modifiers, id)
    }

    suspend fun deleteOrderItem(id: Long): Unit = withContext(Dispatchers.IO) {
        orderItemQueries.deleteOrderItem(id)
    }

    suspend fun deleteOrderItemsByOrderId(orderId: String): Unit = withContext(Dispatchers.IO) {
        orderItemQueries.deleteOrderItemsByOrderId(orderId)
    }

//    suspend fun getOrderItemTotal(orderId: String): Double = withContext(Dispatchers.IO) {
//        orderItemQueries.getOrderItemTotal(orderId).executeAsOneOrNull() ?: 0.0
//    }
}

// Data classes for local operations
data class LocalOrder(
    val orderId: String,
    val customerId: Long? = null,
    val customerName: String? = null,
    val customerPhone: String? = null,
    val orderType: String,
    val status: String,
    val subtotal: Double = 0.0,
    val tax: Double = 0.0,
    val discount: Double = 0.0,
    val total: Double = 0.0,
    val paymentMethod: String? = null,
    val paymentStatus: String = "PENDING",
    val notes: String? = null,
    val tableNumber: Long? = null,
    val deliveryAddress: String? = null,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val storeId: Long,
    val waiterId: Long? = null,
    val waiterName: String? = null,
    val synced: Boolean = false
)

data class LocalOrderItem(
    val orderId: String,
    val itemId: Long,
    val itemName: String,
    val itemDescription: String? = null,
    val quantity: Long = 1,
    val unitPrice: Double,
    val totalPrice: Double,
    val category: String? = null,
    val modifiers: String? = null,
    val notes: String? = null,
    val createdAt: Long = System.currentTimeMillis()
) 