package com.thedasagroup.suminative.data.model.request.order

import android.os.Parcelable
import kotlinx.android.parcel.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json

@Serializable
@Parcelize
data class Order(
    @SerialName("businessId")
    val businessId: Int? = 0,
    @SerialName("carts")
    val carts: List<Cart>? = emptyList(),
    @SerialName("createdBy")
    val createdBy: Int? = 0,
    @SerialName("createdOn")
    val createdOn: String? = null,
    @SerialName("customer")
    val customer: Customer? = null,
    @SerialName("customerId")
    val customerId: Int? = 0,
    @SerialName("deliveryAddress")
    val deliveryAddress: DeliveryAddress? = null,
    @SerialName("deliveryCharges")
    val deliveryCharges: Double? = 0.0,
    @SerialName("deliveryNote")
    val deliveryNote: String? = null,
    @SerialName("deliveryType")
    val deliveryType: Int? = 0,
    @SerialName("discountONPromo")
    val discountONPromo: Double? = 0.0,
    @SerialName("guest")
    val guest: Boolean? = false,
    @SerialName("id")
    val id: Int? = -1,
    @SerialName("isPromoCodeAvailed")
    val isPromoCodeAvailed: Boolean? = false,
    @SerialName("lastUpdatedFromPanda")
    val lastUpdatedFromPanda: String? = null,
    @SerialName("modifiedBy")
    val modifiedBy: Int? = 0,
    @SerialName("modifiedOn")
    val modifiedOn: String? = null,
    @SerialName("netPayable")
    val netPayable: Double? = 0.0,
    @SerialName("orderStatusHistory")
    val orderStatusHistory: List<Order>? = emptyList(),
    @SerialName("pandaOrderDetail")
    val pandaOrderDetail: PandaOrderDetail? = null,
    @SerialName("pandaOrderId")
    val pandaOrderId: String? = null,
    @SerialName("paymentId")
    val paymentId: Int? = 0,
    @SerialName("paymentType")
    val paymentType: Int? = 0,
    @SerialName("pickupTime")
    val pickupTime: String? = null,
    @SerialName("priceAfterPromo")
    val priceAfterPromo: Int? = 0,
    @SerialName("priceBeforePromo")
    val priceBeforePromo: Int? = 0,
    @SerialName("promoCode")
    val promoCode: String? = null,
    @SerialName("scheduled")
    val scheduled: Boolean? = false,
    @SerialName("scheduledDateTime")
    val scheduledDateTime: String? = null,
    @SerialName("status")
    val status: Int? = 0,
    @SerialName("storeId")
    val storeId: Int? = 0,
    @SerialName("tax")
    val tax: Double? = 0.0,
    @SerialName("totalDiscount")
    val totalDiscount: Double? = 0.0,
    @SerialName("totalExtraPrice")
    val totalExtraPrice: Double? = 0.0,
    @SerialName("totalOptionPrice")
    val totalOptionPrice: Double? = 0.0,
    @SerialName("totalPrice")
    val totalPrice: Double? = 0.0,
    @SerialName("trackingUrl")
    val trackingUrl: String? = null,
    @SerialName("transactionId")
    val transactionId: String? = null,
    @SerialName("orderNotes")
    val orderNotes: String? = null,
) : Parcelable {
    fun net(): Double {
        return carts?.sumByDouble {
            calculateTotal(
                stockItem = it.storeItem ?: StoreItem(),
                optionSets = it.storeItem?.optionSets ?: mutableListOf(),
                updatedStock = it.quantity ?: 0
            ).billAmount ?: 0.0
        } ?: 0.0
    }

    fun totalPrice(applyServiceCharge: Boolean, serviceChargePercentage : Double): Double {
        val serviceChargeAmount = if (applyServiceCharge) {
            net() * (serviceChargePercentage / 100.0)
        } else {
            0.0
        }
        val totalPrice = net() + (tax ?: 0.0) + serviceChargeAmount
        return totalPrice
    }
}

fun List<Cart>.encode(): String {
    val json = Json {
        prettyPrint = true
        isLenient = true
        useAlternativeNames = true
        ignoreUnknownKeys = true
        encodeDefaults = true
        explicitNulls = false
    }
    return json.encodeToString(this)
}

fun calculateTotal(
    stockItem: StoreItem, optionSets: List<OptionSet>, updatedStock: Int
): StoreItem {
    if (optionSets?.isNotEmpty() == true) {
        val optionPrice = optionSets.flatMap { optionSet ->
            optionSet.options.map { option ->
                if (option?.optionchecked == true) {
                    (option.price ?: 0.0) * (option.quantity?.toDouble() ?: 0.0)
                } else {
                    0.0
                }
            }
        }.sum()
        val price = stockItem.price ?: 0.0
        val tax = stockItem.tax ?: 0.0
        val discount = stockItem.discountedAmount ?: 0.0
        val total = (price + tax - discount + optionPrice) * (updatedStock)
        return stockItem.copy(
            price = price, tax = tax, discountedAmount = discount, billAmount = total
        )
    } else {
        val price = stockItem.price ?: 0.0
        val tax = stockItem.tax ?: 0.0
        val discount = stockItem.discountedAmount ?: 0.0
        val total = (price + tax - discount) * (updatedStock)
        return stockItem.copy(
            price = price, tax = tax, discountedAmount = discount, billAmount = total
        )
    }
}