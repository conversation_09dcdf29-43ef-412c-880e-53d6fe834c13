package com.thedasagroup.suminative.data.model.request.pagination

import com.thedasagroup.suminative.data.model.response.store_orders.Order
import com.thedasagroup.suminative.data.model.response.store_orders.Order2
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class OrderItem(
    @SerialName("customer")
    val customer: Customer? = null,
    @SerialName("order")
    val order: Order? = null
)


data class OrderItem2(
    @SerialName("customer")
    val customer: Customer? = null,
    @SerialName("order")
    val order: Order2? = null
)