package com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@Parcelize
data class GuavaOrder(
    @SerialName("additionalAmount")
    val additionalAmount: String?= null,
    @SerialName("cashbackAmount")
    val cashbackAmount: String?= null,
    @SerialName("ccy")
    val ccy: String?= null,
    @SerialName("clientReference")
    val clientReference: String?= null,
    @SerialName("createdAt")
    val createdAt: String?= null,
    @SerialName("description")
    val description: String?= null,
    @SerialName("device")
    val device: String?= null,
    @SerialName("expireAt")
    val expireAt: String?= null,
    @SerialName("id")
    val id: String?=null,
    @SerialName("ipAddress")
    val ipAddress: String?= null,
    @SerialName("merchantId")
    val merchantId: String?= null,
    @SerialName("paymentMethod")
    val paymentMethod: String?= null,
    @SerialName("serviceChargeAmount")
    val serviceChargeAmount: String?= null,
    @SerialName("status")
    val status: String?= null,
    @SerialName("taxAmount")
    val taxAmount: String?= null,
    @SerialName("taxId")
    val taxId: String?= null,
    @SerialName("taxName")
    val taxName: String?= null,
    @SerialName("terminalId")
    val terminalId: String?= null,
    @SerialName("tipsAmount")
    val tipsAmount: String?= null,
    @SerialName("totalAmount")
    val totalAmount: String?= null,
    @SerialName("transactionAmount")
    val transactionAmount: String?= null,
    @SerialName("transactionType")
    val transactionType: String?= null,
    @SerialName("updatedAt")
    val updatedAt: String?= null,
    @SerialName("userAgent")
    val userAgent: String?= null,
) : Parcelable