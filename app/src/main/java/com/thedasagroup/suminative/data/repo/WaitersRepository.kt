package com.thedasagroup.suminative.data.repo

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.api.BASE_DOMAIN
import com.thedasagroup.suminative.data.api.apiClient
import com.thedasagroup.suminative.data.model.response.login.User
import io.ktor.client.call.body
import io.ktor.client.request.parameter
import io.ktor.client.request.post
import io.ktor.http.ContentType
import io.ktor.http.contentType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext

class WaitersRepository : BaseRepository() {
    suspend fun getWaitersList(storeId : String): StateFlow<Async<List<User>>> {
        val flow = MutableStateFlow<Async<List<User>>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val response = apiClient.post(urlString = "$BASE_DOMAIN/BackendDASA-1.0.0/login/getAllWaiters") {
                    contentType(ContentType.Application.Json)
                    parameter("storeId", storeId)
                }.body<List<User>>()
                return@safeApiCall Success(response)
            }
            flow.value = response
        }
        return flow
    }
}