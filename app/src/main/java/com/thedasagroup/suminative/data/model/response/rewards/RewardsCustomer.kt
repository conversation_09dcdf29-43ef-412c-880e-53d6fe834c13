package com.thedasagroup.suminative.data.model.response.rewards

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class RewardsCustomer(
    @SerialName("id")
    val id: Int? = null,
    @SerialName("businessId")
    val businessId: Int? = null,
    @SerialName("name")
    val name: String? = null,
    @SerialName("email")
    val email: String? = null,
    @SerialName("phone")
    val phone: String? = null,
    @SerialName("password")
    val password: String? = null,
    @SerialName("googleToken")
    val googleToken: String? = null,
    @SerialName("facebookToken")
    val facebookToken: String? = null,
    @SerialName("stripToken")
    val stripToken: String? = null,
    @SerialName("pic")
    val pic: String? = null,
    @SerialName("isMobileVerified")
    val isMobileVerified: Boolean? = null,
    @SerialName("isEmailVerified")
    val isEmailVerified: Boolean? = null,
    @SerialName("deliveryAddresses")
    val deliveryAddresses: String? = null,
    @SerialName("json")
    val json: String? = null,
    @SerialName("isSocialUser")
    val isSocialUser: Int? = null,
    @SerialName("type")
    val type: Int? = null
)
