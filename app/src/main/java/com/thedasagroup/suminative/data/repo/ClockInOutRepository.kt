package com.thedasagroup.suminative.data.repo

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.api.STORE_USER_LOGIN
import com.thedasagroup.suminative.data.api.CLOCK_IN_USER_TIME
import com.thedasagroup.suminative.data.api.CLOCK_OUT_USER_TIME
import com.thedasagroup.suminative.data.api.apiClient
import com.thedasagroup.suminative.data.model.request.login.StoreUserLoginRequest
import com.thedasagroup.suminative.data.model.request.login.ClockInUserTimeRequest
import com.thedasagroup.suminative.data.model.request.login.ClockOutUserTimeRequest
import com.thedasagroup.suminative.data.model.response.login.LoginResponse
import com.thedasagroup.suminative.data.model.response.waiter_errors.WaiterErrorResponse
import io.ktor.client.call.body
import io.ktor.client.request.forms.submitForm
import io.ktor.http.Parameters
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext

class ClockInOutRepository : BaseRepository() {
    
    suspend fun storeUserLogin(request: StoreUserLoginRequest): StateFlow<Async<LoginResponse>> {
        val flow = MutableStateFlow<Async<LoginResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val apiResponse = apiClient.submitForm(
                    url = STORE_USER_LOGIN,
                    formParameters = Parameters.build {
                        append("userPin", request.userPin)
                        append("storeId", request.storeId.toString())
                    }
                )
                val loginResponse: Async<LoginResponse> = when (apiResponse.status.value) {
                    200 -> {
                        if(apiResponse.body<LoginResponse>().success == true) {
                            Success(apiResponse.body<LoginResponse>())
                        }
                        else {
                            Fail(error = Throwable(apiResponse.body<WaiterErrorResponse>().message ?: "Unable to connect to server"))
                        }
                    }
                    else -> {
                        Fail(error = Throwable("Unknown Error"))
                    }
                }
                return@safeApiCall loginResponse
            }
            flow.value = response
        }
        return flow
    }
    
    suspend fun clockInUserTime(request: ClockInUserTimeRequest): StateFlow<Async<LoginResponse>> {
        val flow = MutableStateFlow<Async<LoginResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val apiResponse = apiClient.submitForm(
                    url = CLOCK_IN_USER_TIME,
                    formParameters = Parameters.build {
                        append("userPin", request.userPin)
                        append("storeId", request.storeId.toString())
                    }
                )
                val loginResponse: Async<LoginResponse> = when (apiResponse.status.value) {
                    200 -> {
                        if(apiResponse.body<LoginResponse>().success == true) {
                            Success(apiResponse.body<LoginResponse>())
                        }
                        else {
                            Fail(error = Throwable(apiResponse.body<WaiterErrorResponse>().message ?: "Unable to connect to server"))
                        }
                    }
                    else -> {
                        Fail(error = Throwable("Clock In Failed"))
                    }
                }
                return@safeApiCall loginResponse
            }
            flow.value = response
        }
        return flow
    }
    
    suspend fun clockOutUserTime(request: ClockOutUserTimeRequest): StateFlow<Async<LoginResponse>> {
        val flow = MutableStateFlow<Async<LoginResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val apiResponse = apiClient.submitForm(
                    url = CLOCK_OUT_USER_TIME,
                    formParameters = Parameters.build {
                        append("userPin", request.userPin)
                        append("storeId", request.storeId.toString())
                    }
                )
                val loginResponse: Async<LoginResponse> = when (apiResponse.status.value) {
                    200 -> {
                        if(apiResponse.body<LoginResponse>().success == true) {
                            Success(apiResponse.body<LoginResponse>())
                        }
                        else {
                            Fail(error = Throwable(apiResponse.body<WaiterErrorResponse>().message ?: "Unable to connect to server"))
                        }
                    }
                    else -> {
                        Fail(error = Throwable("Clock Out Failed"))
                    }
                }
                return@safeApiCall loginResponse
            }
            flow.value = response
        }
        return flow
    }
}