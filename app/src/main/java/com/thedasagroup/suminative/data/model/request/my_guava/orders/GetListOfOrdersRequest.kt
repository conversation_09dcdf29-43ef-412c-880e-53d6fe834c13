package com.thedasagroup.suminative.data.model.request.my_guava.orders

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class GetListOfOrdersRequest(
    @SerialName("size")
    val size: Int? = null,
    @SerialName("page")
    val page: Int? = null,
    @SerialName("sortBy")
    val sortBy: String?= null,
    @SerialName("direction")
    val direction: String? = null,
    @SerialName("dateTo")
    val dateTo: String? = null,
    @SerialName("dateFrom")
    val dateFrom: String? = null,
    @SerialName("rrn")
    val rrn: String? = null,
    @SerialName("cardNumber")
    val cardNumber: String? = null,
    @SerialName("authCode")
    val authCode: String? = null,
)