package com.thedasagroup.suminative.data.model.request.print

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Request model for courses notification API
 * Endpoint: /api/printer/coursesNotification
 */
@Serializable
data class PrintBillRequest(
    @SerialName("storeId")
    val storeId: Int,
    @SerialName("tableName")
    val tableName: String? = null,
    @SerialName("serviceCharge")
    val serviceCharge: <PERSON>ole<PERSON>,
    @SerialName("cartJson")
    val cartJson: String
)
