package com.thedasagroup.suminative.data.model.request.order

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.util.UUID

@Serializable
@Parcelize
data class Cart(
    @SerialName("discount")
    val discount: Double? = 0.0,
    @SerialName("extraPrice")
    val extraPrice: Double? = 0.0,
    @SerialName("isB1G1")
    val isB1G1: Boolean? = false,
    @SerialName("netPayable")
    val netPayable: Double? = 0.0,
    @SerialName("optionPrice")
    val optionPrice: Double? = 0.0,
    @SerialName("price")
    val price: Double? = 0.0,
    @SerialName("quantity")
    val quantity: Int? = 0,
    @SerialName("storeItem")
    val storeItem: StoreItem? = null,
    @SerialName("tax")
    val tax: Double? = 0.0,
    @SerialName("orderNotes")
    val notes: String? = null,
    val uuid: String = UUID.randomUUID().toString(),
    val sentToKitchen: Boolean = false // Track if item has been sent to kitchen
) : Parcelable {
    override fun hashCode(): Int {
        return uuid.hashCode()
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Cart

        if (discount != other.discount) return false
        if (extraPrice != other.extraPrice) return false
        if (isB1G1 != other.isB1G1) return false
        if (netPayable != other.netPayable) return false
        if (optionPrice != other.optionPrice) return false
        if (price != other.price) return false
        if (quantity != other.quantity) return false
        if (tax != other.tax) return false
        if (sentToKitchen != other.sentToKitchen) return false
        if (storeItem != other.storeItem) return false
        if (notes != other.notes) return false
        if (uuid != other.uuid) return false

        return true
    }
}