package com.thedasagroup.suminative.data.model.response.store_orders

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class PandaOrderDetail(
    @SerialName("amount")
    val amount: Double? = null,
    @SerialName("coldbag_needed")
    val coldbag_needed: Boolean? = false,
    @SerialName("created_at")
    val created_at: Int? = 0,
    @SerialName("delivery_fee")
    val delivery_fee: Double? = null,
    @SerialName("description")
    val description: String? = null,
    @SerialName("order_id")
    val order_id: String? = null,
    @SerialName("payment_method")
    val payment_method: String? = null,
    @SerialName("proof_of_delivery_url")
    val proof_of_delivery_url: String? = null,
    @SerialName("status")
    val status: String? = null,
    @SerialName("tracking_link")
    val tracking_link: String? = null,
    @SerialName("updated_at")
    val updated_at: Int? = 0,
)