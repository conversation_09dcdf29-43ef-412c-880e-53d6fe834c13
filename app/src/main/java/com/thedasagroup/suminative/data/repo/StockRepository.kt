package com.thedasagroup.suminative.data.repo

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.api.BASE_URL
import com.thedasagroup.suminative.data.api.CLOUD_PRINT
import com.thedasagroup.suminative.data.api.EDIT_STOCK
import com.thedasagroup.suminative.data.api.GET_CATEGORY_SORTING
import com.thedasagroup.suminative.data.api.GET_STOCK_ITEMS
import com.thedasagroup.suminative.data.api.PLACE_ORDER
import com.thedasagroup.suminative.data.api.apiClient
import com.thedasagroup.suminative.data.model.request.category_sorting.CategorySortingRequest
import com.thedasagroup.suminative.data.model.request.cloud_print.CloudPrintRequest
import com.thedasagroup.suminative.data.model.request.option_details.GetOptionDetailsRequest
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.payment.GetPaymentSecretRequest
import com.thedasagroup.suminative.data.model.request.stock.ChangeStockRequest
import com.thedasagroup.suminative.data.model.request.stock.GetPagedStockItemsRequest
import com.thedasagroup.suminative.data.model.response.category_sorting.CategorySortingResponse
import com.thedasagroup.suminative.data.model.response.options_details.OptionDetails
import com.thedasagroup.suminative.data.model.response.order.OrderResponse
import com.thedasagroup.suminative.data.model.response.order.OrderResponse2
import com.thedasagroup.suminative.data.model.response.payments.PaymentSecretResponse
import com.thedasagroup.suminative.data.model.response.stock.ChangeStockResponse
import com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse
import io.ktor.client.call.body
import io.ktor.client.request.parameter
import io.ktor.client.request.patch
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.contentType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext

class StockRepository : BaseRepository() {
    suspend fun getPagedStockItems(request: GetPagedStockItemsRequest): StateFlow<Async<StockItemsResponse>> {
        val flow = MutableStateFlow<Async<StockItemsResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val orderResponse2 = safeApiCall {
                val response = apiClient.post(urlString = GET_STOCK_ITEMS) {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }
                val orderResponse = when (response.status.value) {
                    200 -> {
                        Success(StockItemsResponse(
                            items = response.body<StockItemsResponse>().items ?: arrayListOf(),
                            totalCount = response.body<StockItemsResponse>().totalCount ?: 0,
                            success = true
                        ))
                    }
                    204 -> {
                        Success(StockItemsResponse(
                            items = arrayListOf(),
                            success = true
                        ))
                    }
                    else -> {
                        Success(StockItemsResponse(
                            items = arrayListOf(),
                            success = false
                        ))
                    }
                }
                return@safeApiCall orderResponse
            }
            flow.value = orderResponse2
        }
        return flow
    }

    suspend fun changeStock(request: ChangeStockRequest): StateFlow<Async<ChangeStockResponse>> {
        val flow = MutableStateFlow<Async<ChangeStockResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val response = apiClient.patch(urlString = EDIT_STOCK) {
                    contentType(ContentType.Application.Json)
                    parameter("itemId", request.itemId)
                    parameter("stock", request.stock)
                }
                return@safeApiCall if(response.status.value == 200) {
                    Success(ChangeStockResponse(
                        success = true
                    ))
                } else {
                    Success(ChangeStockResponse(
                        success = false
                    ))
                }
            }
            flow.value = response
        }
        return flow
    }

    suspend fun placeOrder(request: Order): StateFlow<Async<OrderResponse2>> {
        val flow = MutableStateFlow<Async<OrderResponse2>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val response = apiClient.post(urlString = PLACE_ORDER) {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }
                return@safeApiCall if(response.status.value == 200) {
                    Success(response.body<OrderResponse2>())
                } else {
                    Success(response.body<OrderResponse2>())
                }
            }
            flow.value = response
        }
        return flow
    }

    suspend fun cloudPrint(request: CloudPrintRequest): StateFlow<Async<OrderResponse2>> {
        val flow = MutableStateFlow<Async<OrderResponse2>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val response = apiClient.post(urlString = CLOUD_PRINT) {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }
                return@safeApiCall if(response.status.value == 200) {
                    Success(response.body<OrderResponse2>())
                } else {
                    Success(response.body<OrderResponse2>())
                }
            }
            flow.value = response
        }
        return flow
    }

    suspend fun getOptionDetails(request: GetOptionDetailsRequest): StateFlow<Async<OptionDetails>> {
        val flow = MutableStateFlow<Async<OptionDetails>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val response = apiClient.post(urlString = BASE_URL) {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }
                return@safeApiCall if(response.status.value == 200) {
                    Success(response.body<OptionDetails>())
                } else {
                    Success(response.body<OptionDetails>())
                }
            }
            flow.value = response
        }
        return flow
    }

    suspend fun getPaymentSecret(request: GetPaymentSecretRequest): StateFlow<Async<PaymentSecretResponse>> {
        val flow = MutableStateFlow<Async<PaymentSecretResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val response = apiClient.post(urlString = BASE_URL) {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }
                return@safeApiCall if(response.status.value == 200) {
                    Success(response.body<PaymentSecretResponse>())
                } else {
                    Success(response.body<PaymentSecretResponse>())
                }
            }
            flow.value = response
        }
        return flow
    }

    suspend fun getCategorySorting(request: CategorySortingRequest): StateFlow<Async<CategorySortingResponse>> {
        val flow = MutableStateFlow<Async<CategorySortingResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val response = apiClient.post(urlString = GET_CATEGORY_SORTING) {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }
                return@safeApiCall if(response.status.value == 200) {
                    Success(response.body<CategorySortingResponse>())
                } else {
                    Success(response.body<CategorySortingResponse>())
                }
            }
            flow.value = response
        }
        return flow
    }
}