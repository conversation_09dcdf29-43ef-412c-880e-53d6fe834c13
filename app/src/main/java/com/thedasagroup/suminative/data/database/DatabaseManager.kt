package com.thedasagroup.suminative.data.database

import android.content.Context
import app.cash.sqldelight.db.SqlDriver
import app.cash.sqldelight.driver.android.AndroidSqliteDriver
import com.thedasagroup.suminative.database.POSDatabase
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

class DatabaseManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    private val driver: SqlDriver by lazy {
        AndroidSqliteDriver(POSDatabase.Schema, context, "pos_database.db")
    }

    val database: POSDatabase by lazy {
        POSDatabase(driver)
    }
} 