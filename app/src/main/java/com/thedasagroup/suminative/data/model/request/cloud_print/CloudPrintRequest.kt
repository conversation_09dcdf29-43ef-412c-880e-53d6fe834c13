package com.thedasagroup.suminative.data.model.request.cloud_print

import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.prefs.Prefs
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class CloudPrintRequest(
    @SerialName("storeId")
    val storeId: String,
    @SerialName("carts")
    val carts: List<Cart>?= emptyList()
)