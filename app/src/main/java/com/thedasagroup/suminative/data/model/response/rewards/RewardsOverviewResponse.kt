package com.thedasagroup.suminative.data.model.response.rewards

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class RewardsOverviewResponse(
    @SerialName("userPoints")
    val userPoints: Double? = null,
    @SerialName("rewardItems")
    val rewardItems: List<RewardItem>? = null
)

@Serializable
data class RewardItem(
    @SerialName("id")
    val id: Int? = null,
    @SerialName("businessId")
    val businessId: Int? = null,
    @SerialName("stampsRequired")
    val stampsRequired: Int? = null,
    @SerialName("storeItem")
    val storeItem: RewardStoreItem? = null
)

@Serializable
data class RewardStoreItem(
    @SerialName("id")
    val id: Int? = null,
    @SerialName("businessId")
    val businessId: Int? = null,
    @SerialName("storeId")
    val storeId: Int? = null,
    @SerialName("brandId")
    val brandId: Int? = null,
    @SerialName("categoryId")
    val categoryId: Int? = null,
    @SerialName("name")
    val name: String? = null,
    @SerialName("pic")
    val pic: String? = null,
    @SerialName("price")
    val price: Double? = null,
    @SerialName("unitId")
    val unitId: Int? = null,
    @SerialName("discountType")
    val discountType: Int? = null,
    @SerialName("discountedAmount")
    val discountedAmount: String? = null,
    @SerialName("tax")
    val tax: Double? = null,
    @SerialName("billAmount")
    val billAmount: Double? = null,
    @SerialName("ingredients")
    val ingredients: String? = null,
    @SerialName("description")
    val description: String? = null,
    @SerialName("preparationTime")
    val preparationTime: String? = null,
    @SerialName("additionalInfo")
    val additionalInfo: String? = null,
    @SerialName("dailyCapacity")
    val dailyCapacity: Int? = null,
    @SerialName("createdBy")
    val createdBy: Int? = null,
    @SerialName("createdOn")
    val createdOn: String? = null,
    @SerialName("modifiedBy")
    val modifiedBy: Int? = null,
    @SerialName("modifiedOn")
    val modifiedOn: String? = null,
    @SerialName("json")
    val json: String? = null,
    @SerialName("servingSize")
    val servingSize: String? = null,
    @SerialName("stock")
    val stock: Int? = null,
    @SerialName("vat")
    val vat: Boolean? = null,
    @SerialName("extras")
    val extras: List<String>? = null
)
