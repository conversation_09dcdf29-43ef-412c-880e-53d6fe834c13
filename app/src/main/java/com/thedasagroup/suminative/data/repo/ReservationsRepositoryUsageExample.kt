package com.thedasagroup.suminative.data.repo

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.thedasagroup.suminative.data.model.request.reservations.CreateReservationRequest
import com.thedasagroup.suminative.data.model.request.reservations.EditReservationRequest
import com.thedasagroup.suminative.data.model.response.reservations.CreateReservationResponse
import com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject

/**
 * Example usage of ReservationsRepository
 * This file demonstrates how to use the ReservationsRepository in your ViewModels or Use Cases
 */
class ReservationsRepositoryUsageExample @Inject constructor(
    private val reservationsRepository: ReservationsRepository
) {

    /**
     * Example: Get active reservations for the next 45 minutes
     */
    suspend fun getActiveReservationsExample() {
        val storeId = 123
        val currentTime = "2025-07-15T18:00:00"
        
        val reservationsFlow: StateFlow<Async<ReservationsResponse>> = 
            reservationsRepository.getActiveReservationsRetrofit(storeId, currentTime, timezoneOffset = 0)
        
        // Observe the flow in your ViewModel
        reservationsFlow.collect { async ->
            when (async) {
                is Loading -> {
                    // Show loading state
                    println("Loading active reservations...")
                }
                is Success -> {
                    // Handle successful response
                    val reservations = async.invoke().reservations
                    println("Found ${reservations.size} active reservations")
                    reservations.forEach { reservation ->
                        println("Reservation: ${reservation.customerName} for ${reservation.numPeople} people at ${reservation.reservationTime} (Table: ${reservation.tableName})")
                    }
                }
                is Fail -> {
                    // Handle error
                    println("Error loading active reservations: ${async.error.message}")
                }
                else -> {

                }
            }
        }
    }

    /**
     * Example: Get all reservations history
     */
    suspend fun getAllReservationsExample() {
        val storeId = 123
        val currentTime = "2025-07-15T18:00:00"
        
        val reservationsFlow = reservationsRepository.getAllReservationsRetrofit(storeId, currentTime)
        
        reservationsFlow.collect { async ->
            when (async) {
                is Success -> {
                    val allReservations = async.invoke().reservations
                    println("Total reservations in history: ${allReservations.size}")
                }
                is Fail -> {
                    println("Error loading reservation history: ${async.error.message}")
                }
                is Loading -> {
                    println("Loading reservation history...")
                }
                else -> {

                }
            }
        }
    }

    /**
     * Example: Create a new reservation
     * Uses the new POST API endpoint
     */
    suspend fun createReservationExample() {
        val createRequest = CreateReservationRequest(
            id = null, // null for new reservation
            storeId = 158,
            tableId = 12,
            customerId = 321,
            guestName = "Jane Doe",
            guestPhone = "03001234567",
            numPeople = 4,
            reservationStatus = 0, // 0 = pending, 1 = confirmed, etc.
            reservationTime = "2025-07-22T18:30",
            timezoneOffset = 300 // minutes east of UTC (e.g. +5h = 300)
        )

        val createFlow = reservationsRepository.createReservation(createRequest)

        createFlow.collect { async ->
            when (async) {
                is Success -> {
                    val response = async()
                    println("Reservation created successfully with ID: ${response.id}")
                    println("Reservation time: ${response.reservationTime}")
                    println("Created at: ${response.createdAt}")
                }
                is Fail -> {
                    println("Error creating reservation: ${async.error.message}")
                }
                is Loading -> {
                    println("Creating reservation...")
                }
                else -> {

                }
            }
        }
    }

    /**
     * Example: Update an existing reservation using the POST API
     * Set the id field to the existing reservation ID
     */
    suspend fun updateReservationExample() {
        val updateRequest = CreateReservationRequest(
            id = 30, // existing reservation ID to update
            storeId = 158,
            tableId = 15, // changed table
            customerId = 321,
            guestName = "Jane Doe Updated",
            guestPhone = "03001234567",
            numPeople = 6, // changed party size
            reservationStatus = 1, // changed to confirmed
            reservationTime = "2025-07-22T19:00", // changed time
            timezoneOffset = 300
        )

        val updateFlow = reservationsRepository.createReservation(updateRequest)

        updateFlow.collect { async ->
            when (async) {
                is Success -> {
                    val response = async()
                    println("Reservation updated successfully with ID: ${response.id}")
                    println("Updated reservation time: ${response.reservationTime}")
                }
                is Fail -> {
                    println("Error updating reservation: ${async.error.message}")
                }
                is Loading -> {
                    println("Updating reservation...")
                }
                else -> {

                }
            }
        }
    }

    /**
     * Example: Edit an existing reservation
     * Note: The edit request still uses the original API structure
     */
    suspend fun editReservationExample() {
        val reservationId = 100
        val editRequest = EditReservationRequest(
            customerId = 789,
            tableId = 456,
            reservationTime = "2025-07-15T20:00:00",
            guestName = "John Doe Updated",
            guestPhone = "5559876543",
            partySize = 5
        )

        val editFlow = reservationsRepository.editReservation(reservationId, editRequest)

        editFlow.collect { async ->
            when (async) {
                is Success -> {
                    println("Reservation updated successfully")
                }
                is Fail -> {
                    println("Error updating reservation: ${async.error.message}")
                }
                is Loading -> {
                    println("Updating reservation...")
                }
                else -> {

                }
            }
        }
    }

    /**
     * Example: Cancel a reservation
     */
    suspend fun cancelReservationExample() {
        val reservationId = 100
        
        val cancelFlow = reservationsRepository.cancelReservation(reservationId)
        
        cancelFlow.collect { async ->
            when (async) {
                is Success -> {
                    println("Reservation cancelled successfully")
                }
                is Fail -> {
                    println("Error cancelling reservation: ${async.error.message}")
                }
                is Loading -> {
                    println("Cancelling reservation...")
                }
                else -> {

                }
            }
        }
    }
}
