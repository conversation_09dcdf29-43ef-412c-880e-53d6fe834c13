package com.thedasagroup.suminative.data.model.response.sales

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class SalesReportResponse(
    @SerialName("cardOrders")
    val cardOrders: Int? = 0,
    @SerialName("cardPayment")
    val cardPayment: Double? = 0.0,
    @SerialName("cashOrders")
    val cashOrders: Int? = 0,
    @SerialName("cashPayment")
    val cashPayment: Double? = 0.0,
    @SerialName("categoryStats")
    val categoryStats: Map<String, CategoryTotals>? = mutableMapOf(),
    @SerialName("deliveryCharge")
    val deliveryCharge: Double? = 0.0,
    @SerialName("itemSales")
    val itemSales: Double? = 0.0,
    @SerialName("netSales")
    val netSales: Double? = 0.0,
    @SerialName("totalGuests")
    val totalGuests: Int? = 0,
    @SerialName("totalReceipts")
    val totalReceipts: Int? = 0,
    @SerialName("vat")
    val vat: Double? = 0.0,
    @SerialName("nonVatItemsTotal")
    val nonVatItemsTotal: Double? = 0.0,
    @SerialName("vatItemsTotal")
    val vatItemsTotal: Double? = 0.0
)