package com.thedasagroup.suminative.data.model.response.login

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Businesse(
    @SerialName("address")
    val address: String? = null,
    @SerialName("businessAdminId")
    val businessAdminId: Int? = null,
    @SerialName("countryId")
    val countryId: Int? = null,
    @SerialName("createdBy")
    val createdBy: Int? = null,
    @SerialName("createdOn")
    val createdOn: String? = null,
    @SerialName("email")
    val email: String? = null,
    @SerialName("headerImage")
    val headerImage: String? = null,
    @SerialName("id")
    val id: Int? = null,
    @SerialName("logo")
    val logo: String? = null,
    @SerialName("modifiedBy")
    val modifiedBy: Int? = null,
    @SerialName("modifiedOn")
    val modifiedOn: String? = null,
    @SerialName("name")
    val name: String? = null,
    @SerialName("phone")
    val phone: String? = null
)