package com.thedasagroup.suminative.data.model.response.login

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.json.Json

@Serializable
data class LoginResponse(
    @SerialName("user")
    val user: User? = null,
    @SerialName("business")
    val businesses: Businesse? = null,
    @SerialName("stores")
    val stores: List<Store>? = mutableListOf(),
    val success: Boolean? = false,
    @SerialName("brandId")
    val brandId: Int? = 0,
    @SerialName("storeNameOnBill")
    val storeNameOnBill: String? = "",
    @SerialName("message")
    val message: String? = null
)