package com.thedasagroup.suminative.data.model.response.my_guava.failure

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class GuavaFailResponse(
//    @SerialName("error")
//    val error: String? = null,
//    @SerialName("message")
//    val message: List<String>? = mutableListOf(),
//    @SerialName("statusCode")
//    val statusCode: Int?= 0
    @SerialName("error")
    val error: String? = null,
    @SerialName("message")
    val message: String? = null,
    @SerialName("statusCode")
    val statusCode: Int?= 0
)