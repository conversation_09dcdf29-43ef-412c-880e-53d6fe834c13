package com.thedasagroup.suminative.data.model.request.print

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Request model for courses notification API
 * Endpoint: /api/printer/coursesNotification
 */
@Serializable
data class CoursesNotificationRequest(
    @SerialName("storeId")
    val storeId: Int,
    @SerialName("courseName")
    val courseName: String,
    @SerialName("tableName")
    val tableName: String,
    @SerialName("cartJson")
    val cartJson: String
)
