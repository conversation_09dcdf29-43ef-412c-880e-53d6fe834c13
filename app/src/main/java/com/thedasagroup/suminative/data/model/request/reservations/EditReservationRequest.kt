package com.thedasagroup.suminative.data.model.request.reservations

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Request model for editing a reservation
 * This maintains the original API structure for the PUT request
 */
@Serializable
data class EditReservationRequest(
    @SerialName("customerId")
    val customerId: Int,
    @SerialName("tableId")
    val tableId: Int,
    @SerialName("reservationTime")
    val reservationTime: String, // ISO format: "2025-07-15T20:00:00"
    @SerialName("guestName")
    val guestName: String,
    @SerialName("guestPhone")
    val guestPhone: String,
    @SerialName("partySize")
    val partySize: Int
)

/**
 * Request model for creating or updating a reservation via POST API
 * Matches the new API structure from the curl example
 */
@Serializable
data class CreateReservationRequest(
    @SerialName("id")
    val id: Int? = null, // null for new, or the existing ID to update
    @SerialName("storeId")
    val storeId: Int,
    @SerialName("tableId")
    val tableId: Int,
    @SerialName("customerId")
    val customerId: Int,
    @SerialName("guestName")
    val guestName: String,
    @SerialName("guestPhone")
    val guestPhone: String,
    @SerialName("numPeople")
    val numPeople: Int,
    @SerialName("reservationStatus")
    val reservationStatus: Int, // e.g. 0 = pending, 1 = confirmed, etc.
    @SerialName("reservationTime")
    val reservationTime: String, // ISO format: "2025-07-22T18:30"
    @SerialName("timezoneOffset")
    val timezoneOffset: Int // minutes east of UTC (e.g. +5h = 300)
)
