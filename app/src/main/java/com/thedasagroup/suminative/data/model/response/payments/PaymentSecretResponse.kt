package com.thedasagroup.suminative.data.model.response.payments

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class PaymentSecretResponse(
    @SerialName("command")
    val command: String?= null,
    @SerialName("paymentId")
    val paymentId: Int? = -1,
    @SerialName("paymentResponseJson")
    val paymentResponseJson: String? =null,
    @SerialName("socketId")
    val socketId: String? = null,
    @SerialName("success")
    val success: Boolean? = false
)