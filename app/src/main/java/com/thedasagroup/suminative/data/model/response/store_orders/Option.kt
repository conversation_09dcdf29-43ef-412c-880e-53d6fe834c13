package com.thedasagroup.suminative.data.model.response.store_orders

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@Parcelize
data class Option(
    @SerialName("displayOrder") val displayOrder: Int? = null,
    @SerialName("id") val id: Int? = null,
    @SerialName("name") val name: String? = null,
    @SerialName("optionSetId") val optionSetId: Int? = null,
    @SerialName("price") val price: Double? = null,
    @SerialName("quantity") val quantity: Int? = null,
    @SerialName("status") val status: Int? = null,
    @SerialName("optionchecked") val optionchecked: Boolean = false,
    @SerialName("initialQuantity") val initialQuantity: Int? = 1
) : Parcelable