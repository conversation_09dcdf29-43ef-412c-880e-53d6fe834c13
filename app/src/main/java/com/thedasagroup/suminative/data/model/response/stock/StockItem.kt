package com.thedasagroup.suminative.data.model.response.stock

import com.thedasagroup.suminative.data.model.request.order.StoreItem
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class StockItem(
    @SerialName("additionalInfo") val additionalInfo: String? = null,
    @SerialName("billAmount") val billAmount: Double? = 0.0,
    @SerialName("brandId") val brandId: Int? = 0,
    @SerialName("businessId") val businessId: Int? = 0,
    @SerialName("category") val category: String? = null,
    @SerialName("categoryId") val categoryId: Int? = 0,
    @SerialName("createdBy") val createdBy: Int? = 0,
    @SerialName("createdOn") val createdOn: String? = null,
    @SerialName("dailyCapacity") val dailyCapacity: Int? = 0,
    @SerialName("description") val description: String? = null,
    @SerialName("discountType") val discountType: Int? = 0,
    @SerialName("discountedAmount") val discountedAmount: String? = null,
    @SerialName("id") val id: Int? = 0,
    @SerialName("ingredients") val ingredients: String? = null,
    @SerialName("modifiedBy") val modifiedBy: Int? = 0,
    @SerialName("modifiedOn") val modifiedOn: String? = null,
    @SerialName("name") val name: String? = null,
    @SerialName("pic") val pic: String? = null,
    @SerialName("preparationTime") val preparationTime: String? = null,
    @SerialName("price") val price: Double? = 0.0,
    @SerialName("servingSize") val servingSize: String? = null,
    @SerialName("stock") val stock: Int? = 0,
    @SerialName("storeId") val storeId: Int? = 0,
    @SerialName("tax") val tax: Double? = 0.0,
    @SerialName("unitId") val unitId: Int? = 0,
    @SerialName("vat") val vat: Boolean? = false,
) {
    fun toStoreItem(): StoreItem {
        return StoreItem(
            additionalInfo = "",
            billAmount = billAmount,
            brandId = brandId,
            businessId = businessId,
            categoryId = categoryId,
            createdBy = createdBy,
            createdOn = createdOn,
            dailyCapacity = dailyCapacity,
            description = description,
            discountType = discountType,
            discountedAmount = discountedAmount?.toDouble() ?: 0.0,
            extras = mutableListOf(),
            id = id,
            ingredients = ingredients,
            modifiedBy = -1,
            modifiedOn = "",
            name = name,
            pic = pic,
            preparationTime = preparationTime,
            price = price,
            quantity = stock,
            servingSize = servingSize,
            storeId = storeId,
            tax = tax ?: 0.0,
            unitId = unitId,
            discounttypename = "Flat",
            unitName = "Select Unit",
            optionSets = mutableListOf(),
            vat = vat?: false,
            courseId = null // Will be set when adding to cart
        )
    }
}