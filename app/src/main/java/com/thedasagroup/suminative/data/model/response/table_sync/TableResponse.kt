package com.thedasagroup.suminative.data.model.response.table_sync

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * Response model for table toggle occupied API
 * Matches the response structure for POST /api/tables/toggleOccupied
 */
@Serializable
data class TableResponse(
    @SerialName("id")
    val id: Int,
    @SerialName("storeId")
    val storeId: Int,
    @SerialName("areaId")
    val areaId: Int,
    @SerialName("tableName")
    val tableName: String,
    @SerialName("seatingCapacity")
    val seatingCapacity: Int,
    @SerialName("tableDetailsJson")
    val tableDetailsJson: String,
    @SerialName("occupied")
    val occupied: Boolean,
    @SerialName("reserved")
    val reserved: Boolean,
    @SerialName("netPayable")
    val netPayable: Double
)
