package com.thedasagroup.suminative.data.repo

import android.util.Log
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.jakewharton.retrofit2.converter.kotlinx.serialization.asConverterFactory
import com.thedasagroup.suminative.data.api.BASE_DOMAIN
import com.thedasagroup.suminative.data.api.CANCEL_RESERVATION
import com.thedasagroup.suminative.data.api.CREATE_RESERVATION
import com.thedasagroup.suminative.data.api.EDIT_RESERVATION
import com.thedasagroup.suminative.data.api.ReservationsRetrofitService
import com.thedasagroup.suminative.data.api.apiClient
import com.thedasagroup.suminative.data.model.request.reservations.CreateReservationRequest
import com.thedasagroup.suminative.data.model.request.reservations.EditReservationRequest
import com.thedasagroup.suminative.data.model.response.reservations.Area
import com.thedasagroup.suminative.data.model.response.reservations.CreateReservationResponse
import com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse
import com.thedasagroup.suminative.data.model.response.reservations.Table
import io.ktor.client.call.body
import io.ktor.client.request.delete
import io.ktor.client.request.post
import io.ktor.client.request.put
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.contentType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import timber.log.Timber

class ReservationsRepository : BaseRepository() {

    /**
     * Create a new reservation or update an existing one
     * @param request The create reservation request containing reservation data
     */
    suspend fun createReservation(
        request: CreateReservationRequest
    ): StateFlow<Async<CreateReservationResponse>> {
        val flow = MutableStateFlow<Async<CreateReservationResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val httpResponse = apiClient.post(urlString = CREATE_RESERVATION) {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }
                val responseBody = httpResponse.body<CreateReservationResponse>()
                return@safeApiCall Success(responseBody)
            }
            flow.value = response
        }
        return flow
    }

    /**
     * Edit an existing reservation
     * @param reservationId The reservation ID to edit
     * @param request The edit reservation request containing updated data
     */
    suspend fun editReservation(
        reservationId: Int,
        request: EditReservationRequest
    ): StateFlow<Async<Boolean>> {
        val flow = MutableStateFlow<Async<Boolean>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                apiClient.put(urlString = "$EDIT_RESERVATION/$reservationId") {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }
                return@safeApiCall Success(true)
            }
            flow.value = response
        }
        return flow
    }

    /**
     * Cancel a reservation
     * @param reservationId The reservation ID to cancel
     */
    suspend fun cancelReservation(reservationId: Int): StateFlow<Async<Boolean>> {
        val flow = MutableStateFlow<Async<Boolean>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                apiClient.delete(urlString = "$CANCEL_RESERVATION/$reservationId")
                return@safeApiCall Success(true)
            }
            flow.value = response
        }
        return flow
    }

    /**
     * Get currently active reservations using Retrofit
     * @param storeId The store ID
     * @param currentTime Current time in ISO format (e.g., "2025-07-15T18:00:00")
     */
    suspend fun getActiveReservationsRetrofit(
        storeId: Int,
        currentTime: String,
        timezoneOffset: Int
    ): StateFlow<Async<ReservationsResponse>> {
        val flow = MutableStateFlow<Async<ReservationsResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val service = getReservationRetrofitService()
                val retrofitResponse = service.getActiveReservations(storeId = storeId, currentTime = currentTime, timezoneOffset = timezoneOffset)

                return@safeApiCall when {
                    retrofitResponse.isSuccessful -> {
                        val reservationsList = retrofitResponse.body() ?: emptyList()
                        val reservationsResponse = ReservationsResponse(
                            reservations = reservationsList,
                            success = true,
                            message = null
                        )
                        Success(reservationsResponse)
                    }
                    else -> {
                        Fail(Throwable("API Error: ${retrofitResponse.code()} - ${retrofitResponse.message()}"))
                    }
                }
            }
            flow.value = response
        }
        return flow
    }

    /**
     * Get all reservations using Retrofit
     * @param storeId The store ID
     * @param currentTime Current time in ISO format (e.g., "2025-07-15T18:00:00")
     */
    suspend fun getAllReservationsRetrofit(
        storeId: Int,
        currentTime: String
    ): StateFlow<Async<ReservationsResponse>> {
        val flow = MutableStateFlow<Async<ReservationsResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val service = getReservationRetrofitService()
                val retrofitResponse = service.getAllReservations(storeId, currentTime)

                return@safeApiCall when {
                    retrofitResponse.isSuccessful -> {
                        val reservationsList = retrofitResponse.body() ?: emptyList()
                        val reservationsResponse = ReservationsResponse(
                            reservations = reservationsList,
                            success = true,
                            message = null
                        )
                        Success(reservationsResponse)
                    }
                    else -> {
                        Fail(Throwable("API Error: ${retrofitResponse.code()} - ${retrofitResponse.message()}"))
                    }
                }
            }
            flow.value = response
        }
        return flow
    }

    val interceptor = run {
        val httpLoggingInterceptor = HttpLoggingInterceptor()
        httpLoggingInterceptor.apply {
            httpLoggingInterceptor.level = HttpLoggingInterceptor.Level.BODY
        }
    }

    fun getReservationRetrofitService(): ReservationsRetrofitService {
        val networkJson = Json { ignoreUnknownKeys = true }
        val client: OkHttpClient = OkHttpClient.Builder()
            .addInterceptor(interceptor)
            .build()
        val retrofit = Retrofit.Builder()
            .baseUrl("$BASE_DOMAIN/")
            .client(client)
            .addConverterFactory(networkJson.asConverterFactory("application/json".toMediaType()))
            .build()
        return retrofit.create(ReservationsRetrofitService::class.java)
    }

    /**
     * Get reservation areas for a store using Retrofit
     * @param storeId The store ID to filter areas
     */
    suspend fun getReservationAreas(
        storeId: Int
    ): StateFlow<Async<List<Area>>> {
        val flow = MutableStateFlow<Async<List<Area>>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val service = getReservationRetrofitService()
                val retrofitResponse = service.getReservationAreas(storeId = storeId)

                return@safeApiCall when {
                    retrofitResponse.isSuccessful -> {
                        val areasList = retrofitResponse.body() ?: emptyList()
                        Success(areasList)
                    }
                    else -> {
                        Fail(Throwable("API Error: ${retrofitResponse.code()} - ${retrofitResponse.message()}"))
                    }
                }
            }
            flow.value = response
        }
        return flow
    }

    /**
     * Get tables for a specific area using Retrofit
     * @param areaId The area ID to filter tables
     */
    suspend fun getReservationTables(
        areaId: Int
    ): StateFlow<Async<List<Table>>> {
        val flow = MutableStateFlow<Async<List<Table>>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val service = getReservationRetrofitService()
                val retrofitResponse = service.getReservationTables(areaId = areaId)

                return@safeApiCall when {
                    retrofitResponse.isSuccessful -> {
                        val tablesList = retrofitResponse.body() ?: emptyList()
                        Success(tablesList)
                    }
                    else -> {
                        Fail(Throwable("API Error: ${retrofitResponse.code()} - ${retrofitResponse.message()}"))
                    }
                }
            }
            flow.value = response
        }
        return flow
    }

    /**
     * Get table by ID using Retrofit
     * @param tableId The table ID to retrieve
     */
    suspend fun getTableById(
        tableId: Int
    ): StateFlow<Async<Table>> {
        val flow = MutableStateFlow<Async<Table>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val service = getReservationRetrofitService()
                val retrofitResponse = service.getTableById(tableId = tableId)

                return@safeApiCall when {
                    retrofitResponse.isSuccessful -> {
                        val table = retrofitResponse.body()
                        if (table != null) {
                            Success(table)
                        } else {
                            Fail(Throwable("Table not found"))
                        }
                    }
                    else -> {
                        Fail(Throwable("API Error: ${retrofitResponse.code()} - ${retrofitResponse.message()}"))
                    }
                }
            }
            flow.value = response
        }
        return flow
    }
}