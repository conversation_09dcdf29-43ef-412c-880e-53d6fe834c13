package com.thedasagroup.suminative.data.model.request.order

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class OrderRequest(
    @SerialName("appType")
    val appType: String ?= null,
    @SerialName("businessId")
    val businessId: Int? = 0,
    @SerialName("cartSize")
    val cartSize: Int? = 0,
    @SerialName("command")
    val command: String? = null,
    @SerialName("conversation")
    val conversation: Conversation?= null,
    @SerialName("currentDate")
    val currentDate: String? = null,
    @SerialName("customer")
    val customer: Customer?= null,
    @SerialName("customerId")
    val customerId: Int? = 0,
    @SerialName("deliveryAddress")
    val deliveryAddress: DeliveryAddress?= null,
    @SerialName("email")
    val email: String? = null,
    @SerialName("feedbackComplain")
    val feedbackComplain: FeedbackComplain?= null,
    @SerialName("itemId")
    val itemId: Int? = 0,
    @SerialName("loggedUserId")
    val loggedUserId: Int? = 0,
    @SerialName("order")
    val order: Order?= null,
    @SerialName("orderId")
    val orderId: Int? = 0,
    @SerialName("orderStatus")
    val orderStatus: OrderStatus?= null,
    @SerialName("pandaOrderId")
    val pandaOrderId: String? = null,
    @SerialName("paymentData")
    val paymentData: PaymentData? = null,
    @SerialName("phoneNumber")
    val phoneNumber: String? = null,
    @SerialName("priceAfterPromo")
    val priceAfterPromo: Int? = 0,
    @SerialName("priceBeforePromo")
    val priceBeforePromo: Int? = 0,
    @SerialName("promoCodes")
    val promoCodes: PromoCodes?= null,
    @SerialName("storeId")
    val storeId: Int? = 0,
    @SerialName("supportDetail")
    val supportDetail: SupportDetail?= null,
)