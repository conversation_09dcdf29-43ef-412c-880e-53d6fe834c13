package com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class GetListOfOrdersResponse(
    @SerialName("data")
    val data: ListOfOrderData? = null
)
@Serializable
data class ListOfOrderData(
    @SerialName("list"  )
    val list: List<GuavaOrder>? = mutableListOf(),
    @SerialName("pageInfo")
    val pageInfo: PageInfo? = null,
)