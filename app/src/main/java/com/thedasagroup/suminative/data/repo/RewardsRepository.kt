package com.thedasagroup.suminative.data.repo

import android.util.Log
import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.jakewharton.retrofit2.converter.kotlinx.serialization.asConverterFactory
import com.thedasagroup.suminative.data.api.BASE_DOMAIN
import com.thedasagroup.suminative.data.api.RewardsRetrofitService
import com.thedasagroup.suminative.data.api.apiClient
import com.thedasagroup.suminative.data.model.request.rewards.AddPointsRequest
import com.thedasagroup.suminative.data.model.request.rewards.GetAllCustomersRequest
import com.thedasagroup.suminative.data.model.response.rewards.AddPointsResponse
import com.thedasagroup.suminative.data.model.response.rewards.GetAllCustomersResponse
import com.thedasagroup.suminative.data.model.response.rewards.GetCustomersResponse
import com.thedasagroup.suminative.data.model.response.rewards.RewardsOverviewResponse
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.client.request.parameter
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.contentType
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import timber.log.Timber

class RewardsRepository : BaseRepository() {
    
    suspend fun getAllCustomers(request: GetAllCustomersRequest): StateFlow<Async<GetCustomersResponse>> {
        val flow = MutableStateFlow<Async<GetCustomersResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val service = getRewardsRetrofitService()
                val retrofitResponse = service.getCustomer(customerId = request.customerId, businessId = request.businessId)

                if (retrofitResponse.isSuccessful) {
                    val overview = retrofitResponse.body()
                    if (overview != null) {
                        Success(overview)
                    } else {
                        Fail(Throwable("Empty response body for customer"))
                    }
                } else {
                    Fail(Throwable("Failed to get customer : ${retrofitResponse.message()}"))
                }
            }
            flow.value = response
        }
        return flow
    }
    
    suspend fun addPoints(request: AddPointsRequest): StateFlow<Async<AddPointsResponse>> {
        val flow = MutableStateFlow<Async<AddPointsResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val apiResponse = apiClient.post(urlString = "$BASE_DOMAIN/BackendDASA-1.0.0/api/rewards/addPoints") {
                    contentType(ContentType.Application.Json)
                    setBody(request)
                }.body<AddPointsResponse>()
                return@safeApiCall Success(apiResponse)
            }
            flow.value = response
        }
        return flow
    }

    suspend fun getUserPoints(userId: Int, businessId: Int): StateFlow<Async<Double>> {
        val flow = MutableStateFlow<Async<Double>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val service = getRewardsRetrofitService()
                val retrofitResponse = service.getUserPoints(userId, businessId)

                if (retrofitResponse.isSuccessful) {
                    val points = retrofitResponse.body() ?: 0.0
                    Success(points)
                } else {
                    Fail(Throwable("Failed to get user points: ${retrofitResponse.message()}"))
                }
            }
            flow.value = response
        }
        return flow
    }

    suspend fun getRewardsOverview(userId: Int, businessId: Int): StateFlow<Async<RewardsOverviewResponse>> {
        val flow = MutableStateFlow<Async<RewardsOverviewResponse>>(Loading())
        withContext(Dispatchers.IO) {
            val response = safeApiCall {
                val service = getRewardsRetrofitService()
                val retrofitResponse = service.getRewardsOverview(userId, businessId)

                if (retrofitResponse.isSuccessful) {
                    val overview = retrofitResponse.body()
                    if (overview != null) {
                        Success(overview)
                    } else {
                        Fail(Throwable("Empty response body for rewards overview"))
                    }
                } else {
                    Fail(Throwable("Failed to get rewards overview: ${retrofitResponse.message()}"))
                }
            }
            flow.value = response
        }
        return flow
    }

    val intercepter = HttpLoggingInterceptor().apply {
        this.level = HttpLoggingInterceptor.Level.BODY
    }

    /**
     * Creates and configures the Retrofit service for Rewards API
     */
    private fun getRewardsRetrofitService(): RewardsRetrofitService {
        val networkJson = Json { ignoreUnknownKeys = true }
        val client: OkHttpClient = OkHttpClient.Builder()
            .addInterceptor(intercepter)
            .build()
        val retrofit = Retrofit.Builder()
            .baseUrl("$BASE_DOMAIN/")
            .client(client)
            .addConverterFactory(networkJson.asConverterFactory("application/json".toMediaType()))
            .build()
        return retrofit.create(RewardsRetrofitService::class.java)
    }
}
