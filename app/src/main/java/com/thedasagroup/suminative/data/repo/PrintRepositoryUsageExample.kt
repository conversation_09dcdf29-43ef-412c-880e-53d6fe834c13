package com.thedasagroup.suminative.data.repo

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.model.request.print.CoursesNotificationRequest
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.response.courses_notification.CoursesNotificationResponse
import kotlinx.coroutines.flow.StateFlow

/**
 * Example usage of PrintRepository for courses notification API
 * This file demonstrates how to use the different methods available
 */
class PrintRepositoryUsageExample {
    
    private val printRepository = PrintRepository()
    
    /**
     * Example 1: Using the request object method
     */
    suspend fun sendNotificationWithRequest() {
        val request = CoursesNotificationRequest(
            storeId = 158,
            courseName = "Mains",
            tableName = "T-12",
            cartJson = """{"items":[{"id":201,"name":"Steak","qty":1,"price":19.99}],"total":19.99}"""
        )
        
        val response: StateFlow<Async<CoursesNotificationResponse>> = 
            printRepository.sendCoursesNotification(request)
        
        // Handle the response
        response.collect { asyncResult ->
            when (asyncResult) {
                is Success -> {
                    val result = asyncResult.invoke()
                    println("Success: ${result.message}")
                }
                is Fail -> {
                    val error = asyncResult.error
                    println("Error: ${error.message}")
                }
                else -> {
                    println("Loading...")
                }
            }
        }
    }
    
    /**
     * Example 2: Using the convenience method with individual parameters
     */
    suspend fun sendNotificationWithParameters() {
        val response: StateFlow<Async<CoursesNotificationResponse>> = 
            printRepository.sendCoursesNotification(
                storeId = 158,
                courseName = "Starters",
                tableName = "T-05",
                cartJson = """{"items":[{"id":101,"name":"Soup","qty":2,"price":8.50}],"total":17.00}"""
            )
        
        // Handle the response
        response.collect { asyncResult ->
            when (asyncResult) {
                is Success -> {
                    val result = asyncResult.invoke()
                    println("Notification sent successfully: ${result.message}")
                }
                is Fail -> {
                    val error = asyncResult.error
                    println("Failed to send notification: ${error.message}")
                }
                else -> {
                    println("Sending notification...")
                }
            }
        }
    }
    
    /**
     * Example 3: Using the convenience method with Cart list
     */
    suspend fun sendNotificationWithCartList() {
        // Create sample cart items
        val carts = listOf(
            Cart(
                quantity = 1,
                price = 19.99,
                // Add other cart properties as needed
            ),
            Cart(
                quantity = 2,
                price = 12.50,
                // Add other cart properties as needed
            )
        )
        
        val response: StateFlow<Async<CoursesNotificationResponse>> = 
            printRepository.sendCoursesNotification(
                storeId = 158,
                courseName = "Desserts",
                tableName = "T-08",
                carts = carts
            )
        
        // Handle the response
        response.collect { asyncResult ->
            when (asyncResult) {
                is Success -> {
                    val result = asyncResult.invoke()
                    println("Cart notification sent: ${result.message}")
                }
                is Fail -> {
                    val error = asyncResult.error
                    println("Cart notification failed: ${error.message}")
                }
                else -> {
                    println("Processing cart notification...")
                }
            }
        }
    }
}
