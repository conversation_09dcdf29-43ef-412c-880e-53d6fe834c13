package com.thedasagroup.suminative.domain.table_sync

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.response.table_sync.SyncOrderResponse
import com.thedasagroup.suminative.data.repo.SyncRepository
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject

/**
 * Use case for getting synced order for a table
 * Uses GET request with Retrofit
 */
class GetSyncedOrderForTableUseCase @Inject constructor(
    private val syncRepository: SyncRepository
) {
    
    /**
     * Get synced order for table
     * @param tableId The table ID to get the synced order for
     * @return StateFlow with async result of get operation
     */
    suspend operator fun invoke(
        tableId: Int
    ): StateFlow<Async<SyncOrderResponse>> {
        return syncRepository.getSyncedOrderForTable(tableId)
    }
}
