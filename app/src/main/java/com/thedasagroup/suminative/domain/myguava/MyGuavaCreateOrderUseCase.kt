package com.thedasagroup.suminative.domain.myguava

import com.airbnb.mvrx.Async
import com.instacart.truetime.time.TrueTimeImpl
import com.thedasagroup.suminative.data.model.request.my_guava.orders.CreateOrderRequest
import com.thedasagroup.suminative.data.model.request.order.Customer
import com.thedasagroup.suminative.data.model.request.order.DeliveryAddress
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.PandaOrderDetail
import com.thedasagroup.suminative.data.model.response.my_guava.orders.create_order.GuavaOrderResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.MyGuavaRepository
import com.thedasagroup.suminative.data.repo.StockRepository
import com.thedasagroup.suminative.ui.products.ProductsScreenState
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_BACK_END
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_GUAVA
import com.thedasagroup.suminative.ui.utils.formatDate
import kotlinx.coroutines.flow.StateFlow
import java.util.Calendar
import java.util.UUID

open class MyGuavaCreateOrderUseCase(
        private val guavaRepository: MyGuavaRepository,
        private val prefs: Prefs,
        private val trueTimeImpl: TrueTimeImpl
) {
    suspend operator fun invoke(order: Order, state: ProductsScreenState): StateFlow<Async<GuavaOrderResponse>> {

        val total = order.carts?.sumOf { it.netPayable ?: 0.0 } ?: 0.0
        val totalOptionsPrice = order.carts?.sumOf {
            val storeItem = it.storeItem
            val optionSets = storeItem?.optionSets
            val optionSetsSum = optionSets?.sumOf {optioSet ->
                val optionItems = optioSet.options
                optionItems.sumOf { optionItem ->
                    if(optionItem?.optionchecked == true) {
                        (optionItem.price ?: 0.0) * (optionItem.quantity?.toDouble() ?: 0.0)
                    }
                    else 0.0
                }
            }
            optionSetsSum ?: 0.0
        } ?: 0.0
        val updatedOrder = order.copy(
            id = -1,
            businessId = prefs.store?.businessId,
            storeId = prefs.store?.id,
            customerId = -1,
            totalPrice = total.toDouble(),
            totalExtraPrice = 0.0,
            totalDiscount = 0.0,
            netPayable = order.net(),
            deliveryCharges = 0.0,
            deliveryAddress = DeliveryAddress(),
            status = 1,
            createdBy = -1,
            deliveryType = 4,
            paymentType = order.paymentType,
            createdOn = trueTimeImpl.now().formatDate(DATE_FORMAT_BACK_END),
            orderStatusHistory = mutableListOf(),
            tax = order.tax ?: 0.0,
            modifiedBy = -1,
            modifiedOn = trueTimeImpl.now().formatDate(DATE_FORMAT_BACK_END),
            deliveryNote = "",
            customer = Customer(),
            transactionId = UUID.randomUUID().toString(),
            paymentId = 1122,
            trackingUrl = "",
            pickupTime = "",
            pandaOrderId = "",
            pandaOrderDetail = PandaOrderDetail(),
            lastUpdatedFromPanda = "",
            totalOptionPrice = totalOptionsPrice,
            promoCode = "",
            discountONPromo = 0.0,
            isPromoCodeAvailed = false,
            priceBeforePromo = 0,
            priceAfterPromo = 0,
            scheduled = false,
            scheduledDateTime = "",
            guest = false
        )

        val calendar = Calendar.getInstance()
        calendar.time = trueTimeImpl.now()
        calendar.add(Calendar.MINUTE, 15)
        val expireAt = calendar.time.formatDate(DATE_FORMAT_GUAVA)

        val orderRequest =
                CreateOrderRequest(
                        clientReference = UUID.randomUUID().toString(),
                        transactionType = "SALE",
                        paymentMethod = if (updatedOrder?.paymentType == 5) "MANUAL_ENTRY" else "CARD",
                        expireAt = expireAt,
                        totalAmount = updatedOrder?.totalPrice(
                            applyServiceCharge = state.serviceChargeApplied,
                            serviceChargePercentage = prefs.storeConfigurations?.data?.serviceChargePercentage ?: 0.0
                        ).toString(),
                        transactionAmount = updatedOrder?.totalPrice(
                            applyServiceCharge = state.serviceChargeApplied,
                            serviceChargePercentage = prefs.storeConfigurations?.data?.serviceChargePercentage ?: 0.0
                        ).toString(),
                        ccy = "GBP",
                        taxAmount = updatedOrder?.tax.toString(),
                )
        val guavaResponse = guavaRepository.createOrder(orderRequest)
        return guavaResponse
    }
}
