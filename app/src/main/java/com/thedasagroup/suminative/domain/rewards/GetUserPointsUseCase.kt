package com.thedasagroup.suminative.domain.rewards

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.repo.RewardsRepository
import kotlinx.coroutines.flow.StateFlow

class GetUserPointsUseCase(
    private val rewardsRepository: RewardsRepository
) {
    suspend operator fun invoke(
        userId: Int,
        businessId: Int
    ): StateFlow<Async<Double>> {
        return rewardsRepository.getUserPoints(userId, businessId)
    }
}
