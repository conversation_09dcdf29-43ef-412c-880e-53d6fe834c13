package com.thedasagroup.suminative.domain.orders

import com.thedasagroup.suminative.data.database.LocalOrderRepository
import com.thedasagroup.suminative.database.OrderEntity
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject

class GetLocalOrdersUseCase @Inject constructor(
    private val orderRepository: LocalOrderRepository
) {
    fun getAllOrders(): Flow<List<OrderEntity>> {
        return orderRepository.getAllOrdersFlow()
    }

    fun getOrdersByStatus(status: String): Flow<List<OrderEntity>> {
        return orderRepository.getOrdersByStatusFlow(status)
    }

    fun getPendingOrders(): Flow<List<OrderEntity>> {
        return orderRepository.getPendingOrdersFlow()
    }

    fun getUnsyncedOrders(): Flow<List<OrderEntity>> {
        return orderRepository.getUnsyncedOrdersFlow()
    }

    fun getSyncedOrders(): Flow<List<OrderEntity>> {
        return orderRepository.getSyncedOrdersFlow()
    }

    fun getOrdersByStore(storeId: Long): Flow<List<OrderEntity>> {
        return orderRepository.getOrdersByStoreFlow(storeId)
    }

    suspend fun updateOrderStatus(orderId: Long, status: String) {
        orderRepository.updateOrderStatus(orderId, status)
    }

    suspend fun markOrderComplete(orderId: Long) {
        orderRepository.markOrderComplete(orderId)
    }
} 