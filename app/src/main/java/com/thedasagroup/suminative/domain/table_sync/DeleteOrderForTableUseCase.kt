package com.thedasagroup.suminative.domain.table_sync

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.thedasagroup.suminative.data.model.response.table_sync.SyncOrderResponse
import com.thedasagroup.suminative.data.repo.SyncRepository
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject

/**
 * Use case for deleting order for a table
 * Uses DELETE request with Retrofit
 */
class DeleteOrderForTableUseCase @Inject constructor(
    private val syncRepository: SyncRepository
) {
    
    /**
     * Delete order for table
     * @param tableId The table ID to delete the order for
     * @return StateFlow with async result of delete operation
     */
    suspend operator fun invoke(
        tableId: Int
    ): StateFlow<Async<SyncOrderResponse>> {
        return syncRepository.deleteOrderForTable(tableId)
    }
}
