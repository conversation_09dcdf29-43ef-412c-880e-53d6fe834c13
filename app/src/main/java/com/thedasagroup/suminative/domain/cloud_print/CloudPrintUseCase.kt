package com.thedasagroup.suminative.domain.cloud_print

import com.airbnb.mvrx.Async
import com.instacart.truetime.time.TrueTimeImpl
import com.thedasagroup.suminative.data.model.request.cloud_print.CloudPrintRequest
import com.thedasagroup.suminative.data.model.request.order.Conversation
import com.thedasagroup.suminative.data.model.request.order.Customer
import com.thedasagroup.suminative.data.model.request.order.DeliveryAddress
import com.thedasagroup.suminative.data.model.request.order.FeedbackComplain
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.OrderRequest
import com.thedasagroup.suminative.data.model.request.order.OrderStatus
import com.thedasagroup.suminative.data.model.request.order.PandaOrderDetail
import com.thedasagroup.suminative.data.model.request.order.PaymentData
import com.thedasagroup.suminative.data.model.request.order.PromoCodes
import com.thedasagroup.suminative.data.model.request.order.SupportDetail
import com.thedasagroup.suminative.data.model.response.order.OrderResponse2
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.MyGuavaRepository
import com.thedasagroup.suminative.data.repo.StockRepository
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_BACK_END
import com.thedasagroup.suminative.ui.utils.formatDate
import kotlinx.coroutines.flow.StateFlow
import java.util.UUID

open class CloudPrintUseCase(
    private val stockRepository: StockRepository
) {
    suspend operator fun invoke(request: CloudPrintRequest): StateFlow<Async<OrderResponse2>> {
        val orderResponse = stockRepository.cloudPrint(
            request = request,
        )
        return orderResponse
    }
}