package com.thedasagroup.suminative.domain.rewards

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.request.rewards.AddPointsRequest
import com.thedasagroup.suminative.data.model.response.rewards.AddPointsResponse
import com.thedasagroup.suminative.data.repo.RewardsRepository
import kotlinx.coroutines.flow.StateFlow

open class AddPointsUseCase(
    private val rewardsRepository: RewardsRepository
) {
    suspend operator fun invoke(
        customerId: Int,
        businessId: Int,
        points: Int,
    ): StateFlow<Async<AddPointsResponse>> {
        val request = AddPointsRequest(
            userId = customerId,
            businessId = businessId,
            orderAmount = points
        )
        return rewardsRepository.addPoints(request)
    }
}
