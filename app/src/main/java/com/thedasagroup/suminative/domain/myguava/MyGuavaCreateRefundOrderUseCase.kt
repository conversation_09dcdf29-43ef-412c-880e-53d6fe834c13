package com.thedasagroup.suminative.domain.myguava

import com.airbnb.mvrx.Async
import com.instacart.truetime.time.TrueTimeImpl
import com.thedasagroup.suminative.data.model.request.my_guava.orders.CreateOrderRequest
import com.thedasagroup.suminative.data.model.request.order.Customer
import com.thedasagroup.suminative.data.model.request.order.DeliveryAddress
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.PandaOrderDetail
import com.thedasagroup.suminative.data.model.response.my_guava.orders.create_order.GuavaOrderResponse
import com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GuavaOrder
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.MyGuavaRepository
import com.thedasagroup.suminative.data.repo.StockRepository
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_BACK_END
import com.thedasagroup.suminative.ui.utils.formatDate
import kotlinx.coroutines.flow.StateFlow
import java.util.UUID

open class MyGuavaCreateRefundOrderUseCase(
        private val guavaRepository: MyGuavaRepository
) {
    suspend operator fun invoke(guavaOrder : GuavaOrder): StateFlow<Async<GuavaOrderResponse>> {

        val orderRequest =
                CreateOrderRequest(
                        clientReference = guavaOrder.clientReference ?: "" ,
                        transactionType = "REFUND",
                        paymentMethod = "CARD",
                        expireAt = "2026-01-01T20:00:01.000",
                        totalAmount = guavaOrder.totalAmount.toString(),
                        transactionAmount = guavaOrder.transactionAmount.toString(),
                        ccy = "GBP",
                        taxAmount = guavaOrder.taxAmount.toString(),
                )
        val guavaResponse = guavaRepository.createOrder(orderRequest)
        return guavaResponse
    }
}
