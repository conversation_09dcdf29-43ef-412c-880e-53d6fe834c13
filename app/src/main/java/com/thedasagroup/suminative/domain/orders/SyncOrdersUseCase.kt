package com.thedasagroup.suminative.domain.orders

import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.database.LocalOrderRepository
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.Customer
import com.thedasagroup.suminative.data.model.request.order.DeliveryAddress
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.PandaOrderDetail
import com.thedasagroup.suminative.data.model.request.order.StoreItem
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.StockRepository
import com.thedasagroup.suminative.database.OrderEntity
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_BACK_END2
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.runBlocking
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import javax.inject.Inject

class SyncOrdersUseCase @Inject constructor(
    private val localOrderRepository: LocalOrderRepository,
    private val stockRepository: StockRepository,
    private val prefs: Prefs
) {

    suspend fun syncAllUnsyncedOrders(): Flow<SyncResult2> = flow {
        val unsyncedOrders = localOrderRepository.getUnsyncedOrders()

        if (unsyncedOrders.isEmpty()) {
            emit(SyncResult2(syncedCount = 0, failedCount = 0, totalCount = 0))
            return@flow
        }

        var syncedCount = 0
        var failedCount = 0
        val totalCount = unsyncedOrders.size

        for (localOrder in unsyncedOrders) {
            try {
                // Convert local order to API format
                val orderRequest = convertLocalOrderToApiOrder(localOrder)

                // Sync to remote server using createNewPOSOrder
                val response = stockRepository.placeOrder(orderRequest)
                val result = response.value
                if (result is Success) {
                    // Mark order as synced
                    localOrderRepository.markOrderSynced(localOrder.id)
                    syncedCount++
                } else {
                    failedCount++
                }
            } catch (e: Exception) {
                failedCount++
                e.printStackTrace()
            }
        }

        emit(SyncResult2(syncedCount = syncedCount, failedCount = failedCount, totalCount = totalCount))
    }

    suspend fun syncSingleOrder(orderId: String): Flow<Boolean> = flow {
        try {
            // Get the specific order from local database
            val localOrder = localOrderRepository.getOrderByOrderId(orderId)

            if (localOrder == null) {
                emit(false)
                return@flow
            }

            // Convert local order to API format
            val orderRequest = convertLocalOrderToApiOrder(localOrder)

            // Sync to remote server using createNewPOSOrder
            val response = stockRepository.placeOrder(orderRequest)
            response.collect { result ->
                if (result is Success) {
                    // Mark order as synced
                    localOrderRepository.markOrderSynced(localOrder.id)
                    emit(true)
                } else {
                    emit(false)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            emit(false)
        }
    }

    private suspend fun convertLocalOrderToApiOrder(localOrder: OrderEntity): Order {
        val dateFormatter = SimpleDateFormat(DATE_FORMAT_BACK_END2, Locale.getDefault())
        val formattedDate = dateFormatter.format(Date(localOrder.createdAt))

        // Get order items for this order
        val orderItems = localOrderRepository.getOrderItemsByOrderId(localOrder.orderId)

        // Convert order items to cart format
        val carts = orderItems.map { item ->
            Cart(
                storeItem = StoreItem(
                    id = item.itemId.toInt(),
                    name = item.itemName,
                    description = item.itemDescription,
                    price = item.unitPrice,
                    optionSets = emptyList()
                ),
                quantity = item.quantity.toInt(),
                netPayable = item.totalPrice,
            )
        }

        return Order(
            businessId = prefs.store?.businessId ?: 0,
            storeId = prefs.store?.id ?: 0,
            customerId = localOrder.customerId?.toInt() ?: -1,
            createdBy = localOrder.waiterId?.toInt() ?: prefs.selectedWaiter?.id ?: -1,
            createdOn = formattedDate,
            customer = Customer(
                name = localOrder.customerName ?: "Guest",
                phone = localOrder.customerPhone
            ),
            deliveryAddress = if (!localOrder.deliveryAddress.isNullOrEmpty()) {
                DeliveryAddress(address = localOrder.deliveryAddress)
            } else {
                DeliveryAddress()
            },
            deliveryType = when (localOrder.orderType) {
                "DINE_IN" -> 4
                "DELIVERY" -> 1
                "TAKEAWAY" -> 2
                else -> 4
            },
            paymentType = when (localOrder.paymentMethod) {
                "CASH" -> 5
                "CARD" -> 6
                "DIGITAL" -> 7
                else -> 5
            },
            status = when (localOrder.status) {
                "PENDING" -> 1
                "ACCEPTED" -> 2
                "PREPARING" -> 3
                "READY" -> 4
                "COMPLETED" -> 5
                "CANCELLED" -> 6
                else -> 1
            },
            totalPrice = localOrder.subtotal + localOrder.tax,
            tax = localOrder.tax,
            totalDiscount = localOrder.discount,
            netPayable = localOrder.total,
            deliveryCharges = 0.0,
            totalExtraPrice = 0.0,
            totalOptionPrice = 0.0,
            deliveryNote = localOrder.notes,
            carts = carts,
            pandaOrderDetail = PandaOrderDetail(),
            discountONPromo = 0.0,
            isPromoCodeAvailed = false,
            priceBeforePromo = 0,
            priceAfterPromo = 0,
            scheduled = false,
            scheduledDateTime = "",
            guest = localOrder.customerId == null,
            promoCode = "",
            transactionId = localOrder.orderId,
            paymentId = 0,
            trackingUrl = "",
            pickupTime = "",
            pandaOrderId = "",
            lastUpdatedFromPanda = "",
            modifiedBy = -1,
            modifiedOn = formattedDate,
            orderStatusHistory = emptyList()
        )
    }
}

sealed class SyncResult {
    data class Success(val syncedCount: Int, val message: String) : SyncResult()
    data class Error(val error: String) : SyncResult()
}

data class SyncResult2(
    val syncedCount: Int,
    val failedCount: Int,
    val totalCount: Int
)