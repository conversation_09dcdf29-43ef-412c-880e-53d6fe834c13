package com.thedasagroup.suminative.domain.table_sync

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.response.table_sync.TableResponse
import com.thedasagroup.suminative.data.repo.SyncRepository
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject

/**
 * Use case for toggling table occupied status
 * Uses POST request with API client
 */
class ToggleTableOccupiedUseCase @Inject constructor(
    private val syncRepository: SyncRepository
) {
    
    /**
     * Toggle table occupied status
     * @param tableId The table ID to toggle occupied status
     * @param netPayable The net payable amount for the table
     * @return StateFlow with async result of toggle operation
     */
    suspend operator fun invoke(
        tableId: Int,
        netPayable: Double
    ): StateFlow<Async<TableResponse>> {
        return syncRepository.toggleTableOccupied(tableId, netPayable)
    }
}
