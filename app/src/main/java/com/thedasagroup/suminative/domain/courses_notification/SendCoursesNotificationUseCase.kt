package com.thedasagroup.suminative.domain.courses_notification

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.request.print.CoursesNotificationRequest
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.response.courses_notification.CoursesNotificationResponse
import com.thedasagroup.suminative.data.repo.PrintRepository
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject

/**
 * Use case for sending courses notification to printer
 */
class SendCoursesNotificationUseCase @Inject constructor(
    private val printRepository: PrintRepository
) {
    
    /**
     * Send courses notification using request object
     */
    suspend operator fun invoke(
        request: CoursesNotificationRequest
    ): StateFlow<Async<CoursesNotificationResponse>> {
        return printRepository.sendCoursesNotification(request)
    }
    
    /**
     * Send courses notification using individual parameters
     */
    suspend operator fun invoke(
        storeId: Int,
        courseName: String,
        tableName: String,
        cartJson: String
    ): StateFlow<Async<CoursesNotificationResponse>> {
        return printRepository.sendCoursesNotification(
            storeId = storeId,
            courseName = courseName,
            tableName = tableName,
            cartJson = cartJson
        )
    }
    
    /**
     * Send courses notification using Cart list
     */
    suspend operator fun invoke(
        storeId: Int,
        courseName: String,
        tableName: String,
        carts: List<Cart>
    ): StateFlow<Async<CoursesNotificationResponse>> {
        return printRepository.sendCoursesNotification(
            storeId = storeId,
            courseName = courseName,
            tableName = tableName,
            carts = carts
        )
    }
}
