package com.thedasagroup.suminative.domain.table_sync

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.thedasagroup.suminative.data.model.request.table_sync.SyncOrderRequest
import com.thedasagroup.suminative.data.model.request.table_sync.UpdateOrderRequest
import com.thedasagroup.suminative.data.model.response.table_sync.SyncOrderResponse
import com.thedasagroup.suminative.data.repo.SyncRepository
import io.ktor.client.request.request
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject

/**
 * Use case for updating order for a table
 * Uses PUT request with API client
 */
class UpdateOrderForTableUseCase @Inject constructor(
    private val syncRepository: SyncRepository
) {
    
    /**
     * Update order for table
     * @param tableId The table ID to update the order for
     * @param request The update order request containing order data
     * @return StateFlow with async result of update operation
     */
    suspend operator fun invoke(
        tableId: Int,
        request: UpdateOrderRequest
    ): StateFlow<Async<SyncOrderResponse>> {
        val flow = syncRepository.updateOrderForTable(tableId, request)
        return flow
    }
}
