package com.thedasagroup.suminative.domain.sales_report

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.request.sales.SalesRequest
import com.thedasagroup.suminative.data.model.response.sales.SalesReportResponse
import com.thedasagroup.suminative.data.model.response.sales.SalesResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.SalesRepository
import kotlinx.coroutines.flow.StateFlow

class GetSalesReportUseCase(private val salesRepository: SalesRepository, private val prefs : Prefs) {
    suspend operator fun invoke(request: SalesRequest): StateFlow<Async<SalesReportResponse>> {
        return salesRepository.getSalesReport(
            request = request
        )
    }
}