package com.thedasagroup.suminative.domain.orders

import com.thedasagroup.suminative.data.database.LocalOrder
import com.thedasagroup.suminative.data.database.LocalOrderItem
import com.thedasagroup.suminative.data.database.LocalOrderRepository
import java.util.UUID
import javax.inject.Inject

class CreateOrderUseCase @Inject constructor(
    private val orderRepository: LocalOrderRepository
) {
    suspend fun createOrder(
        customerId: Long? = null,
        customerName: String? = null,
        customerPhone: String? = null,
        orderType: String,
        storeId: Long,
        waiterId: Long? = null,
        waiterName: String? = null,
        tableNumber: Long? = null,
        deliveryAddress: String? = null,
        notes: String? = null
    ): String {
        val orderId = UUID.randomUUID().toString()
        
        val order = LocalOrder(
            orderId = orderId,
            customerId = customerId,
            customerName = customerName,
            customerPhone = customerPhone,
            orderType = orderType,
            status = "PENDING",
            storeId = storeId,
            waiterId = waiterId,
            waiterName = waiterName,
            tableNumber = tableNumber,
            deliveryAddress = deliveryAddress,
            notes = notes
        )
        
        orderRepository.insertOrder(order)
        return orderId
    }
    
    suspend fun addItemToOrder(
        orderId: String,
        itemId: Long,
        itemName: String,
        itemDescription: String? = null,
        quantity: Long = 1,
        unitPrice: Double,
        category: String? = null,
        modifiers: String? = null,
        notes: String? = null
    ) {
        val orderItem = LocalOrderItem(
            orderId = orderId,
            itemId = itemId,
            itemName = itemName,
            itemDescription = itemDescription,
            quantity = quantity,
            unitPrice = unitPrice,
            totalPrice = unitPrice * quantity,
            category = category,
            modifiers = modifiers,
            notes = notes
        )
        
        orderRepository.insertOrderItem(orderItem)
    }
    
    suspend fun updateOrderTotals(
        orderId: String,
        subtotal: Double,
        tax: Double,
        discount: Double = 0.0
    ) {
        val order = orderRepository.getOrderByOrderId(orderId)
        if (order != null) {
            val total = subtotal + tax - discount
            // Note: This would require adding an update method to the repository
            // For now, this is just to show the concept
        }
    }
} 