package com.thedasagroup.suminative.domain.table_sync

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.encode
import com.thedasagroup.suminative.data.model.request.table_sync.OrderCourse
import com.thedasagroup.suminative.data.model.request.table_sync.SyncOrderRequest
import com.thedasagroup.suminative.data.model.request.table_sync.UpdateOrderRequest
import com.thedasagroup.suminative.data.model.response.table_sync.SyncOrderResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.SyncRepository
import com.thedasagroup.suminative.ui.products.ProductsScreenState
import kotlinx.coroutines.flow.StateFlow
import java.util.UUID
import javax.inject.Inject

/**
 * Use case for getting synced order for a table
 * Uses GET request with Retrofit
 */
class CreateOrUpdateOrderForTableUseCase @Inject constructor(
    private val syncRepository: SyncRepository,
    val prefs: Prefs
) {

    /**
     * Get synced order for table
     * @param tableId The table ID to get the synced order for
     * @return StateFlow with async result of get operation
     */
    suspend operator fun invoke(
        tableId: Int,
        order: Order,
        state: ProductsScreenState
    ): StateFlow<Async<SyncOrderResponse>> {
        val flow = syncRepository.getSyncedOrderForTable(tableId)
        return when (flow.value) {
            is Success<*> -> {
                val response = (flow.value as Success<SyncOrderResponse>).invoke()
                if (response.success && response.data != null) {
                    // Update order when cart changes (and it's not the first item)
                    val updateRequest =
                        createUpdateOrderRequest(tableId, order, state = state)
                    return syncRepository.updateOrderForTable(
                        tableId,
                        updateRequest
                    )
                } else {
                    val syncRequest =
                        createSyncOrderRequest(tableId, order, state = state)
                    return syncRepository.syncOrderToTable(syncRequest)
                }
            }

            else -> {
                flow
            }
        }

    }

    private fun createUpdateOrderRequest(
        tableId: Int,
        order: Order,
        state: ProductsScreenState
    ): UpdateOrderRequest {
        val orderCourses = createOrderCoursesFromCart(order, tableId, state = state)
        val courseStatusQueue = state.getStatusQueue()
        return UpdateOrderRequest(
            tableId = tableId,
            customerId = order.customerId ?: 0,
            businessId = order.businessId ?: prefs.store?.businessId ?: 0,
            netPayable = order.net() ?: 0.0,
            orderCourses = orderCourses,
            goQueue = courseStatusQueue.goQueue.joinToString(","),
            preparingQueue = courseStatusQueue.preparingQueue.joinToString(","),
            completeQueue = courseStatusQueue.completedCourses.joinToString(","),
            deviceId = prefs.localDeviceId ?: UUID.randomUUID().toString()
        )
    }

    private fun createSyncOrderRequest(
        tableId: Int,
        order: Order,
        state: ProductsScreenState
    ): SyncOrderRequest {
        val orderCourses = createOrderCoursesFromCart(order, tableId, state = state)
        val courseStatusQueue = state.getStatusQueue()
        return SyncOrderRequest(
            tableId = tableId,
            customerId = order.customerId ?: 0,
            businessId = order.businessId ?: prefs.store?.businessId ?: 0,
            netPayable = order.net() ?: 0.0,
            orderCourses = orderCourses,
            goQueue = courseStatusQueue.goQueue.joinToString(","),
            preparingQueue = courseStatusQueue.preparingQueue.joinToString(","),
            completeQueue = courseStatusQueue.completedCourses.joinToString(","),
            deviceId = prefs.localDeviceId ?: UUID.randomUUID().toString()
        )
    }

    private fun createOrderCoursesFromCart(
        order: Order,
        tableId: Int,
        state: ProductsScreenState
    ): List<OrderCourse> {
        val cartItemsWithCourses = state.cartItemsWithCourses[tableId] ?: emptyList()

        // Group cart items by course
        val courseGroups = cartItemsWithCourses.groupBy { it.courseId }
        val updatedOrderCourses = mutableListOf<OrderCourse>()

        courseGroups.forEach { (courseId, items) ->
            val course = state.tableAvailableCourses[tableId]?.find { it.name == courseId }
            val courseName = course?.name ?: courseId
            val updatedCourseName = courseName
            val cartJson = createCartJsonFromItems(items.map { it.cart })

            // The courseId from CartItemWithCourse should match what's in the status queue
            // Since loadExistingOrderToTable sets courseId = course.coursesName,
            // we should use courseId directly for status lookup
//            val courseStatusKey = courseId

//            val courseStatus = when {
//                courseStatusQueue.isInGoQueue(courseStatusKey) -> 0
//                courseStatusQueue.isInPreparingQueue(courseStatusKey) -> 1
//                courseStatusQueue.isCompleted(courseStatusKey) -> 2
//                else -> 0 // Default to GO status
//            }

            updatedOrderCourses.add(
                OrderCourse(
                    coursesName = updatedCourseName,
                    cartJson = cartJson,
                    sortOrder = course?.sortOrder ?: 0
                )
            )
        }
        return updatedOrderCourses
    }

    private fun createCartJsonFromItems(carts: List<Cart>): String {
        return carts.encode()
    }
}
