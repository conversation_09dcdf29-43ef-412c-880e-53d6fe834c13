package com.thedasagroup.suminative.domain.rewards

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.response.rewards.RewardsOverviewResponse
import com.thedasagroup.suminative.data.repo.RewardsRepository
import kotlinx.coroutines.flow.StateFlow

class GetRewardsOverviewUseCase(
    private val rewardsRepository: RewardsRepository
) {
    suspend operator fun invoke(
        userId: Int,
        businessId: Int
    ): StateFlow<Async<RewardsOverviewResponse>> {
        return rewardsRepository.getRewardsOverview(userId, businessId)
    }
}
