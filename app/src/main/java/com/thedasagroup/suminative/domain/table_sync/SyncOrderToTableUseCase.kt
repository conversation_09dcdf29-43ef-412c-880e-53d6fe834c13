package com.thedasagroup.suminative.domain.table_sync

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.request.table_sync.SyncOrderRequest
import com.thedasagroup.suminative.data.model.response.table_sync.SyncOrderResponse
import com.thedasagroup.suminative.data.repo.SyncRepository
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject

/**
 * Use case for syncing order to table
 * Uses POST request with API client
 */
class SyncOrderToTableUseCase @Inject constructor(
    private val syncRepository: SyncRepository
) {
    
    /**
     * Sync order to table
     * @param request The sync order request containing table and order data
     * @return StateFlow with async result of sync operation
     */
    suspend operator fun invoke(
        request: SyncOrderRequest
    ): StateFlow<Async<SyncOrderResponse>> {
        return syncRepository.syncOrderToTable(request)
    }
}
