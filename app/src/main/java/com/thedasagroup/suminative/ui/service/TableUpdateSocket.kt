package com.thedasagroup.suminative.ui.service

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
@Parcelize
data class TableUpdateSocket(
    @SerialName("tableId") val tableId: Int? = null,
    @SerialName("storeId") val storeId: Int? = null,
    @SerialName("action") val action: String? = null,
    @SerialName("deviceId") val deviceId: String? = null
) : Parcelable
