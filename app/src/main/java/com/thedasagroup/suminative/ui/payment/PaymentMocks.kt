package com.thedasagroup.suminative.ui.payment

import com.airbnb.mvrx.Success
import com.airbnb.mvrx.mocking.mockSingleViewModel
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.Customer
import com.thedasagroup.suminative.data.model.request.order.DeliveryAddress
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.PandaOrderDetail
import com.thedasagroup.suminative.data.model.request.order.StoreItem

fun PaymentFragment.mocks() = mockSingleViewModel(
    PaymentFragment::paymentViewModel,
    defaultState = PaymentState(),
    defaultArgs = null
){
    state(name = "Loading") {
        setLoading { ::order }
    }
    state(name = "Order with Values") {
        set { ::order }.with {
            Success(
                Order(
                    businessId = 95,
                    carts = listOf(
                        Cart(
                            discount = 0.0,
                            extraPrice = 0.0,
                            isB1G1 = false,
                            netPayable = 6.5,
                            optionPrice = 0.0,
                            price = 6.5,
                            quantity = 1,
                            storeItem = StoreItem(
                                additionalInfo = "",
                                billAmount = 6.5,
                                brandId = 54,
                                businessId = 95,
                                categoryId = 414,
                                createdBy = 197,
                                createdOn = "2025-02-17T13:30:24Z",
                                dailyCapacity = 100,
                                description = "CHEESE + MUSHROOM",
                                discountType = 2,
                                discountedAmount = 0.0,
                                discounttypename = "Flat",
                                extras = emptyList(),
                                id = 3447,
                                ingredients = "",
                                modifiedBy = -1,
                                modifiedOn = "",
                                name = "CHEESE + MUSHROOM",
                                optionSets = emptyList(),
                                pic = "173979902434898844.jpeg",
                                preparationTime = "",
                                price = 6.5,
                                quantity = 1,
                                servingSize = "",
                                storeId = 145,
                                tax = 0.0,
                                unitId = -1,
                                unitName = "Select Unit",
                                vat = true
                            ),
                            tax = 0.0
                        )
                    ),
                    createdBy = -1,
                    createdOn = "2025-03-29T02:36:27Z",
                    customer = Customer(),
                    customerId = -1,
                    deliveryAddress = DeliveryAddress(),
                    deliveryCharges = 0.0,
                    deliveryNote = "",
                    deliveryType = 4,
                    discountONPromo = 0.0,
                    guest = false,
                    id = -1,
                    isPromoCodeAvailed = false,
                    lastUpdatedFromPanda = "",
                    modifiedBy = -1,
                    modifiedOn = "2025-03-29T02:36:27Z",
                    netPayable = 6.5,
                    orderStatusHistory = emptyList(),
                    pandaOrderDetail = PandaOrderDetail(),
                    pandaOrderId = "",
                    paymentId = 1122,
                    paymentType = 6,
                    pickupTime = "",
                    priceAfterPromo = 0,
                    priceBeforePromo = 0,
                    promoCode = "",
                    scheduled = false,
                    scheduledDateTime = "",
                    status = 1,
                    storeId = 145,
                    tax = 0.0,
                    totalDiscount = 0.0,
                    totalExtraPrice = 0.0,
                    totalOptionPrice = 0.0,
                    totalPrice = 6.5,
                    trackingUrl = "",
                    transactionId = "77086332-9047-4725-8cc3-a4af8a1f10cb"
                )
            )
        }
    }
}