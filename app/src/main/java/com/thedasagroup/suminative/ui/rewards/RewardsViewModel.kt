package com.thedasagroup.suminative.ui.rewards

import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.MavericksViewModelFactory
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.hilt.AssistedViewModelFactory
import com.airbnb.mvrx.hilt.hiltMavericksViewModelFactory
import com.thedasagroup.suminative.data.model.response.rewards.RewardsOverviewResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.domain.rewards.AddPointsUseCase
import com.thedasagroup.suminative.domain.rewards.GetAllCustomersUseCase
import com.thedasagroup.suminative.domain.rewards.GetUserPointsUseCase
import com.thedasagroup.suminative.domain.rewards.GetRewardsOverviewUseCase
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject

class RewardsViewModel @AssistedInject constructor(
    @Assisted state: RewardsState,
    private val getAllCustomersUseCase: GetAllCustomersUseCase,
    private val addPointsUseCase: AddPointsUseCase,
    private val getUserPointsUseCase: GetUserPointsUseCase,
    private val getRewardsOverviewUseCase: GetRewardsOverviewUseCase,
    val prefs: Prefs
) : MavericksViewModel<RewardsState>(state) {

    @AssistedFactory
    interface Factory : AssistedViewModelFactory<RewardsViewModel, RewardsState> {
        override fun create(state: RewardsState): RewardsViewModel
    }

    companion object :
        MavericksViewModelFactory<RewardsViewModel, RewardsState> by hiltMavericksViewModelFactory()

    /**
     * Get all customers for a specific customer ID and business ID
     */
    suspend fun getAllCustomers(customerId: Int, businessId: Int) {
        val actualBusinessId = businessId
        
        setState {
            copy(
                getAllCustomersResponse = Loading(),
                isLoading = true,
                selectedCustomerId = customerId,
                selectedBusinessId = actualBusinessId
            )
        }

        getAllCustomersUseCase(customerId, businessId).execute { response ->
            when (response) {
                is Success -> {
                    copy(
                        getAllCustomersResponse = response(),
                        isLoading = false
                    )
                }
                else -> {
                    copy(
                        getAllCustomersResponse = Uninitialized,
                        isLoading = false
                    )
                }
            }
        }
    }

    /**
     * Add points to a customer's rewards account
     */
    suspend fun addPoints(
        customerId: Int,
        points: Int,
        businessId: Int,
        orderId: Int? = null,
        description: String? = null
    ) {
        setState {
            copy(
                addPointsResponse = Loading(),
                isLoading = true
            )
        }

        addPointsUseCase(
            customerId = customerId,
            businessId = businessId,
            points = points
        ).execute { response ->
            when (response) {
                is Success -> {
                    copy(
                        addPointsResponse = response(),
                        isLoading = false
                    )
                }
                else -> {
                    copy(
                        addPointsResponse = Uninitialized,
                        isLoading = false
                    )
                }
            }
        }
    }

    /**
     * Clear the add points response state
     */
    fun clearAddPointsResponse() {
        setState {
            copy(addPointsResponse = Uninitialized)
        }
    }

    /**
     * Clear the get all customers response state
     */
    fun clearGetAllCustomersResponse() {
        setState {
            copy(getAllCustomersResponse = Uninitialized)
        }
    }

    /**
     * Get user points for a specific user and business
     */
    suspend fun getUserPoints(userId: Int, businessId: Int) {
        setState {
            copy(
                userPointsResponse = Loading(),
                isLoading = true
            )
        }

        getUserPointsUseCase(userId, businessId).execute { response ->
            when (response) {
                is Success -> {
                    copy(
                        userPointsResponse = response(),
                        isLoading = false
                    )
                }
                else -> {
                    copy(
                        userPointsResponse = Uninitialized,
                        isLoading = false
                    )
                }
            }
        }
    }

    /**
     * Get rewards overview for a specific user and business
     */
    suspend fun getRewardsOverview(userId: Int, businessId: Int) {

        setState {
            copy(
                rewardsOverviewResponse = Loading(),
                isLoading = true
            )
        }

        getRewardsOverviewUseCase(userId, businessId).execute { response ->
            copy(
                rewardsOverviewResponse = response() ?: Success(RewardsOverviewResponse()),
                isLoading = false
            )
        }
    }

    /**
     * Clear the user points response state
     */
    fun clearUserPointsResponse() {
        setState {
            copy(userPointsResponse = Uninitialized)
        }
    }

    /**
     * Clear the rewards overview response state
     */
    fun clearRewardsOverviewResponse() {
        setState {
            copy(rewardsOverviewResponse = Uninitialized)
        }
    }

    /**
     * Reset all state to initial values
     */
    fun resetState() {
        setState {
            copy(
                getAllCustomersResponse = Uninitialized,
                addPointsResponse = Uninitialized,
                userPointsResponse = Uninitialized,
                rewardsOverviewResponse = Uninitialized,
                isLoading = false,
                selectedCustomerId = null,
                selectedBusinessId = null,
                customerIdInput = "",
                showRewardsAvailScreen = false,
                selectedCustomer = null,
            )
        }
    }

    /**
     * Update customer ID input
     */
    fun updateCustomerIdInput(input: String) {
        setState {
            copy(customerIdInput = input)
        }
    }

    /**
     * Set selected customer
     */
    fun setSelectedCustomer(customer: com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer?) {
        setState {
            copy(selectedCustomer = customer)
        }
    }

    /**
     * Show/hide rewards available screen
     */
    fun setShowRewardsAvailScreen(show: Boolean) {
        setState {
            copy(showRewardsAvailScreen = show)
        }
    }

    fun showRewardsDialog(show: Boolean){
        setState {
            copy(showRewardsDialog = show)
        }
    }

    /**
     * Clear selected customer and hide banner
     */
    fun clearSelectedCustomer() {
        setState {
            copy(
                selectedCustomer = null,
            )
        }
    }
}
