package com.thedasagroup.suminative.ui.stock

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.database.CategoryRepository
import com.thedasagroup.suminative.data.model.request.category_sorting.CategorySortingRequest
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.StockRepository
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class CategorySortingHelper @Inject constructor(
    private val stockRepository: StockRepository,
    private val categoryRepository: CategoryRepository,
    private val prefs: Prefs
) {
    
    // Get categories for sorting with database-first approach
    suspend fun getCategoriesForSorting(storeId: Long): List<String> {
        return try {
            // First try to get from database
            val categoriesFromDb = categoryRepository.getCategoryNamesSorted(storeId)
            
            if (categoriesFromDb.isNotEmpty()) {
                // Return database categories if available
                categoriesFromDb
            } else {
                // Fallback to API and sync to database
                val apiCategories = getCategoriesFromApiAndSync(storeId)
                apiCategories
            }
        } catch (e: Exception) {
            // Final fallback to API only
            getCategoriesFromApiDirectly(storeId)
        }
    }
    
    // Get categories from API and sync to database
    private suspend fun getCategoriesFromApiAndSync(storeId: Long): List<String> {
        return try {
            val categorySortingResult = stockRepository.getCategorySorting(
                CategorySortingRequest(storeId = storeId.toString())
            )
            
            if (categorySortingResult.value is Success) {
                val categories = categorySortingResult.value()?.categories ?: emptyList()
                if (categories.isNotEmpty()) {
                    // Sync to database for future use
                    categoryRepository.syncCategoriesFromApi(categories, storeId)
                }
                categories
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    // Fallback method to get categories directly from API without database sync
    private suspend fun getCategoriesFromApiDirectly(storeId: Long): List<String> {
        return try {
            val categorySortingResult = stockRepository.getCategorySorting(
                CategorySortingRequest(storeId = storeId.toString())
            )
            
            if (categorySortingResult.value is Success) {
                categorySortingResult.value()?.categories ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    // Force sync categories from API to database
    suspend fun forceSyncCategories(storeId: Long): Boolean {
        return try {
            getCategoriesFromApiAndSync(storeId)
            true
        } catch (e: Exception) {
            false
        }
    }
    
    // Check if categories exist in database
    suspend fun hasCategoriesInDatabase(storeId: Long): Boolean {
        return try {
            categoryRepository.hasCategoriesForStore(storeId)
        } catch (e: Exception) {
            false
        }
    }
    
    // Update category sort order
    suspend fun updateCategoryOrder(categories: List<String>, storeId: Long): Boolean {
        return try {
            val categoriesWithIndex = categories.mapIndexed { index, category ->
                category to index.toLong()
            }
            categoryRepository.updateCategoriesSortOrder(categoriesWithIndex, storeId)
            true
        } catch (e: Exception) {
            false
        }
    }
} 