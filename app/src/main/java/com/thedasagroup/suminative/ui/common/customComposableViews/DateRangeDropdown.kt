package com.thedasagroup.suminative.ui.common.customComposableViews

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowDropDown
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_DATE_ONLY
import com.thedasagroup.suminative.ui.utils.formatDate
import java.util.Calendar

enum class DateRange {
    All,
    Today,
    Yesterday,
    LastSevenDays,
    LastThirtyDays,
    ThisMonth,
    LastMonth,
    CustomRange
}

@Composable
fun DateRangeDropdown(
    selectedRange: DateRange,
    onRangeSelected: (String, String, DateRange) -> Unit,
    modifier: Modifier = Modifier,
    viewModel: ProductsScreenViewModel
) {
    var expanded by remember { mutableStateOf(false) }
    var showCustomDatePicker by remember { mutableStateOf(false) }
    
    // Handle custom date range picker
    if (showCustomDatePicker) {
        CustomDateRangePicker(
            onDateRangeSelected = { startDate, endDate ->
                onRangeSelected(startDate, endDate, DateRange.CustomRange)
                showCustomDatePicker = false
            },
            onDismiss = {
                showCustomDatePicker = false
            },
            viewModel = viewModel
        )
    }

    Box(modifier = modifier) {
        Row(
            modifier = Modifier
                .clickable { expanded = true }
                .padding(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = when (selectedRange) {
                    DateRange.All -> "All Time"
                    DateRange.Today -> "Today"
                    DateRange.Yesterday -> "Yesterday"
                    DateRange.LastSevenDays -> "Last 7 Days"
                    DateRange.LastThirtyDays -> "Last 30 Days"
                    DateRange.ThisMonth -> "This Month"
                    DateRange.LastMonth -> "Last Month"
                    DateRange.CustomRange -> "Custom Range"
                }
            )
            Icon(
                imageVector = Icons.Default.ArrowDropDown,
                contentDescription = "Select date range"
            )
        }

        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { expanded = false }
        ) {
            DateRange.values().forEach { range ->
                DropdownMenuItem(
                    text = {
                        Text(
                            text = when (range) {
                                DateRange.All -> "All"
                                DateRange.Today -> "Today"
                                DateRange.Yesterday -> "Yesterday"
                                DateRange.LastSevenDays -> "Last 7 Days"
                                DateRange.LastThirtyDays -> "Last 30 Days"
                                DateRange.ThisMonth -> "This Month"
                                DateRange.LastMonth -> "Last Month"
                                DateRange.CustomRange -> "Custom Range"
                            }
                        )
                    },
                    onClick = {
                        if (range == DateRange.CustomRange) {
                            showCustomDatePicker = true
                            expanded = false
                        } else {
                            val now = viewModel.trueTimeImpl.now()
                            val calendar = Calendar.getInstance()
                            calendar.time = now
                            
                            // Reset time to start of day (00:00:00)
                            calendar.set(Calendar.HOUR_OF_DAY, 0)
                            calendar.set(Calendar.MINUTE, 0)
                            calendar.set(Calendar.SECOND, 0)
                            calendar.set(Calendar.MILLISECOND, 0)
                            
                            val today = calendar.time.formatDate(DATE_FORMAT_DATE_ONLY)
                            
                            // Yesterday
                            calendar.add(Calendar.DAY_OF_MONTH, -1)
                            val yesterday = calendar.time.formatDate(DATE_FORMAT_DATE_ONLY)
                            
                            // Last 7 days
                            calendar.time = now // Reset to current date
                            calendar.set(Calendar.HOUR_OF_DAY, 0)
                            calendar.set(Calendar.MINUTE, 0)
                            calendar.set(Calendar.SECOND, 0)
                            calendar.set(Calendar.MILLISECOND, 0)
                            calendar.add(Calendar.DAY_OF_MONTH, -6) // 6 days ago plus today = 7 days
                            val last7Days = calendar.time.formatDate(DATE_FORMAT_DATE_ONLY)
                            
                            // Last 30 days
                            calendar.time = now // Reset to current date
                            calendar.set(Calendar.HOUR_OF_DAY, 0)
                            calendar.set(Calendar.MINUTE, 0)
                            calendar.set(Calendar.SECOND, 0)
                            calendar.set(Calendar.MILLISECOND, 0)
                            calendar.add(Calendar.DAY_OF_MONTH, -29) // 29 days ago plus today = 30 days
                            val last30Days = calendar.time.formatDate(DATE_FORMAT_DATE_ONLY)
                            
                            // This month (first day of current month)
                            calendar.time = now
                            calendar.set(Calendar.HOUR_OF_DAY, 0)
                            calendar.set(Calendar.MINUTE, 0)
                            calendar.set(Calendar.SECOND, 0)
                            calendar.set(Calendar.MILLISECOND, 0)
                            calendar.set(Calendar.DAY_OF_MONTH, 1)
                            val thisMonth = calendar.time.formatDate(DATE_FORMAT_DATE_ONLY)
                            
                            // Last month (first day of previous month)
                            calendar.time = now
                            calendar.set(Calendar.HOUR_OF_DAY, 0)
                            calendar.set(Calendar.MINUTE, 0)
                            calendar.set(Calendar.SECOND, 0)
                            calendar.set(Calendar.MILLISECOND, 0)
                            calendar.add(Calendar.MONTH, -1)
                            calendar.set(Calendar.DAY_OF_MONTH, 1)
                            val lastMonth = calendar.time.formatDate(DATE_FORMAT_DATE_ONLY)

                            // last day of last month
                            calendar.time = now
                            calendar.set(Calendar.HOUR_OF_DAY, 0)
                            calendar.set(Calendar.MINUTE, 0)
                            calendar.set(Calendar.SECOND, 0)
                            calendar.set(Calendar.MILLISECOND, 0)
                            calendar.set(Calendar.DAY_OF_MONTH, 1) // First day of current month
                            calendar.add(Calendar.DAY_OF_MONTH, -1) // Last day of previous month
                            val lastDayOfLastMonth = calendar.time.formatDate(DATE_FORMAT_DATE_ONLY)

                            val startDate = when (range) {
                                DateRange.All -> "1970-01-01"
                                DateRange.Today -> today
                                DateRange.Yesterday -> yesterday
                                DateRange.LastSevenDays -> last7Days
                                DateRange.LastThirtyDays -> last30Days
                                DateRange.ThisMonth -> thisMonth
                                DateRange.LastMonth -> lastMonth
                                DateRange.CustomRange -> today // This won't be used
                            }
                            val endDate = when (range) {
                                DateRange.All -> today
                                DateRange.Today -> today
                                DateRange.Yesterday -> yesterday
                                DateRange.LastSevenDays -> today
                                DateRange.LastThirtyDays -> today
                                DateRange.ThisMonth -> today
                                DateRange.LastMonth -> lastDayOfLastMonth
                                DateRange.CustomRange -> today // This won't be used
                            }
                            onRangeSelected(startDate, endDate, range)
                            expanded = false
                        }
                    }
                )
            }
        }
    }
} 