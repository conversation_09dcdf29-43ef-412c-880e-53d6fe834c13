package com.thedasagroup.suminative.ui.guava_orders

import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.appcompat.app.AppCompatActivity
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.viewModel
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class GuavaOrdersActivity : AppCompatActivity(), MavericksView {
    val viewModel : GuavaOrdersViewModel by viewModel()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            SumiNativeTheme {
                GuavaOrdersScreen(
                    onBackPressed = { finish() },
                    viewModel = viewModel,
                    activity = this
                )
            }
        }
    }

    override fun invalidate() {

    }
} 