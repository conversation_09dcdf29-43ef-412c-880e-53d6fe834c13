package com.thedasagroup.suminative.ui.products

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.instacart.truetime.time.TrueTimeImpl
import com.thedasagroup.suminative.data.database.LocalOrder
import com.thedasagroup.suminative.data.database.LocalOrderItem
import com.thedasagroup.suminative.data.database.LocalOrderRepository
import com.thedasagroup.suminative.data.model.request.my_guava.orders.CreateOrderRequest
import com.thedasagroup.suminative.data.model.request.order.Conversation
import com.thedasagroup.suminative.data.model.request.order.Customer
import com.thedasagroup.suminative.data.model.request.order.DeliveryAddress
import com.thedasagroup.suminative.data.model.request.order.FeedbackComplain
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.OrderRequest
import com.thedasagroup.suminative.data.model.request.order.OrderStatus
import com.thedasagroup.suminative.data.model.request.order.PandaOrderDetail
import com.thedasagroup.suminative.data.model.request.order.PaymentData
import com.thedasagroup.suminative.data.model.request.order.PromoCodes
import com.thedasagroup.suminative.data.model.request.order.SupportDetail
import com.thedasagroup.suminative.data.model.response.order.OrderResponse
import com.thedasagroup.suminative.data.model.response.order.OrderResponse2
import com.thedasagroup.suminative.data.model.response.store_orders.Order2
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.MyGuavaRepository
import com.thedasagroup.suminative.data.repo.StockRepository
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_BACK_END
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_BACK_END2
import com.thedasagroup.suminative.ui.utils.formatDate
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.withContext
import java.util.UUID

open class OrderUseCase(
    private val stockRepository: StockRepository,
    private val guavaRepository: MyGuavaRepository,
    private val localOrderRepository: LocalOrderRepository,
    private val prefs: Prefs,
    private val trueTimeImpl: TrueTimeImpl
) {
    suspend operator fun invoke(order: Order): StateFlow<Async<OrderResponse2>> {
        val total = order.carts?.sumOf { it.netPayable ?: 0.0 } ?: 0.0
        val totalOptionsPrice = order.carts?.sumOf {
            val storeItem = it.storeItem
            val optionSets = storeItem?.optionSets
            val optionSetsSum = optionSets?.sumOf {optioSet ->
                val optionItems = optioSet.options
                optionItems.sumOf { optionItem ->
                    if(optionItem?.optionchecked == true) {
                        (optionItem.price ?: 0.0) * (optionItem.quantity?.toDouble() ?: 0.0)
                    }
                    else 0.0
                }
            }
            optionSetsSum ?: 0.0
        } ?: 0.0
        
        val orderId = UUID.randomUUID().toString()
        val now = trueTimeImpl.now()
        val formattedDate = now.formatDate(DATE_FORMAT_BACK_END)
        
        val updatedOrder = order.copy(
            id = -1,
            businessId = prefs.store?.businessId,
            storeId = prefs.store?.id,
            customerId = -1,
            totalPrice = total.toDouble(),  
            totalExtraPrice = 0.0,
            totalDiscount = 0.0,
            netPayable = order.net(),
            deliveryCharges = 0.0,
            deliveryAddress = DeliveryAddress(),
            status = 1,
            createdBy = prefs.selectedWaiter?.id ?: -1,
            deliveryType = 4,
            paymentType = order.paymentType,
            createdOn = formattedDate,
            orderStatusHistory = mutableListOf(),
            tax = order.tax ?: 0.0,
            modifiedBy = -1,
            modifiedOn = formattedDate,
            deliveryNote = "",
            customer = Customer(),
            transactionId = orderId,
            paymentId = 1122,
            trackingUrl = "",
            pickupTime = "",
            pandaOrderId = "",
            pandaOrderDetail = PandaOrderDetail(),
            lastUpdatedFromPanda = "",
            totalOptionPrice = totalOptionsPrice,
            promoCode = "",
            discountONPromo = 0.0,
            isPromoCodeAvailed = false,
            priceBeforePromo = 0,
            priceAfterPromo = 0,
            scheduled = false,
            scheduledDateTime = "",
            guest = false
        )

        val orderItems = mutableListOf<LocalOrderItem>()
        // Save to local database BEFORE sending to server
        try {
            val localOrder = convertToLocalOrder(updatedOrder, orderId, now.time)
            localOrderRepository.insertOrder(localOrder)
            
            // Save order items to local database
            updatedOrder.carts?.forEachIndexed { index, cart ->
                val localOrderItem = LocalOrderItem(
                    orderId = orderId,
                    itemId = cart.storeItem?.id?.toLong() ?: index.toLong(),
                    itemName = cart.storeItem?.name ?: "Unknown Item",
                    itemDescription = cart.storeItem?.description,
                    quantity = cart.quantity?.toLong() ?: 1L,
                    unitPrice = cart.storeItem?.price ?: 0.0,
                    totalPrice = cart.netPayable ?: 0.0,
                    category = "",
                    modifiers = buildModifiersJson(cart.storeItem?.optionSets),
                    notes = order.orderNotes,
                    createdAt = now.time
                )
                orderItems.add(localOrderItem)
                localOrderRepository.insertOrderItem(localOrderItem)
            }
        } catch (e: Exception) {
            // Log error but continue with API call
            // In production, you might want to handle this differently
            e.printStackTrace()
        }

        // val orderResponse = stockRepository.placeOrder(
        //     request = updatedOrder,
        // )

        val orderResponse = MutableStateFlow<Async<OrderResponse2>>(Loading())
        orderResponse.value = Success(
            convertLocalOrderToOrderResponse(
                localOrder = convertToLocalOrder(updatedOrder, orderId, now.time),
                localOrderItems = orderItems
            )
        )

        // TODO: For complete sync workflow, inject PostOrderSyncUseCase and use:
        // postOrderSyncUseCase.handleOrderPlacementResult(orderId, orderResponse)
        //
        // Or mark as synced manually if API call succeeds:
        // if (orderResponse.value is Success) {
        //     val localOrder = localOrderRepository.getOrderByOrderId(orderId)
        //     if (localOrder != null) {
        //         localOrderRepository.markOrderSynced(localOrder.id)
        //     }
        // }

        return orderResponse
    }

    private fun convertToLocalOrder(order: Order, orderId: String, timestamp: Long): LocalOrder {
        return LocalOrder(
            orderId = orderId,
            customerId = order.customerId?.toLong(),
            customerName = order.customer?.name ?: "Guest",
            customerPhone = order.customer?.phone,
            orderType = when (order.deliveryType) {
                4 -> "DINE_IN"  // Assuming 4 is dine-in
                1 -> "DELIVERY"
                2 -> "TAKEAWAY"
                else -> "DINE_IN"
            },
            status = when (order.status) {
                1 -> "PENDING"
                2 -> "ACCEPTED"
                3 -> "PREPARING"
                4 -> "READY"
                5 -> "COMPLETED"
                6 -> "CANCELLED"
                else -> "PENDING"
            },
            subtotal = (order.totalPrice ?: 0.0) - (order.tax ?: 0.0),
            tax = order.tax ?: 0.0,
            discount = order.totalDiscount ?: 0.0,
            total = order.net() ?: 0.0,
            paymentMethod = when (order.paymentType) {
                1 -> "CASH"
                2 -> "CARD"
                3 -> "DIGITAL"
                else -> "CASH"
            },
            paymentStatus = "PENDING", // New orders start as pending payment
            notes = order.deliveryNote,
            tableNumber = null, // You might want to add table info to the Order model
            deliveryAddress = order.deliveryAddress?.let { "${it.address}, ${it.city}" },
            createdAt = timestamp,
            updatedAt = timestamp,
            storeId = order.storeId?.toLong() ?: prefs.store?.id?.toLong() ?: 1L,
            waiterId = order.createdBy?.toLong(),
            waiterName = prefs.selectedWaiter?.name,
            synced = false // IMPORTANT: Always false for new orders
        )
    }
    
    private fun buildModifiersJson(optionSets: List<Any>?): String? {
        // Build a JSON string from option sets for storage
        // This is a simplified implementation - you might want to use proper JSON serialization
        if (optionSets.isNullOrEmpty()) return null
        
        return try {
            // TODO: Implement proper JSON serialization for option sets
            // For now, returning a simple string representation
            "modifiers_data"
        } catch (e: Exception) {
            null
        }
    }

    /**
     * Converts a LocalOrder from the local database to an OrderResponse
     * This is useful for displaying local orders in the same format as server responses
     */
    fun convertLocalOrderToOrderResponse(
        localOrder: LocalOrder,
        localOrderItems: List<LocalOrderItem> = emptyList()
    ): OrderResponse2 {
        val order = convertLocalOrderToStoreOrder(localOrder, localOrderItems)
        
        return OrderResponse2(
            command = null,
            customerId = localOrder.customerId?.toInt() ?: 0,
            message = "Local order retrieved successfully",
            order = order,
            paymentId = 0, // Local orders don't have payment IDs initially
            paymentResponseJson = null,
            success = true
        )
    }

    /**
     * Converts a LocalOrder to the Order model used in responses
     */
    private fun convertLocalOrderToStoreOrder(
        localOrder: LocalOrder,
        localOrderItems: List<LocalOrderItem> = emptyList()
    ): Order2 {
        val formattedDate = java.text.SimpleDateFormat(DATE_FORMAT_BACK_END2, java.util.Locale.getDefault())
            .format(java.util.Date(localOrder.createdAt))
        
        // Convert cart items to JSON string if needed
        val cartJson = if (localOrderItems.isNotEmpty()) {
            buildCartJsonFromOrderItems(localOrderItems)
        } else null

        return Order2(
            businessId = prefs.store?.businessId ?: 0,
            cartJson = cartJson,
            createdBy = localOrder.waiterId?.toInt() ?: 0,
            createdOn = formattedDate,
            customerId = localOrder.customerId?.toInt() ?: 0,
            deliveryCharges = 0.0,
            deliveryNote = localOrder.notes,
            deliveryType = convertOrderTypeToDeliveryType(localOrder.orderType),
            discountOnPromo = localOrder.discount,
            id = 0, // Local orders don't have server IDs initially
            isGuest = localOrder.customerId == null,
            isScheduled = false,
            json = null,
            netPayable = localOrder.total,
            paymentType = convertPaymentMethodToPaymentType(localOrder.paymentMethod),
            pickupTime = null,
            promoId = 0,
            scheduleInt = 0,
            scheduledDateTime = null,
            status = convertStatusStringToInt(localOrder.status),
            storeId = localOrder.storeId.toInt(),
            tax = localOrder.tax,
            totalDiscount = localOrder.discount,
            totalExtraPrice = 0.0,
            totalOptionPrice = 0.0,
            totalPrice = localOrder.subtotal + localOrder.tax,
            trackingUrl = null,
            acceptedDate = if (localOrder.status != "PENDING") formattedDate else null,
            countMinutes = 0
        )
    }

    /**
     * Converts order type string to delivery type integer
     */
    private fun convertOrderTypeToDeliveryType(orderType: String): Int {
        return when (orderType.uppercase()) {
            "DINE_IN" -> 4
            "DELIVERY" -> 1
            "TAKEAWAY" -> 2
            else -> 4 // Default to dine-in
        }
    }

    /**
     * Converts payment method string to payment type integer
     */
    private fun convertPaymentMethodToPaymentType(paymentMethod: String?): Int {
        return when (paymentMethod?.uppercase()) {
            "CASH" -> 1
            "CARD" -> 2
            "DIGITAL" -> 3
            else -> 1 // Default to cash
        }
    }

    /**
     * Converts status string to status integer
     */
    private fun convertStatusStringToInt(status: String): Int {
        return when (status.uppercase()) {
            "PENDING" -> 1
            "ACCEPTED" -> 2
            "PREPARING" -> 3
            "READY" -> 4
            "COMPLETED" -> 5
            "CANCELLED" -> 6
            else -> 1 // Default to pending
        }
    }

    /**
     * Builds a cart JSON string from order items
     * This is a simplified implementation - you may need to enhance based on your Cart model
     */
    private fun buildCartJsonFromOrderItems(orderItems: List<LocalOrderItem>): String? {
        return try {
            // TODO: Implement proper JSON serialization for cart items
            // This is a placeholder implementation
            // You'll need to create proper Cart objects and serialize them
            val cartItems = orderItems.map { item ->
                mapOf(
                    "itemId" to item.itemId,
                    "itemName" to item.itemName,
                    "quantity" to item.quantity,
                    "unitPrice" to item.unitPrice,
                    "totalPrice" to item.totalPrice,
                    "modifiers" to item.modifiers
                )
            }
            
            // Use your preferred JSON serialization method here
            // For now, returning a simple string representation
            kotlinx.serialization.json.Json.encodeToString(cartItems)
        } catch (e: Exception) {
            null
        }
    }
}