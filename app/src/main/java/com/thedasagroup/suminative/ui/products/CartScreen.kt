package com.thedasagroup.suminative.ui.products

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Card
import androidx.compose.material.DrawerDefaults
import androidx.compose.material.DrawerState
import androidx.compose.material.DrawerValue
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.MaterialTheme
import androidx.compose.material.ModalDrawer
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material.contentColorFor
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AddCard
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Money
import androidx.compose.material.rememberDrawerState
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.R
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.StoreItem
import com.thedasagroup.suminative.data.model.response.stock.StockItem
import com.thedasagroup.suminative.ui.print.MyTextDivider
import com.thedasagroup.suminative.ui.print.Total
import com.thedasagroup.suminative.ui.print.TotalCart
import com.thedasagroup.suminative.ui.theme.fontNunito
import com.thedasagroup.suminative.ui.theme.fontPoppins
import com.thedasagroup.suminative.ui.utils.transformDecimal
import ir.kaaveh.sdpcompose.sdp

@Composable
fun RightModalDrawer(
    content: @Composable () -> Unit,
) {
    content()/*CompositionLocalProvider(LocalLayoutDirection provides LayoutDirection.Rtl) {
        ModalDrawer(
            modifier = modifier,
            drawerState = drawerState,
            gesturesEnabled = gesturesEnabled,
            drawerShape = drawerShape,
            drawerElevation = drawerElevation,
            drawerBackgroundColor = drawerBackgroundColor,
            drawerContentColor = drawerContentColor,
            scrimColor = scrimColor,
            drawerContent = {
                CompositionLocalProvider(LocalLayoutDirection provides LayoutDirection.Ltr) {
                    // under the hood, drawerContent is wrapped in a Column, but it would be under the Rtl layout
                    // so we create new column filling max width under the Ltr layout
                    Column(
                        modifier = Modifier.fillMaxWidth(), content = drawerContent
                    )
                }
            },
            content = {
                CompositionLocalProvider(LocalLayoutDirection provides LayoutDirection.Ltr) {

                }
            },
        )

    }*/
}

/*
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CartScreen(
    order: Order,
    onRemoveItem: (Cart) -> Unit,
    onClose: () -> Unit,
    placeOrderCash: () -> Unit,
    placeOrderCard: () -> Unit,
    onUpdateStock: (Int, StoreItem) -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {

    val ordersResponse by productsScreenViewModel.collectAsState(ProductsScreenState::orderResponse)

    Column(
        modifier = Modifier.padding(top = 30.sdp, bottom = 20.sdp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        TopAppBar(modifier = Modifier.fillMaxWidth(), title = {
            Text(text = "Cart", style = MaterialTheme.typography.h3)
        })
        LazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(1.0f),
            contentPadding = PaddingValues(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            items(order.carts ?: mutableListOf()) { cartItem ->
                CartItemCard(
                    cartItem = cartItem, onRemoveItem = onRemoveItem, onUpdateStock = onUpdateStock
                )
            }

            item {
                if (order.carts?.isNotEmpty() == true) {
                    Text(
                        modifier = Modifier.padding(end = 10.sdp, start = 5.sdp),
                        text = "Bill Details: ",
                        style = MaterialTheme.typography.h4
                    )
                }

                if (ordersResponse is Loading) {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        CircularProgressIndicator()
                    }
                } else {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {

                        if (order.carts?.isNotEmpty() == true) {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(end = 16.dp, start = 16.dp)
                            ) {
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "Subtotal:", style = MaterialTheme.typography.h5
                                    )
                                    Text(
                                        text = "£ ${order.netPayable?.transformDecimal()}",
                                        style = MaterialTheme.typography.h5
                                    )
                                }
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "Taxes: ", style = MaterialTheme.typography.h5
                                    )
                                    Text(
                                        text = "£ ${order.tax?.transformDecimal()}",
                                        style = MaterialTheme.typography.h5
                                    )
                                }
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text(
                                        text = "Total Payable: ",
                                        style = MaterialTheme.typography.h5
                                    )
                                    Text(
                                        text = "£ ${order.totalPrice?.transformDecimal()}",
                                        style = MaterialTheme.typography.h5
                                    )
                                }
                            }
                        }

                        Row(
                            modifier = Modifier.fillMaxWidth(1f),
                            verticalAlignment = Alignment.CenterVertically
                        ) {

                            Button(
                                colors = ButtonDefaults.buttonColors(
                                    contentColor = Color.White, containerColor = Color.Green
                                ), onClick = {
                                    placeOrderCard()
                                }, shape = MaterialTheme.shapes.medium
                            ) {
                                Row(modifier = Modifier.padding(8.dp)) {
                                    Image(
                                        modifier = Modifier.size(10.sdp),
                                        imageVector = Icons.Default.AddCard,
                                        contentDescription = "Button"
                                    )
                                    Spacer(modifier = Modifier.width(8.sdp))
                                    Text(
                                        "Card", style = MaterialTheme.typography.h4
                                    )
                                }
                            }

                            Spacer(modifier = Modifier.width(10.sdp))

                            Button(
                                colors = ButtonDefaults.buttonColors(
                                    contentColor = Color.White, containerColor = Color.Red
                                ), onClick = {
                                    placeOrderCash()
                                }, shape = MaterialTheme.shapes.medium
                            ) {
                                Row(modifier = Modifier.padding(8.dp)) {
                                    Image(
                                        modifier = Modifier.size(10.sdp),
                                        imageVector = Icons.Default.Money,
                                        contentDescription = "Button"
                                    )
                                    Spacer(modifier = Modifier.width(8.sdp))
                                    Text(
                                        "Cash", style = MaterialTheme.typography.h4
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }


    }
}*/
