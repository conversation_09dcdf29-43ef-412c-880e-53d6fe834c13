package com.thedasagroup.suminative.ui.rewards

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.text.input.ImeAction
import com.airbnb.mvrx.compose.mavericksActivityViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

// Theme colors
private val ThemeGreen = Color(0xFF2E7D32)

@AndroidEntryPoint
class RewardsTestActivity : ComponentActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        setContent {
            MaterialTheme {
                RewardsTestScreen(
                    onAddToCart = { rewardItem ->
                        // Demo function - in real app this would add to actual cart
                        println("Added reward item to cart: ${rewardItem.storeItem?.name} - FREE")
                    }
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RewardsTestScreen(
    onAddToCart: ((rewardItem: com.thedasagroup.suminative.data.model.response.rewards.RewardItem) -> Unit)? = null
) {
    val viewModel: RewardsViewModel = mavericksActivityViewModel()
    var userId by remember { mutableStateOf("942") }
    var businessId by remember { mutableStateOf("92") }
    var showUserPoints by remember { mutableStateOf(false) }
    var showRewardsOverview by remember { mutableStateOf(false) }
    var showRewardsScreen by remember { mutableStateOf(false) }
    val coroutineScope = rememberCoroutineScope()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Title
        Text(
            text = "Rewards API Test",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black
        )
        
        // User ID Input
        OutlinedTextField(
            value = userId,
            onValueChange = { userId = it },
            label = { Text("User ID") },
            modifier = Modifier.fillMaxWidth(),
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Number,
                imeAction = ImeAction.Next
            ),
            singleLine = true
        )
        
        // Business ID Input
        OutlinedTextField(
            value = businessId,
            onValueChange = { businessId = it },
            label = { Text("Business ID") },
            modifier = Modifier.fillMaxWidth(),
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Number,
                imeAction = ImeAction.Done
            ),
            singleLine = true
        )
        
        // Test User Points Button
        Button(
            onClick = {
                val userIdInt = userId.toIntOrNull()
                if (userIdInt != null) {
                    showUserPoints = true
                    showRewardsOverview = false
                }
            },
            modifier = Modifier.fillMaxWidth(),
            colors = ButtonDefaults.buttonColors(containerColor = ThemeGreen),
            shape = RoundedCornerShape(8.dp)
        ) {
            Text(
                text = "Test Get User Points API",
                color = Color.White,
                fontWeight = FontWeight.Medium
            )
        }
        
        // Test Rewards Overview Button
        Button(
            onClick = {
                val userIdInt = userId.toIntOrNull()
                if (userIdInt != null) {
                    showRewardsOverview = true
                    showUserPoints = false
                    showRewardsScreen = false
                }
            },
            modifier = Modifier.fillMaxWidth(),
            colors = ButtonDefaults.buttonColors(containerColor = ThemeGreen),
            shape = RoundedCornerShape(8.dp)
        ) {
            Text(
                text = "Test Get Rewards Overview API",
                color = Color.White,
                fontWeight = FontWeight.Medium
            )
        }

        // Test Full Rewards Screen Button
        Button(
            onClick = {
                showRewardsScreen = true
                showUserPoints = false
                showRewardsOverview = false
            },
            modifier = Modifier.fillMaxWidth(),
            colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF1976D2)),
            shape = RoundedCornerShape(8.dp)
        ) {
            Text(
                text = "Test Full Rewards Screen",
                color = Color.White,
                fontWeight = FontWeight.Medium
            )
        }
        
        // API Information
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(8.dp),
            colors = CardDefaults.cardColors(containerColor = Color.Gray.copy(alpha = 0.1f))
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "API Endpoints:",
                    fontWeight = FontWeight.Bold,
                    fontSize = 16.sp
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "1. GET /api/rewards/points?userId={userId}&businessId={businessId}",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
                
                Text(
                    text = "   Returns: Double (user points)",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "2. GET /api/rewards/overview?userId={userId}&businessId={businessId}",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
                
                Text(
                    text = "   Returns: RewardsOverviewResponse (points + reward items)",
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            }
        }
    }
    
    // Show User Points Screen
    if (showUserPoints) {
        val userIdInt = userId.toIntOrNull() ?: 942
        UserPointsScreen(
            viewModel = viewModel,
            userId = userIdInt,
            businessId = businessId.toInt(),
            onBackClick = { showUserPoints = false }
        )
    }
    
    // Show Rewards Overview Screen
    if (showRewardsOverview) {
        val userIdInt = userId.toIntOrNull() ?: 942
        RewardsOverviewScreen(
            viewModel = viewModel,
            businessId = businessId.toInt(),
            userId = userIdInt,
            onBackClick = { showRewardsOverview = false }
        )
    }

    // Show Full Rewards Screen
    if (showRewardsScreen) {

    }
}
