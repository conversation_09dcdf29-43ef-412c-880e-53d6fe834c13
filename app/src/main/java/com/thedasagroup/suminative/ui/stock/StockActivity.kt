package com.thedasagroup.suminative.ui.stock

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.ui.Modifier
import androidx.lifecycle.lifecycleScope
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.viewModel
import com.thedasagroup.suminative.ui.login.LoginScreenViewModel
import kotlinx.coroutines.launch

class StockActivity : ComponentActivity(), MavericksView {
    val viewModel: StockScreenViewModel by viewModel()
    val loginViewModel: LoginScreenViewModel by viewModel()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            StockScreen(viewModel = viewModel, onBackClick = {
                finish()
            }, modifier = Modifier.fillMaxSize())
        }
    }

    override fun onResume() {
        super.onResume()
        lifecycleScope.launch {
            viewModel.getStockItems()
        }
    }

    override fun invalidate() {

    }
}