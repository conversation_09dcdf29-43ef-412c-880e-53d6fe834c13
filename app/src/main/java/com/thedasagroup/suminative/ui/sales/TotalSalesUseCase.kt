package com.thedasagroup.suminative.ui.sales

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.request.sales.SalesRequest
import com.thedasagroup.suminative.data.model.response.sales.SalesResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.SalesRepository
import kotlinx.coroutines.flow.StateFlow

class TotalSalesUseCase(private val salesRepository: SalesRepository, private val prefs : Prefs) {
    suspend operator fun invoke(request: SalesRequest): StateFlow<Async<SalesResponse>> {
        return salesRepository.getTotalSales(
            request = request
        )
    }
}