package com.thedasagroup.suminative.ui.rewards

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.filled.ShoppingCart
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import coil.compose.AsyncImage
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.data.model.request.order.StoreItem
import com.thedasagroup.suminative.data.model.response.rewards.RewardItem
import com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer
import com.thedasagroup.suminative.data.model.response.rewards.RewardsOverviewResponse
import kotlinx.coroutines.launch

// Theme colors
private val ThemeGreen = Color(0xFF2E7D32)
private val LightGray = Color(0xFFF5F5F5)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RewardsAvailScreen(
    viewModel: RewardsViewModel,
    customer: RewardsCustomer,
    onBackClick: () -> Unit,
    onAddToCart: (rewardItem: RewardItem) -> Unit,
    modifier: Modifier = Modifier
) {
    val state by viewModel.collectAsState()
    val coroutineScope = rememberCoroutineScope()
    
    // Load rewards overview when screen opens
    LaunchedEffect(customer.id) {
        customer.id?.let { userId ->
            println("RewardsAvailScreen: Loading rewards overview for userId: $userId")
            viewModel.getRewardsOverview(userId = userId, businessId = customer.businessId ?: 0)
        }
    }

    Dialog(properties = DialogProperties(usePlatformDefaultWidth = false),onDismissRequest = {
        onBackClick()
    }) {

        Column(
            modifier = modifier
                .fillMaxSize()
                .background(Color.White)
        ) {
            // Top App Bar
            TopAppBar(
                title = {
                    Text(
                        text = "Available Rewards",
                        fontWeight = FontWeight.Bold,
                        fontSize = 20.sp
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onBackClick) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "Back"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = ThemeGreen,
                    titleContentColor = Color.White,
                    navigationIconContentColor = Color.White
                )
            )

            // Content
            when (val response = state.rewardsOverviewResponse) {
                is Loading -> {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            CircularProgressIndicator(color = ThemeGreen)
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "Loading rewards...",
                                color = Color.Gray,
                                fontSize = 14.sp
                            )
                        }
                    }
                }

                is Success -> {
                    val overview = response.invoke()
                    if (overview != null) {
                        RewardsAvailContent(
                            overview = overview,
                            customer = customer,
                            onAddToCart = onAddToCart,
                            modifier = Modifier.fillMaxSize()
                        )
                    } else {
                        ErrorMessage("No rewards data available")
                    }
                }

                is com.airbnb.mvrx.Fail -> {
                    ErrorMessage("Error loading rewards: ${response.error.message}")
                }

                else -> {
                    // Show loading initially when state is Uninitialized
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            CircularProgressIndicator(color = ThemeGreen)
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(
                                text = "Initializing rewards...",
                                color = Color.Gray,
                                fontSize = 14.sp
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun RewardsAvailContent(
    overview: RewardsOverviewResponse,
    customer: RewardsCustomer,
    onAddToCart: ((rewardItem: RewardItem) -> Unit),
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier.padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // User Points Header
        item {
            UserPointsHeader(
                customerName = customer.name ?: "Customer",
                points = overview.userPoints ?: 0.0
            )
        }
        
        // Available Rewards Header
        item {
            Text(
                text = "Available Rewards",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black,
                modifier = Modifier.padding(vertical = 8.dp)
            )
        }
        
        // Reward Items
        val rewardItems = overview.rewardItems ?: emptyList()
        if (rewardItems.isEmpty()) {
            item {
                EmptyRewardsMessage()
            }
        } else {
            items(rewardItems) { rewardItem ->
                RewardAvailItemCard(
                    rewardItem = rewardItem,
                    userPoints = overview.userPoints ?: 0.0,
                    onAddToCart = onAddToCart
                )
            }
        }
    }
}

@Composable
private fun UserPointsHeader(
    customerName: String,
    points: Double,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = ThemeGreen),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Star,
                contentDescription = "Points",
                tint = Color.White,
                modifier = Modifier.size(32.dp)
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = customerName,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
                Text(
                    text = "Available Points",
                    fontSize = 14.sp,
                    color = Color.White.copy(alpha = 0.9f)
                )
            }
            
            Text(
                text = points.toInt().toString(),
                fontSize = 28.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White
            )
        }
    }
}

@Composable
private fun RewardAvailItemCard(
    rewardItem: RewardItem,
    userPoints: Double,
    onAddToCart: ((rewardItem: RewardItem) -> Unit)?,
    modifier: Modifier = Modifier
) {
    val canAfford = userPoints >= (rewardItem.stampsRequired ?: 0)
    
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = if (canAfford) Color.White else Color.Gray.copy(alpha = 0.1f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Product Image
            AsyncImage(
                model = rewardItem.storeItem?.pic,
                contentDescription = rewardItem.storeItem?.name,
                modifier = Modifier
                    .size(80.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(LightGray),
                contentScale = ContentScale.Crop
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // Product Details
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = rewardItem.storeItem?.name ?: "Unknown Item",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = if (canAfford) Color.Black else Color.Gray
                )
                
                if (!rewardItem.storeItem?.description.isNullOrBlank()) {
                    Text(
                        text = rewardItem.storeItem?.description ?: "",
                        fontSize = 14.sp,
                        color = if (canAfford) Color.Gray else Color.Gray.copy(alpha = 0.7f),
                        maxLines = 2,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Points Required
                Surface(
                    shape = RoundedCornerShape(16.dp),
                    color = if (canAfford) ThemeGreen.copy(alpha = 0.1f) else Color.Gray.copy(alpha = 0.1f)
                ) {
                    Text(
                        text = "${rewardItem.stampsRequired} points required",
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = if (canAfford) ThemeGreen else Color.Gray,
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                    )
                }
            }
            
            // Add to Cart Button
            if (canAfford && onAddToCart != null) {
                Button(
                    onClick = { onAddToCart(rewardItem) },
                    colors = ButtonDefaults.buttonColors(containerColor = ThemeGreen),
                    shape = RoundedCornerShape(8.dp),
                    modifier = Modifier.size(width = 100.dp, height = 40.dp),
                    contentPadding = PaddingValues(4.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.ShoppingCart,
                        contentDescription = "Add to Cart",
                        tint = Color.White,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "Redeem",
                        color = Color.White,
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
            } else if (!canAfford) {
                Surface(
                    shape = RoundedCornerShape(8.dp),
                    color = Color.Gray.copy(alpha = 0.3f),
                    modifier = Modifier.size(width = 100.dp, height = 40.dp)
                ) {
                    Box(
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "Not enough\npoints",
                            color = Color.Gray,
                            fontSize = 10.sp,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun EmptyRewardsMessage(
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(32.dp),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = "No rewards available",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Gray
            )
            Text(
                text = "Check back later for new rewards!",
                fontSize = 14.sp,
                color = Color.Gray.copy(alpha = 0.7f),
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(top = 8.dp)
            )
        }
    }
}

@Composable
private fun ErrorMessage(
    message: String,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = message,
                fontSize = 16.sp,
                color = Color.Gray,
                textAlign = TextAlign.Center
            )
        }
    }
}

/**
 * Converts a RewardItem to a StoreItem for adding to cart with zero price
 */
fun RewardItem.toStoreItem(): StoreItem {
    val rewardStoreItem = this.storeItem
    return StoreItem(
        id = rewardStoreItem?.id,
        name = rewardStoreItem?.name,
        description = rewardStoreItem?.description,
        pic = rewardStoreItem?.pic,
        price = 0.0, // Set price to zero for rewards
        billAmount = 0.0, // Set bill amount to zero for rewards
        tax = 0.0, // Set tax to zero for rewards
        discountedAmount = 0.0,
        businessId = rewardStoreItem?.businessId,
        storeId = rewardStoreItem?.storeId,
        brandId = rewardStoreItem?.brandId,
        categoryId = rewardStoreItem?.categoryId,
        unitId = rewardStoreItem?.unitId ?: -1,
        discountType = rewardStoreItem?.discountType ?: 0,
        ingredients = rewardStoreItem?.ingredients,
        preparationTime = rewardStoreItem?.preparationTime,
        additionalInfo = rewardStoreItem?.additionalInfo,
        dailyCapacity = rewardStoreItem?.dailyCapacity ?: 0,
        createdBy = rewardStoreItem?.createdBy,
        createdOn = rewardStoreItem?.createdOn,
        modifiedBy = rewardStoreItem?.modifiedBy,
        modifiedOn = rewardStoreItem?.modifiedOn,
        servingSize = rewardStoreItem?.servingSize,
        quantity = 1, // Default quantity
        vat = rewardStoreItem?.vat ?: false,
        discounttypename = "Reward",
        unitName = "Unit",
        optionSets = emptyList(),
        extras = emptyList()
    )
}
