package com.thedasagroup.suminative.ui.common.customComposableViews

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.Checkbox
import androidx.compose.material3.DatePicker
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.rememberDatePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_DATE_ONLY
import com.thedasagroup.suminative.ui.utils.formatDate
import java.util.Calendar

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CustomDateRangePicker(
    onDateRangeSelected: (String, String) -> Unit,
    onDismiss: () -> Unit,
    viewModel: ProductsScreenViewModel
) {
    var startDate by remember { mutableStateOf<Long?>(null) }
    var endDate by remember { mutableStateOf<Long?>(null) }
    var selectingStartDate by remember { mutableStateOf(true) }
    var singleDaySelection by remember { mutableStateOf(false) }
    
    val today = viewModel.trueTimeImpl.now().time
    
    // Set up date picker states - don't pre-select any date
    val startDatePickerState = rememberDatePickerState(initialSelectedDateMillis = null)
    val endDatePickerState = rememberDatePickerState(initialSelectedDateMillis = null)
    
    // Monitor for date selection changes
    LaunchedEffect(startDatePickerState.selectedDateMillis) {
        if (selectingStartDate && startDatePickerState.selectedDateMillis != null) {
            startDate = startDatePickerState.selectedDateMillis
            
            // If single day selection is enabled, automatically set end date to same as start date
            if (singleDaySelection) {
                endDate = startDate
            } else {
                // Otherwise automatically switch to end date selection
                selectingStartDate = false
            }
        }
    }
    
    LaunchedEffect(endDatePickerState.selectedDateMillis) {
        if (!selectingStartDate && endDatePickerState.selectedDateMillis != null) {
            endDate = endDatePickerState.selectedDateMillis
        }
    }
    
    // Update UI when switching between single day and range modes
    LaunchedEffect(singleDaySelection) {
        if (singleDaySelection) {
            // When switching to single day mode, ensure start date is selected
            // and update the start date picker state
            if (startDate != null) {
//                startDatePickerState.setSelection(startDate)
            } else if (endDate != null) {
                // If only end date was selected, use it as the single day
                startDate = endDate
//                startDatePickerState.setSelection(endDate)
            }
            
            // In single day mode, set end date equal to start date
            endDate = startDate
            
            // Always focus on start date in single day mode
            selectingStartDate = true
        } else {
            // When switching to range mode, preserve existing selections
            if (startDate != null && endDate != null) {
                // Ensure dates are properly ordered
                val finalStartDate = minOf(startDate!!, endDate!!)
                val finalEndDate = maxOf(startDate!!, endDate!!)
                
                startDate = finalStartDate
                endDate = finalEndDate
                
//                startDatePickerState.setSelection(finalStartDate)
//                endDatePickerState.setSelection(finalEndDate)
            }
        }
    }
    
    // Function to apply the selected date range
    fun applyDateRange() {
        if (startDate != null) {
            // For single day selection, set both dates to the same value
            if (singleDaySelection || endDate == null) {
                endDate = startDate
            }
            
            // Ensure start date is always before or equal to end date
            val finalStartDate = minOf(startDate!!, endDate!!)
            val finalEndDate = maxOf(startDate!!, endDate!!)
            
            val startDateFormatted = Calendar.getInstance().apply { 
                timeInMillis = finalStartDate 
            }.time.formatDate(DATE_FORMAT_DATE_ONLY)
            
            val endDateFormatted = Calendar.getInstance().apply { 
                timeInMillis = finalEndDate 
            }.time.formatDate(DATE_FORMAT_DATE_ONLY)
            
            onDateRangeSelected(startDateFormatted, endDateFormatted)
            onDismiss()
        }
    }
    
    Dialog(onDismissRequest = onDismiss) {
        Card(
            shape = RoundedCornerShape(16.dp),
            modifier = Modifier.padding(16.dp)
        ) {
            Column(
                modifier = Modifier
                    .padding(16.dp)
                    .fillMaxWidth()
            ) {
                Text(
                    text = "Select Date Range",
                    style = MaterialTheme.typography.headlineSmall,
                    modifier = Modifier.padding(bottom = 8.dp)
                )
                
                // Single day selection toggle
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 8.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "Single day selection",
                        modifier = Modifier.clickable {
                            singleDaySelection = !singleDaySelection
                        }
                    )
                    
                    Switch(
                        checked = singleDaySelection,
                        onCheckedChange = { checked ->
                            singleDaySelection = checked
                        }
                    )
                }
                
                // Date selection tabs (only show both if not in single day mode)
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Column(
                        modifier = Modifier
                            .weight(1f)
                            .clickable { selectingStartDate = true }
                            .padding(8.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = if (singleDaySelection) "Select Date" else "Start Date",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = startDate?.let { 
                                Calendar.getInstance().apply { 
                                    timeInMillis = it 
                                }.time.formatDate(DATE_FORMAT_DATE_ONLY)
                            } ?: "Select",
                            fontWeight = FontWeight.Bold,
                            color = if (selectingStartDate) MaterialTheme.colorScheme.primary else Color.Unspecified
                        )
                    }
                    
                    if (!singleDaySelection) {
                        Spacer(
                            modifier = Modifier
                                .width(1.dp)
                                .height(40.dp)
                                .background(Color.LightGray)
                        )
                        
                        Column(
                            modifier = Modifier
                                .weight(1f)
                                .clickable { 
                                    // Only allow end date selection if start date is selected
                                    if (startDate != null) {
                                        selectingStartDate = false
                                    }
                                }
                                .padding(8.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "End Date",
                                style = MaterialTheme.typography.bodyMedium
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = endDate?.let { 
                                    Calendar.getInstance().apply { 
                                        timeInMillis = it 
                                    }.time.formatDate(DATE_FORMAT_DATE_ONLY)
                                } ?: if (startDate != null) "Select" else "Select start date first",
                                fontWeight = FontWeight.Bold,
                                color = if (!selectingStartDate) MaterialTheme.colorScheme.primary else Color.Unspecified
                            )
                        }
                    }
                }
                
                // Show the appropriate date picker based on what we're selecting
                if (selectingStartDate || singleDaySelection) {
                    DatePicker(
                        state = startDatePickerState,
                        modifier = Modifier.height(400.dp),
                        title = null,
                        headline = null,
                        showModeToggle = false
                    )
                } else {
                    DatePicker(
                        state = endDatePickerState,
                        modifier = Modifier.height(400.dp),
                        title = null,
                        headline = null,
                        showModeToggle = false
                    )
                }
                
                // Action buttons
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 16.dp),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    TextButton(onClick = onDismiss) {
                        Text("Cancel")
                    }
                    
                    // Enable Apply button if we have at least a start date
                    Button(
                        onClick = { applyDateRange() },
                        enabled = startDate != null && (singleDaySelection || endDate != null),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF009551),
                            disabledContainerColor = Color.Gray
                        )
                    ) {
                        Text("Apply", color = Color.White)
                    }
                }
            }
        }
    }
} 