package com.thedasagroup.suminative.ui.reservations

import com.airbnb.mvrx.Success
import com.airbnb.mvrx.mocking.mockSingleViewModel
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.Customer
import com.thedasagroup.suminative.data.model.request.order.DeliveryAddress
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.PandaOrderDetail
import com.thedasagroup.suminative.data.model.request.order.StoreItem
import com.thedasagroup.suminative.data.model.response.reservations.Reservation
import com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse
import com.thedasagroup.suminative.ui.payment.PaymentFragment
import com.thedasagroup.suminative.ui.payment.PaymentState

fun ReservationsFragment.mocks() = mockSingleViewModel(
    ReservationsFragment::viewModel,
    defaultState = ReservationsState(),
    defaultArgs = null
) {
    state(name = "Loading") {
        setLoading { ::activeReservationsResponse }
        setLoading { ::allReservationsResponse }
    }

    state(name = "Active Reservations") {
        copy(
            activeReservationsResponse =
                Success(
                    ReservationsResponse(
                        reservations = listOf(
                            Reservation(
                                tableName = "Main Table",
                                reservationTime = "2025-07-18T11:30",
                                numPeople = 11,
                                customerName = "Test Reservations",
                                customerPhone = "3315685399",
                                reservationStatus = 0 // Reserved
                            ),
                            Reservation(
                                tableName = "Outside Table",
                                reservationTime = "2025-07-18T12:00",
                                numPeople = 4,
                                customerName = "John Doe",
                                customerPhone = "1234567890",
                                reservationStatus = 2 // Arrived
                            ),
                            Reservation(
                                tableName = "Inside Table",
                                reservationTime = "2025-07-18T12:30",
                                numPeople = 2,
                                customerName = "Jane Smith",
                                customerPhone = "0987654321",
                                reservationStatus = 0 // Reserved
                            ),
                            Reservation(
                                tableName = "Bar Table",
                                reservationTime = "2025-07-18T13:00",
                                numPeople = 6,
                                customerName = "Bob Johnson",
                                customerPhone = "1122334455",
                                reservationStatus = 1 // Cancelled
                            )
                        ),
                        success = true,
                        message = null
                    )
                )
        )
    }

    state(name = "All Reservations") {
        copy(
            allReservationsResponse =
                Success(
                    ReservationsResponse(
                        reservations = listOf(
                            Reservation(
                                tableName = "Main Table",
                                reservationTime = "2025-07-18T11:30",
                                numPeople = 11,
                                customerName = "Test Reservations",
                                customerPhone = "3315685399",
                                reservationStatus = 4 // Completed
                            ),
                            Reservation(
                                tableName = "Outside Table",
                                reservationTime = "2025-07-18T12:00",
                                numPeople = 4,
                                customerName = "John Doe",
                                customerPhone = "1234567890",
                                reservationStatus = 1 // Cancelled
                            ),
                            Reservation(
                                tableName = "Inside Table",
                                reservationTime = "2025-07-18T12:30",
                                numPeople = 2,
                                customerName = "Jane Smith",
                                customerPhone = "0987654321",
                                reservationStatus = 4 // Completed
                            ),
                            Reservation(
                                tableName = "Bar Table",
                                reservationTime = "2025-07-18T13:00",
                                numPeople = 6,
                                customerName = "Bob Johnson",
                                customerPhone = "1122334455",
                                reservationStatus = 3 // Failed
                            )
                        ),
                        success = true,
                        message = null
                    )
                )
        )
    }
}