package com.thedasagroup.suminative.ui.rewards

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.data.model.response.rewards.RewardItem
import com.thedasagroup.suminative.data.model.response.rewards.RewardsOverviewResponse
import kotlinx.coroutines.launch

// Theme colors
private val ThemeGreen = Color(0xFF2E7D32)
private val LightGray = Color(0xFFF5F5F5)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun RewardsOverviewScreen(
    viewModel: RewardsViewModel,
    userId: Int,
    businessId : Int,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val state by viewModel.collectAsState()
    val coroutineScope = rememberCoroutineScope()
    
    // Load rewards overview when screen opens
    LaunchedEffect(userId) {
        viewModel.getRewardsOverview(userId, businessId = businessId)
    }
    
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        // Top App Bar
        TopAppBar(
            title = {
                Text(
                    text = "Rewards Overview",
                    fontWeight = FontWeight.Bold,
                    fontSize = 20.sp
                )
            },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "Back"
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = ThemeGreen,
                titleContentColor = Color.White,
                navigationIconContentColor = Color.White
            )
        )
        
        // Content
        when (val response = state.rewardsOverviewResponse) {
            is Loading -> {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(color = ThemeGreen)
                }
            }
            is Success -> {
                val overview = response.invoke()
                if (overview != null) {
                    RewardsOverviewContent(
                        overview = overview,
                        modifier = Modifier.fillMaxSize()
                    )
                } else {
                    ErrorMessage("Failed to load rewards overview")
                }
            }
            else -> {
                ErrorMessage("Failed to load rewards overview")
            }
        }
    }
}

@Composable
private fun RewardsOverviewContent(
    overview: RewardsOverviewResponse,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier.padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // User Points Card
        item {
            UserPointsCard(points = overview.userPoints ?: 0.0)
        }
        
        // Reward Items Header
        item {
            Text(
                text = "Available Rewards",
                fontSize = 20.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black,
                modifier = Modifier.padding(vertical = 8.dp)
            )
        }
        
        // Reward Items
        items(overview.rewardItems ?: emptyList()) { rewardItem ->
            RewardItemCard(rewardItem = rewardItem)
        }
    }
}

@Composable
private fun UserPointsCard(
    points: Double,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = ThemeGreen),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Star,
                contentDescription = "Points",
                tint = Color.White,
                modifier = Modifier.size(32.dp)
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "Your Points",
                fontSize = 16.sp,
                color = Color.White.copy(alpha = 0.9f)
            )
            
            Text(
                text = points.toInt().toString(),
                fontSize = 32.sp,
                fontWeight = FontWeight.Bold,
                color = Color.White
            )
        }
    }
}

@Composable
private fun RewardItemCard(
    rewardItem: RewardItem,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = Color.White),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Product Image
            AsyncImage(
                model = rewardItem.storeItem?.pic,
                contentDescription = rewardItem.storeItem?.name,
                modifier = Modifier
                    .size(80.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(LightGray),
                contentScale = ContentScale.Crop
            )
            
            Spacer(modifier = Modifier.width(16.dp))
            
            // Product Details
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = rewardItem.storeItem?.name ?: "Unknown Item",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black
                )
                
                if (!rewardItem.storeItem?.description.isNullOrBlank()) {
                    Text(
                        text = rewardItem.storeItem?.description ?: "",
                        fontSize = 14.sp,
                        color = Color.Gray,
                        maxLines = 2,
                        modifier = Modifier.padding(top = 4.dp)
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Price and Points Required
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "$${rewardItem.storeItem?.price ?: 0.0}",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = ThemeGreen
                    )
                    
                    Spacer(modifier = Modifier.width(16.dp))
                    
                    Surface(
                        shape = RoundedCornerShape(16.dp),
                        color = ThemeGreen.copy(alpha = 0.1f)
                    ) {
                        Text(
                            text = "${rewardItem.stampsRequired} points required",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            color = ThemeGreen,
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun ErrorMessage(
    message: String,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = message,
                fontSize = 16.sp,
                color = Color.Gray,
                textAlign = TextAlign.Center
            )
        }
    }
}
