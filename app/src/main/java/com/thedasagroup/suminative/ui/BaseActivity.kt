//package com.thedasagroup.suminative.ui
//
//import android.content.Context
//import android.content.Intent
//import android.os.Bundle
//import android.os.CountDownTimer
//import androidx.appcompat.app.AppCompatActivity
//import com.google.firebase.remoteconfig.FirebaseRemoteConfig
//import com.thedasagroup.suminative.App.Companion.WAITER_IDLE_TIME
//import com.thedasagroup.suminative.ui.user_profile.SelectUserProfileActivity
//
//
//abstract class BaseActivity : AppCompatActivity(){
//
//    private val interval = (1 * 1000).toLong()
//    private lateinit var countDownTimer: MyCountDownTimer
//
//    override fun onCreate(savedInstanceState: Bundle?) {
//        super.onCreate(savedInstanceState)
//        val firebaseRemoteConfig = FirebaseRemoteConfig.getInstance()
//        val startTime = firebaseRemoteConfig.getLong(WAITER_IDLE_TIME)
//        countDownTimer = MyCountDownTimer(startTime * 1000, interval, this)
//    }
//
//
//    override fun onUserInteraction() {
//        super.onUserInteraction()
//        //Reset the timer on user interaction...
//        countDownTimer.cancel();
//        countDownTimer.start();
//    }
//}
//
//class MyCountDownTimer(startTime: Long, interval: Long, val context : Context) : CountDownTimer(startTime, interval) {
//
//    override fun onFinish() {
//        val intent = Intent(
//            context,
//            SelectUserProfileActivity::class.java,
//        )
//        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
//        context.startActivity(intent)
//    }
//
//    override fun onTick(millisUntilFinished: Long) {
//    }
//}