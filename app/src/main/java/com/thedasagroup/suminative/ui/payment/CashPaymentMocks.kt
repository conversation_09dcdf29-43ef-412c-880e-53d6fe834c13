package com.thedasagroup.suminative.ui.payment

import com.airbnb.mvrx.Success
import com.airbnb.mvrx.mocking.mockSingleViewModel
import com.thedasagroup.suminative.data.model.request.order.Cart
import com.thedasagroup.suminative.data.model.request.order.Customer
import com.thedasagroup.suminative.data.model.request.order.DeliveryAddress
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.request.order.PandaOrderDetail
import com.thedasagroup.suminative.data.model.request.order.StoreItem

fun CashPaymentDialogFragment.mocks() = mockSingleViewModel(
    CashPaymentDialogFragment::viewModel,
    defaultState = PaymentState(),
    defaultArgs = null
){
    state(name = "No Amount Given") {
        PaymentState(
            order = Success(Order(
                totalPrice = 30.0,
            )),
            amountGivenText = "",
            showChangeCalculation = false
        )
    }
    
    state(name = "Amount Given - No Change") {
        PaymentState(
            amountGivenText = "25.00",
            showChangeCalculation = false
        )
    }
    
    state(name = "Amount Given - Show Change") {
        PaymentState(
            order = Success(Order(
                totalPrice = 30.0,
            )),
            amountGivenText = "30.00",
            showChangeCalculation = true
        )
    }

    state(name = "Amount Given - Negative Change") {
        PaymentState(
            order = Success(Order(
                totalPrice = 10.0,
            )),
            amountGivenText = "20.00",
            showChangeCalculation = true
        )
    }

    state(name = "Amount Given - Same Amount") {
        PaymentState(
            order = Success(Order(
                totalPrice = 12.10,
            )),
            amountGivenText = "12.10",
            showChangeCalculation = true
        )
    }
    
    state(name = "Large Amount Given") {
        PaymentState(
            amountGivenText = "100.00",
            showChangeCalculation = true
        )
    }
    
    state(name = "Invalid Amount Format") {
        PaymentState(
            amountGivenText = "abc.def",
            showChangeCalculation = false
        )
    }
}