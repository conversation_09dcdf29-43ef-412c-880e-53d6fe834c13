package com.thedasagroup.suminative.ui.stores

import android.content.Intent
import android.os.Build
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Text
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.airbnb.mvrx.MavericksView
import com.thedasagroup.suminative.ui.service.Actions
import com.thedasagroup.suminative.ui.service.EndlessSocketService
import com.thedasagroup.suminative.ui.service.ServiceState
import com.thedasagroup.suminative.ui.service.getServiceState
import com.thedasagroup.suminative.ui.service.log
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme

class ClosedStoreActivity : AppCompatActivity(), MavericksView {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            SumiNativeTheme {
                Column(modifier = Modifier.fillMaxSize(),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center) {
                    Text(text = "Store Closed")
                }
            }
        }
    }

    override fun invalidate() {

    }

    private fun scheduleJob(){
//        actionOnService(Actions.START)
    }

    private fun actionOnService(action: Actions) {
        if (getServiceState(this) == ServiceState.STOPPED && action == Actions.STOP) return
        Intent(this, EndlessSocketService::class.java).also {
            it.action = action.name
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                log("Starting the service in >=26 Mode")
                startForegroundService(it)
                return
            }
            log("Starting the service in < 26 Mode")
            startService(it)
        }
    }
}