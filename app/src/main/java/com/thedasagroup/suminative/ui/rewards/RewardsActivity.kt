package com.thedasagroup.suminative.ui.rewards

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.lifecycle.lifecycleScope
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.compose.collectAsState
import com.airbnb.mvrx.viewModel
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

@AndroidEntryPoint
class RewardsActivity : ComponentActivity(), MavericksView {
    
    private val viewModel: RewardsViewModel by viewModel()
    private val productsViewModel : ProductsScreenViewModel by viewModel()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            val productState by productsViewModel.collectAsState()
//            RewardsScreen(
//                viewModel = viewModel,
//                onBackClick = {
//                    finish()
//                },
//                modifier = Modifier.fillMaxSize(),
//                onAddToCart = {rewardItem ->
//                    // Add to cart with zero price
//                    RewardsCartHelper.addRewardItemToCart(
//                        rewardItem = rewardItem,
//                        productsViewModel = productsViewModel,
//                        state = productState
//                    )
//                }
//            )
        }
    }
    
    override fun invalidate() {
        // Required by MavericksView
    }
}
