package com.thedasagroup.suminative.ui.rewards

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer
import kotlinx.coroutines.launch

// Theme colors

private val ThemeGreen = Color(0xFF2E7D32)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddPointsDialog(
    customer: RewardsCustomer,
    viewModel: RewardsViewModel,
    onDismiss: () -> Unit,
    onPointsAdded: (() -> Unit)? = null
) {
    val state by viewModel.collectAsState()
    var orderAmount by remember { mutableStateOf("") }
    var points by remember { mutableStateOf("") }
    var orderId by remember { mutableStateOf("") }
    var description by remember { mutableStateOf("") }
    var showSuccessMessage by remember { mutableStateOf(false) }
    val coroutineScope = rememberCoroutineScope()
    
    // Watch for successful add points response
    LaunchedEffect(state.addPointsResponse) {
        if (state.addPointsResponse is Success) {
            val response = state.addPointsResponse.invoke()
            if (response?.status == "success") {
                showSuccessMessage = true
                // Close the rewards dialog
                viewModel.showRewardsDialog(show = false)
                // Call the callback if provided
                onPointsAdded?.invoke()
                onDismiss()
            }
        }
    }

    Dialog(properties = DialogProperties(usePlatformDefaultWidth = false),onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp)
            ) {
                // Dialog Title
                Text(
                    text = "Add Reward Points",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                // Customer Info
                Text(
                    text = "Customer: ${customer.name ?: "N/A"}",
                    fontSize = 14.sp,
                    color = Color.Gray,
                    modifier = Modifier.padding(bottom = 16.dp)
                )
                
                if (showSuccessMessage) {
                    // Success Message
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 16.dp),
                        colors = CardDefaults.cardColors(containerColor = Color(0xFFE8F5E8))
                    ) {
                        Text(
                            text = "Points added successfully!",
                            modifier = Modifier.padding(12.dp),
                            color = Color(0xFF2E7D32),
                            fontWeight = FontWeight.Medium
                        )
                    }
                } else {
                    // Input Fields
                    OutlinedTextField(
                        value = orderAmount,
                        onValueChange = { 
                            orderAmount = it
                            // Auto-calculate points (assuming 1 point per £1)
                            val amount = it.toDoubleOrNull()
                            if (amount != null) {
                                points = amount.toInt().toString()
                            }
                        },
                        label = { Text("Order Amount (£)") },
                        placeholder = { Text("0.00") },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 12.dp),
                        keyboardOptions = KeyboardOptions(
                            keyboardType = KeyboardType.Decimal,
                            imeAction = ImeAction.Next
                        ),
                        singleLine = true
                    )
                    
                    OutlinedTextField(
                        value = points,
                        onValueChange = { points = it },
                        label = { Text("Points to Add") },
                        placeholder = { Text("0") },
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 12.dp),
                        keyboardOptions = KeyboardOptions(
                            keyboardType = KeyboardType.Number,
                            imeAction = ImeAction.Next
                        ),
                        singleLine = true,
                        enabled = false
                    )
                }
                
                // Error Message
                state.addPointsResponse.invoke()?.let { response ->
                    if (response.status != "success") {
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 16.dp),
                            colors = CardDefaults.cardColors(containerColor = Color(0xFFFFEBEE))
                        ) {
                            Text(
                                text = response.message ?: "Failed to add points",
                                modifier = Modifier.padding(12.dp),
                                color = Color(0xFFD32F2F)
                            )
                        }
                    }
                }
                
                // Loading indicator
                if (state.isLoading) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
                
                // Action Buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // Cancel Button
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier.weight(1f),
                        enabled = !state.isLoading
                    ) {
                        Text("Cancel")
                    }
                    
                    if (showSuccessMessage) {
                        // Close Button
                        Button(
                            modifier = Modifier.weight(1f),
                            onClick = onDismiss,
                            colors = ButtonDefaults.buttonColors(containerColor = ThemeGreen),
                            shape = RoundedCornerShape(8.dp)
                        ) {
                            Text(
                                text = "Close",
                                color = Color.White,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    } else {
                        // Add Points Button
                        Button(
                            modifier = Modifier.weight(1f),
                            onClick = {
                                val pointsToAdd = points.toIntOrNull()
                                val orderIdInt = orderId.toIntOrNull()

                                if (pointsToAdd != null && pointsToAdd > 0 && customer.id != null) {
                                    coroutineScope.launch {
                                        viewModel.addPoints(
                                            customerId = customer.id,
                                            points = pointsToAdd,
                                            businessId = customer.businessId ?: 0,
                                            orderId = orderIdInt,
                                            description = description.ifEmpty { null }
                                        )
                                    }
                                }
                            },
                            colors = ButtonDefaults.buttonColors(containerColor = ThemeGreen),
                            shape = RoundedCornerShape(8.dp),
                            enabled = !state.isLoading
                        ) {
                            Text(
                                text = if (state.isLoading) "Adding..." else "Add Points",
                                color = Color.White,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }
            }
        }
    }
}
