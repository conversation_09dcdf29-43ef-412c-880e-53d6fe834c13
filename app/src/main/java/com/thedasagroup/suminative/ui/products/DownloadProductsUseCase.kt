package com.thedasagroup.suminative.ui.products

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Success
import com.thedasagroup.suminative.data.model.request.stock.GetPagedStockItemsRequest
import com.thedasagroup.suminative.data.model.response.stock.StockItemsResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.ProductRepository
import com.thedasagroup.suminative.data.repo.StockRepository
import com.thedasagroup.suminative.data.repo.OptionRepository
import com.thedasagroup.suminative.data.database.CategoryRepository
import com.thedasagroup.suminative.data.model.request.option_details.GetOptionDetailsRequest
import com.thedasagroup.suminative.data.model.request.category_sorting.CategorySortingRequest
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.delay

class DownloadProductsUseCase(
    private val stockRepository: StockRepository,
    private val productRepository: ProductRepository,
    private val optionRepository: OptionRepository,
    private val categoryRepository: CategoryRepository,
    private val prefs: Prefs
) {
    suspend operator fun invoke(): StateFlow<Async<Boolean>> {
        val flow = MutableStateFlow<Async<Boolean>>(Success(false))
        
        val storeId = prefs.store?.id ?: 0
        if (storeId == 0) {
            flow.value = Success(false)
            return flow
        }

        // Get stock items from API
        val stockItems = stockRepository.getPagedStockItems(
            request = GetPagedStockItemsRequest(storeId = storeId)
        )
        
        when (stockItems.value) {
            is Success -> {
                val response = stockItems.value()
                if (response?.success == true && response.items?.isNotEmpty() == true) {
                    // Save products to database
                    val saveResult = productRepository.insertMultipleProducts(response.items)
                    when (saveResult.value) {
                        is Success -> {
                            // Now fetch and save option details for each product
                            var optionsSuccess = true
                            response.items.forEach { product ->
                                product.id?.let { productId ->
                                    try {
                                        // Add small delay to avoid overwhelming the API
                                        delay(100)
                                        
                                        // Get option details for this product
                                        val optionDetailsResult = stockRepository.getOptionDetails(
                                            GetOptionDetailsRequest(itemId = productId)
                                        )
                                        
                                        when (optionDetailsResult.value) {
                                            is Success -> {
                                                val optionDetails = optionDetailsResult.value()
                                                if (optionDetails?.success == true && 
                                                    !optionDetails.optionSets.isNullOrEmpty()) {
                                                    // Save option details to database
                                                    val optionSaveResult = optionRepository.saveOptionDetails(
                                                        productId = productId,
                                                        storeId = storeId,
                                                        optionDetails = optionDetails
                                                    )
                                                    if (optionSaveResult.value !is Success || 
                                                        optionSaveResult.value() != true) {
                                                        optionsSuccess = false
                                                    }
                                                }
                                            }
                                            else -> {
                                                // Option details fetch failed, but we continue
                                                // (not all products have options)
                                            }
                                        }
                                    } catch (e: Exception) {
                                        // Continue with other products if one fails
                                        optionsSuccess = false
                                    }
                                }
                            }
                            
                            // After successfully downloading products, also download and save categories
                            val categorySyncSuccess = downloadAndSaveCategories(storeId.toLong())
                            
                            flow.value = Success(saveResult.value() ?: false)
                        }
                        else -> {
                            flow.value = Success(false)
                        }
                    }
                } else {
                    flow.value = Success(false)
                }
            }
            else -> {
                flow.value = Success(false)
            }
        }
        
        return flow
    }
    
    suspend fun checkExistingProducts(): StateFlow<Async<Boolean>> {
        val flow = MutableStateFlow<Async<Boolean>>(Success(false))
        
        val storeId = prefs.store?.id ?: 0
        if (storeId == 0) {
            flow.value = Success(false)
            return flow
        }
        
        val existingProducts = productRepository.getProductsByStore(storeId)
        when (existingProducts.value) {
            is Success -> {
                val products = existingProducts.value() ?: emptyList()
                flow.value = Success(products.isNotEmpty())
            }
            else -> {
                flow.value = Success(false)
            }
        }
        
        return flow
    }
    
    suspend fun refreshProducts(): StateFlow<Async<Boolean>> {
        val flow = MutableStateFlow<Async<Boolean>>(Success(false))
        
        val storeId = prefs.store?.id ?: 0
        if (storeId == 0) {
            flow.value = Success(false)
            return flow
        }

        // Get latest stock items from API
        val stockItems = stockRepository.getPagedStockItems(
            request = GetPagedStockItemsRequest(storeId = storeId)
        )
        
        when (stockItems.value) {
            is Success -> {
                val response = stockItems.value()
                if (response?.success == true && response.items?.isNotEmpty() == true) {
                    // Use insertMultipleProducts which handles upsert (insert or update)
                    val saveResult = productRepository.insertMultipleProducts(response.items)
                    when (saveResult.value) {
                        is Success -> {
                            // Now fetch and save option details for each product
                            var optionsSuccess = true
                            response.items.forEach { product ->
                                product.id?.let { productId ->
                                    try {
                                        // Add small delay to avoid overwhelming the API
                                        delay(100)
                                        
                                        // Get option details for this product
                                        val optionDetailsResult = stockRepository.getOptionDetails(
                                            GetOptionDetailsRequest(itemId = productId)
                                        )
                                        
                                        when (optionDetailsResult.value) {
                                            is Success -> {
                                                val optionDetails = optionDetailsResult.value()
                                                if (optionDetails?.success == true && 
                                                    !optionDetails.optionSets.isNullOrEmpty()) {
                                                    // Save option details to database
                                                    val optionSaveResult = optionRepository.saveOptionDetails(
                                                        productId = productId,
                                                        storeId = storeId,
                                                        optionDetails = optionDetails
                                                    )
                                                    if (optionSaveResult.value !is Success || 
                                                        optionSaveResult.value() != true) {
                                                        optionsSuccess = false
                                                    }
                                                }
                                            }
                                            else -> {
                                                // Option details fetch failed, but we continue
                                                // (not all products have options)
                                            }
                                        }
                                    } catch (e: Exception) {
                                        // Continue with other products if one fails
                                        optionsSuccess = false
                                    }
                                }
                            }
                            
                            // After successfully refreshing products, also download and save categories
                            val categorySyncSuccess = downloadAndSaveCategories(storeId.toLong())
                            
                            flow.value = Success(saveResult.value() ?: false)
                        }
                        else -> {
                            flow.value = Success(false)
                        }
                    }
                } else {
                    flow.value = Success(false)
                }
            }
            else -> {
                flow.value = Success(false)
            }
        }
        
        return flow
    }
    
    // Helper function to download and save categories
    private suspend fun downloadAndSaveCategories(storeId: Long): Boolean {
        return try {
            // Get category sorting from API
            val categorySortingResult = stockRepository.getCategorySorting(
                CategorySortingRequest(storeId = storeId.toString())
            )
            
            when (categorySortingResult.value) {
                is Success -> {
                    val categories = categorySortingResult.value()?.categories
                    if (!categories.isNullOrEmpty()) {
                        // Save categories to database
                        categoryRepository.syncCategoriesFromApi(categories, storeId)
                        true
                    } else {
                        false
                    }
                }
                else -> false
            }
        } catch (e: Exception) {
            false
        }
    }
} 