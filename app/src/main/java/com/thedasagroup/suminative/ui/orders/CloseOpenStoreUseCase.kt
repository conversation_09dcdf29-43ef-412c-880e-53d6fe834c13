package com.thedasagroup.suminative.ui.orders

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.thedasagroup.suminative.data.model.request.store_settings.StoreSettingsRequest
import com.thedasagroup.suminative.data.model.response.close_open_store.CloseOpenStoreResponse
import com.thedasagroup.suminative.data.model.response.login.LoginResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.LoginRepository
import com.thedasagroup.suminative.data.repo.OrdersRepository
import kotlinx.coroutines.flow.StateFlow

class CloseOpenStoreUseCase(
    private val ordersRepository: OrdersRepository, private val prefs: Prefs
) {
    suspend operator fun invoke(closed: Boolean): StateFlow<Async<CloseOpenStoreResponse>> {
        val response = ordersRepository.closeOpenStore(
            storeId = (prefs.store?.id ?: 105).toString(), closed = closed
        )
        return when(response.value){
            is Success -> {
                ordersRepository.isClosed(storeId = (prefs.store?.id ?: 105).toString())
            }

            else -> {
                response
            }
        }
    }
}