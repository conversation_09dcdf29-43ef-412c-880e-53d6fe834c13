package com.thedasagroup.suminative.ui.payment

import android.app.Activity
import android.content.Intent
import com.sumup.merchant.reader.api.SumUpAPI
import com.sumup.merchant.reader.api.SumUpLogin
import com.sumup.merchant.reader.api.SumUpPayment
import com.sumup.merchant.reader.models.TransactionInfo
import com.thedasagroup.suminative.data.model.request.order.Order
import java.math.BigDecimal
import java.util.UUID

object SumUpPaymentHelper {

    const val REQUEST_CODE_SUMUP_LOGIN = 1001
    const val REQUEST_CODE_SUMUP_PAYMENT = 1002

    // SumUp Affiliate Key - Replace with your actual affiliate key
    private const val SUMUP_AFFILIATE_KEY = "sup_afk_GwzGZwAVI4oI7RITwJp7CxbfOmN3ze2x"

    /**
     * Check if user is logged in to SumUp
     */
    fun isLoggedIn(): Boolean {
        return try {
            SumUpAPI.isLoggedIn()
        } catch (e: Exception) {
            android.util.Log.e("SumUpPaymentHelper", "Error checking login status", e)
            false
        }
    }

    /**
     * Start SumUp login flow
     */
    fun startLogin(activity: Activity) {
        try {
            val sumupLogin = SumUpLogin.builder(SUMUP_AFFILIATE_KEY).build()
            SumUpAPI.openLoginActivity(activity, sumupLogin, REQUEST_CODE_SUMUP_LOGIN)
        } catch (e: Exception) {
            android.util.Log.e("SumUpPaymentHelper", "Error starting SumUp login", e)
            android.widget.Toast.makeText(activity, "Error starting SumUp login", android.widget.Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * Start SumUp payment flow
     */
    fun startPayment(activity: Activity, order: Order) {
        try {
            val totalAmount = order.totalPrice ?: 0.0

            // SumUp requires minimum amount of 1.00
            val amount = if (totalAmount < 1.0) BigDecimal("1.00") else BigDecimal(totalAmount.toString())

            val payment = SumUpPayment.builder()
                // Mandatory parameters
                .total(amount)
                .currency(SumUpPayment.Currency.GBP)
                // Optional: add details
                .title("POS Order Payment")
                .receiptEmail("") // Can be set if customer email is available
                // Optional: Add metadata
                .addAdditionalInfo("OrderId", order.id?.toString() ?: "")
                .addAdditionalInfo("PaymentType", "Card")
                .addAdditionalInfo("Store", "DasaPOS")
                .configureRetryPolicy(2000, 60000, true)
                // Optional: foreign transaction ID, must be unique!
                .foreignTransactionId(UUID.randomUUID().toString())
                .skipSuccessScreen()
                .build()

            SumUpAPI.checkout(activity, payment, REQUEST_CODE_SUMUP_PAYMENT)
        } catch (e: Exception) {
            android.util.Log.e("SumUpPaymentHelper", "Error starting SumUp payment", e)
            android.widget.Toast.makeText(activity, "Error starting SumUp payment", android.widget.Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * Get current merchant info
     */
    fun getCurrentMerchantInfo(): String? {
        return if (isLoggedIn()) {
            val merchant = SumUpAPI.getCurrentMerchant()
            "Currency: ${merchant?.currency?.isoCode}, Merchant Code: ${merchant?.merchantCode}"
        } else {
            null
        }
    }

    /**
     * Logout from SumUp
     */
    fun logout() {
        SumUpAPI.logout()
    }

    /**
     * Prepare card terminal for checkout
     */
    fun prepareForCheckout() {
        SumUpAPI.prepareForCheckout()
    }
}
