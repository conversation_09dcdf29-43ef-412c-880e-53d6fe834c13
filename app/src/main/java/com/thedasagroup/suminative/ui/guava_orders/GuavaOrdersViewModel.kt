package com.thedasagroup.suminative.ui.guava_orders

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Fail
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.MavericksViewModelFactory
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.hilt.AssistedViewModelFactory
import com.airbnb.mvrx.hilt.hiltMavericksViewModelFactory
import com.thedasagroup.suminative.data.model.response.my_guava.orders.create_order.GuavaOrderResponse
import com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GetListOfOrdersResponse
import com.thedasagroup.suminative.data.repo.MyGuavaRepository
import com.thedasagroup.suminative.domain.myguava.MyGuavaGetOrdersUseCase
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

data class GuavaOrdersState(
    val ordersResponse: Async<GetListOfOrdersResponse> = Uninitialized,
    val refreshing: Boolean = false,
    val voidOrderResponse: Async<GuavaOrderResponse> = Uninitialized
) : MavericksState

class GuavaOrdersViewModel @AssistedInject constructor(
    @Assisted initialState: GuavaOrdersState,
    private val myGuavaGetOrdersUseCase: MyGuavaGetOrdersUseCase,
    private val myGuavaRepository: MyGuavaRepository
) : MavericksViewModel<GuavaOrdersState>(initialState) {

    init {
        viewModelScope.launch(Dispatchers.IO) {
            fetchOrders()
        }
    }

    suspend fun fetchOrders() {
        setState { copy(refreshing = true) }
        myGuavaGetOrdersUseCase().execute {
            when (it) {
                is Success -> {
                    copy(ordersResponse = it(), refreshing = false)
                }
                else -> {
                    copy(ordersResponse = Uninitialized, refreshing = false)
                }
            }
        }
    }

    fun voidOrder(orderId: String, amount : String) {
        setState { copy(voidOrderResponse = Loading(), refreshing = true) }
        viewModelScope.launch(Dispatchers.IO) {
            myGuavaRepository.voidOrder(orderId = orderId, amount = amount).execute { result ->
                when (result) {
                    is Success -> {
                        viewModelScope.launch {
                            fetchOrders()
                        }
                        copy(voidOrderResponse = result())
                    }
                    else -> {
                        copy(voidOrderResponse = Uninitialized)
                    }
                }
            }
        }
    }

    @AssistedFactory
    interface Factory : AssistedViewModelFactory<GuavaOrdersViewModel, GuavaOrdersState> {
        override fun create(state: GuavaOrdersState): GuavaOrdersViewModel
    }

    companion object : MavericksViewModelFactory<GuavaOrdersViewModel, GuavaOrdersState> by hiltMavericksViewModelFactory()
} 