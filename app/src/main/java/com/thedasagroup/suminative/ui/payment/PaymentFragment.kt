package com.thedasagroup.suminative.ui.payment

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.app.AlertDialog
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.fragment.app.DialogFragment
import androidx.lifecycle.viewmodel.compose.viewModel
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.activityViewModel
import com.airbnb.mvrx.fragmentViewModel
import com.airbnb.mvrx.mocking.MockableMavericksView
import com.airbnb.mvrx.viewModel
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.response.store_orders.Order2
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme
import dagger.hilt.android.AndroidEntryPoint

class PaymentFragment : DialogFragment(), MockableMavericksView {

    val paymentViewModel : PaymentViewModel by fragmentViewModel()
    val productsScreenViewModel: ProductsScreenViewModel by activityViewModel()

    private var order : Order? = null
    private var onPaymentSuccess : ((Order2) -> Unit) = {}

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Configure dialog appearance
        // setStyle(STYLE_NORMAL, android.R.style.Theme_Material_Light_NoActionBar_Fullscreen)
        
        // Get arguments if passed
        arguments?.let {
            order = it.getParcelable(ARG_ORDER)
        }
        
        // Initialize with default order if none provided
        paymentViewModel.updateOrder(order ?: Order())
    }
    
    override fun onStart() {
        super.onStart()
        dialog?.let { dialog ->
            // Make dialog fullscreen with proper width/height
            dialog.window?.let { window ->
                window.setLayout(
                    WindowManager.LayoutParams.MATCH_PARENT,
                    WindowManager.LayoutParams.MATCH_PARENT
                )
                // Optional: remove background dimming
                window.clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
            }
        }
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                SumiNativeTheme {
                    androidx.compose.material3.Surface(
                        modifier = Modifier.fillMaxSize(fraction = 0.7f),
                        color = Color(0xFF0A2540)
                    ) {
                        PaymentScreen(
                            onMakePaymentClickSuccess = {order1 ->
                                requireActivity().let { activity ->
                                    AlertDialog.Builder(activity)
                                        .setTitle("Success")
                                        .setMessage("Payment completed successfully")
                                        .setPositiveButton("OK") { dialog, _ ->
                                            onPaymentSuccess(order1)
                                            dismiss() // Dismiss the dialog fragment
                                        }
                                        .show()
                                }
                            },
                            onMakePaymentClickFail = {guavaFailResponse ->
                                requireActivity().let { activity ->
                                    AlertDialog.Builder(activity)
                                        .setTitle("Error")
                                        .setMessage(guavaFailResponse.error)
                                        .setPositiveButton("OK") { dialog, _ ->
                                            dialog.dismiss()
                                        }
                                        .show()
                                }
                            },
                            onPaymentCancelled = {
                                requireActivity().let { activity ->
                                    AlertDialog.Builder(activity)
                                        .setTitle("Payment Cancelled")
                                        .setMessage("The payment has been cancelled.")
                                        .setPositiveButton("OK") { dialog, _ ->
                                            dialog.dismiss()
                                            <EMAIL>()
                                        }
                                        .show()
                                }
                            },
                            onSumUpPaymentClick = { order ->
                                // Direct SumUp login/payment flow - no intermediate activity
                                if (SumUpPaymentHelper.isLoggedIn()) {
                                    // Already logged in, start payment directly
                                    SumUpPaymentHelper.startPayment(requireActivity(), order)
                                } else {
                                    // Not logged in, start login first
                                    SumUpPaymentHelper.startLogin(requireActivity())
                                }
                            },
                            paymentViewModel = paymentViewModel,
                            productsScreenViewModel = productsScreenViewModel
                        )
                    }
                }
            }
        }
    }

    override fun invalidate() {
        // Called when the state is updated
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            SumUpPaymentHelper.REQUEST_CODE_SUMUP_LOGIN -> {
                handleSumUpLoginResult(data)
            }
            SumUpPaymentHelper.REQUEST_CODE_SUMUP_PAYMENT -> {
                handleSumUpPaymentResult(data)
            }
        }
    }

    private fun handleSumUpLoginResult(data: Intent?) {
        if (data != null) {
            val extra = data.extras
            val resultCode = extra?.getInt(com.sumup.merchant.reader.api.SumUpAPI.Response.RESULT_CODE)
            val message = extra?.getString(com.sumup.merchant.reader.api.SumUpAPI.Response.MESSAGE) ?: "Unknown error"

            val success = resultCode == com.sumup.merchant.reader.api.SumUpAPI.Response.ResultCode.SUCCESSFUL
            if (success) {
                // Auto-start payment after successful login
                // Get the current order from the payment screen
                // For now, we'll show a success message
                requireActivity().let { activity ->
                    AlertDialog.Builder(activity)
                        .setTitle("Login Successful")
                        .setMessage("SumUp login successful. Please try payment again.")
                        .setPositiveButton("OK") { dialog, _ ->
                            dialog.dismiss()
                        }
                        .show()
                }
            } else {
                requireActivity().let { activity ->
                    AlertDialog.Builder(activity)
                        .setTitle("Login Failed")
                        .setMessage("SumUp login failed: $message")
                        .setPositiveButton("OK") { dialog, _ ->
                            dialog.dismiss()
                        }
                        .show()
                }
            }
        } else {
            requireActivity().let { activity ->
                AlertDialog.Builder(activity)
                    .setTitle("Login Cancelled")
                    .setMessage("SumUp login was cancelled.")
                    .setPositiveButton("OK") { dialog, _ ->
                        dialog.dismiss()
                    }
                    .show()
            }
        }
    }

    private fun handleSumUpPaymentResult(data: Intent?) {
        if (data != null) {
            val extra = data.extras
            val resultCode = extra?.getInt(com.sumup.merchant.reader.api.SumUpAPI.Response.RESULT_CODE)
            val message = extra?.getString(com.sumup.merchant.reader.api.SumUpAPI.Response.MESSAGE) ?: "Unknown error"
            val transactionInfo = extra?.getParcelable<com.sumup.merchant.reader.models.TransactionInfo>(com.sumup.merchant.reader.api.SumUpAPI.Response.TX_INFO)

            val success = resultCode == com.sumup.merchant.reader.api.SumUpAPI.Response.ResultCode.SUCCESSFUL
            if (success && transactionInfo != null) {
                // Create a mock Order2 object for successful payments
                val order2 = com.thedasagroup.suminative.data.model.response.store_orders.Order2(
                    id = transactionInfo.transactionCode?.toIntOrNull(),
                    // Add other fields as needed based on your Order2 structure
                )
                onPaymentSuccess(order2)
                dismiss()
            } else {
                requireActivity().let { activity ->
                    AlertDialog.Builder(activity)
                        .setTitle("Payment Failed")
                        .setMessage("SumUp payment failed: $message")
                        .setPositiveButton("OK") { dialog, _ ->
                            dialog.dismiss()
                        }
                        .show()
                }
            }
        } else {
            requireActivity().let { activity ->
                AlertDialog.Builder(activity)
                    .setTitle("Payment Cancelled")
                    .setMessage("SumUp payment was cancelled.")
                    .setPositiveButton("OK") { dialog, _ ->
                        dialog.dismiss()
                    }
                    .show()
            }
        }
    }
    
    override fun provideMocks() = mocks()
    
    companion object {
        // Tag for fragment manager
        const val TAG = "PaymentDialogFragment"
        
        // Argument keys
        const val ARG_ORDER = "order"
        
        fun newInstance(order: Order? = null, onPaymentSuccess : (Order2) -> Unit): PaymentFragment {
            val fragment = PaymentFragment()
            return fragment.apply {
                this.onPaymentSuccess = onPaymentSuccess
                arguments = Bundle().apply {
                    putParcelable(ARG_ORDER, order)
                }
            }
        }
    }
} 