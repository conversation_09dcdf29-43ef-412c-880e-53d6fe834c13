package com.thedasagroup.suminative.ui.products

/**
 * Check if a course should show the Go button
 * @param courseId The course to check
 * @return true if this course should show the Go button
 */
fun shouldShowGoButton(
    courseId: String,
    state: ProductsScreenState
): Boolean {
    val queue = state.getStatusQueue()

    // Check if course is not in preparing or completed queues
    val notInOtherQueues = queue.preparingQueue.firstOrNull {
        it == courseId
    }?.isNotEmpty() != true && queue.completedCourses.firstOrNull {
        it == courseId
    }?.isNotEmpty() != true

    // Find index of course in go queue
    val goQueueIndex = queue.goQueue.indexOfFirst { it == courseId }
    
    // Show Go button only for first item in go queue
    return notInOtherQueues && goQueueIndex == 0 && queue.goQueue.isNotEmpty()
}


fun shouldShowPreparing(
    courseId: String,
    state: ProductsScreenState
): Boolean {
    val queue = state.getStatusQueue()

    if(courseId.isNotEmpty()) {
        return queue.goQueue.firstOrNull {
            it == courseId
        }?.isNotEmpty() != true && queue.preparingQueue.firstOrNull {
            it == courseId
        }?.isNotEmpty() == true && queue.completedCourses.firstOrNull {
            it == courseId
        }?.isNotEmpty() != true
    }
    else {
        return queue.goQueue.firstOrNull {
            it == courseId
        } == null && queue.preparingQueue.firstOrNull {
            it == courseId
        } != null && queue.completedCourses.firstOrNull {
            it == courseId
        } == null
    }
}


fun shouldShowComplete(
    courseId: String,
    state: ProductsScreenState
): Boolean {
    val queue = state.getStatusQueue()

    if(courseId.isNotEmpty()) {
        return queue.goQueue.firstOrNull {
            it == courseId
        }?.isNotEmpty() != true && queue.preparingQueue.firstOrNull {
            it == courseId
        }?.isNotEmpty() != true && queue.completedCourses.firstOrNull {
            it == courseId
        }?.isNotEmpty() == true
    }
    else {
        return queue.goQueue.firstOrNull {
            it == courseId
        } == null && queue.preparingQueue.firstOrNull {
            it == courseId
        } == null && queue.completedCourses.firstOrNull {
            it == courseId
        } != null
    }
}