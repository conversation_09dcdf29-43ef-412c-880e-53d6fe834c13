package com.thedasagroup.suminative.ui.utils

import android.content.Context
import android.media.AudioManager
import android.media.MediaPlayer
import android.media.MediaPlayer.OnCompletionListener
import android.media.SoundPool
import android.os.Handler
import android.util.Log

class SoundPoolPlayer(maxStreams: Int, streamType: Int, srcQuality: Int) :
    SoundPool(1, streamType, srcQuality) {
    var context: Context? = null
    var soundId: Int = 0
    var resId: Int = 0
    var duration: Long = 0
    var isPlaying: Boolean = false
    var loaded: Boolean = false
    var loops: Int = -1
    var listener: OnCompletionListener? = null
    var runnable: Runnable = Runnable {
        if (isPlaying) {
            isPlaying = false
            Log.d("debug", "ending..")
            if (listener != null) {
                listener!!.onCompletion(null)
            }
        }
    }

    //timing related
    var handler: Handler? = null
    var startTime: Long = 0
    var endTime: Long = 0
    var timeSinceStart: Long = 0

    fun pause() {
        if (streamId > 0) {
            endTime = System.currentTimeMillis()
            timeSinceStart += endTime - startTime
            super.pause(streamId)
            if (handler != null) {
                handler!!.removeCallbacks(runnable)
            }
            isPlaying = false
        }
    }

    fun stop() {
        if (streamId > 0) {
            timeSinceStart = 0
            super.stop(streamId)
            if (handler != null) {
                handler!!.removeCallbacks(runnable)
            }
            isPlaying = false
        }
    }

    fun play() {
        loadAndPlay()
    }

    fun setOnCompletionListener(listener: OnCompletionListener?) {
        this.listener = listener
    }

    private fun loadAndPlay() {
        duration = getSoundDuration(resId)
        soundId = super.load(context, resId, 1)
        setOnLoadCompleteListener { soundPool, sampleId, status ->
            loaded = true
            playIt()
        }
    }

    private fun playIt() {
        if (loaded && !isPlaying) {
            Log.d("debug", "start playing..")
            if (timeSinceStart == 0L) {
                streamId = super.play(soundId, 1f, 1f, 1, loops, 1f)
            } else {
                super.resume(streamId)
            }
            startTime = System.currentTimeMillis()
            handler = Handler()
            handler!!.postDelayed(runnable, 2000)
            isPlaying = true
        }
    }

    private fun getSoundDuration(rawId: Int): Long {
        val player = MediaPlayer.create(context, rawId)
        val duration = player.duration
        return duration.toLong()
    }

    companion object {
        var streamId: Int = 0
        fun create(context: Context?, resId: Int): SoundPoolPlayer {
            val player = SoundPoolPlayer(1, AudioManager.STREAM_MUSIC, 0)
            player.context = context
            player.resId = resId
            return player
        }
    }
}