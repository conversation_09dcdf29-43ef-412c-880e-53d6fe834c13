package com.thedasagroup.suminative.ui.products

/**
 * Add a course to the GO queue
 */
fun CourseStatusQueue.addToGoQueue(courseId: String): CourseStatusQueue {
    if (goQueue.contains(courseId)) return this
    return copy(goQueue = goQueue + courseId)
}

/**
 * Add a course to the PREPARING queue
 */
fun CourseStatusQueue.addToPreparingQueue(courseId: String): CourseStatusQueue {
    if (preparingQueue.contains(courseId)) return this
    return copy(preparingQueue = preparingQueue + courseId)
}

/**
 * Remove a course from the GO queue
 */
fun CourseStatusQueue.removeFromGoQueue(courseId: String): CourseStatusQueue {
    return copy(goQueue = goQueue.filter { it != courseId })
}

/**
 * Remove a course from the PREPARING queue
 */
fun CourseStatusQueue.removeFromPreparingQueue(courseId: String): CourseStatusQueue {
    return copy(preparingQueue = preparingQueue.filter { it != courseId })
}

/**
 * Move a course from GO queue to PREPARING queue
 * Marks all previous courses as complete
 */
fun CourseStatusQueue.moveFromGoToPreparingWithAllPreviousComplete(
    courseId: String,
    allCourses: List<String>
): CourseStatusQueue {
    // Find the index of the current course
    val currentIndex = allCourses.indexOf(courseId)

    // All courses before the current one should be completed
    val allPreviousCourses = if (currentIndex > 0) {
        allCourses.subList(0, currentIndex)
    } else {
        emptyList()
    }

    // Add all previous courses to completed courses
    val coursesToComplete = (completedCourses + allPreviousCourses).distinct()

    return copy(
        goQueue = goQueue.filter { it != courseId },
        preparingQueue = preparingQueue + courseId,
        completedCourses = coursesToComplete
    )
}

/**
 * Move a course from GO queue to PREPARING queue (legacy method)
 */
fun CourseStatusQueue.moveFromGoToPreparing(courseId: String): CourseStatusQueue {
    return copy(
        goQueue = goQueue.filter { it != courseId },
        preparingQueue = preparingQueue + courseId
    )
}

/**
 * Complete all courses in the preparing queue
 */
fun CourseStatusQueue.completeAllPreparingCourses(): CourseStatusQueue {
    return copy(
        preparingQueue = emptyList(),
        completedCourses = (completedCourses + preparingQueue).distinct()
    )
}

/**
 * Complete a specific course from the preparing queue
 */
fun CourseStatusQueue.completeSpecificCourse(courseId: String): CourseStatusQueue {
    return if (preparingQueue.contains(courseId)) {
        copy(
            preparingQueue = preparingQueue.filter { it != courseId },
            completedCourses = completedCourses + courseId
        )
    } else {
        this
    }
}