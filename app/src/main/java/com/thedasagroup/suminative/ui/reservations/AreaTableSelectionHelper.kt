package com.thedasagroup.suminative.ui.reservations

import android.app.Activity
import android.content.Intent
import androidx.activity.result.ActivityResultLauncher
import com.thedasagroup.suminative.data.model.response.reservations.Table

/**
 * Helper class for launching the Area and Table Selection Activity
 * and handling the results.
 */
object AreaTableSelectionHelper {

    const val REQUEST_CODE_AREA_TABLE_SELECTION = 1001

    /**
     * Data class to hold the selected area and table information
     */
    data class AreaTableSelection(
        val areaId: Int,
        val areaName: String,
        val tableId: Int,
        val tableName: String,
        val tableCapacity: Int,
        val isOccupied : Boolean = false,
        val table : Table? = null
    )

    /**
     * Launch the Area and Table Selection Activity
     *
     * @param activity The calling activity
     * @param launcher The activity result launcher
     * @param excludedTableIds List of table IDs to exclude from selection (already selected tables)
     */
    fun launchAreaTableSelection(
        activity: Activity,
        launcher: ActivityResultLauncher<Intent>,
        excludedTableIds: List<Int> = emptyList()
    ) {
        val intent = AreaTableSelectionActivity.createIntent(activity,mutableListOf())
        launcher.launch(intent)
    }

    /**
     * Parse the result from the Area and Table Selection Activity
     * 
     * @param resultCode The result code from the activity
     * @param data The intent data containing the selection
     * @return AreaTableSelection object if successful, null otherwise
     */
    fun parseResult(resultCode: Int, data: Intent?): AreaTableSelection? {
        if (resultCode == Activity.RESULT_OK && data != null) {
            val areaId = data.getIntExtra(AreaTableSelectionActivity.EXTRA_SELECTED_AREA_ID, -1)
            val areaName = data.getStringExtra(AreaTableSelectionActivity.EXTRA_SELECTED_AREA_NAME) ?: ""
            val tableId = data.getIntExtra(AreaTableSelectionActivity.EXTRA_SELECTED_TABLE_ID, -1)
            val tableName = data.getStringExtra(AreaTableSelectionActivity.EXTRA_SELECTED_TABLE_NAME) ?: ""
            val tableCapacity = data.getIntExtra(AreaTableSelectionActivity.EXTRA_SELECTED_TABLE_CAPACITY, 0)
            val table = data.getParcelableExtra<Table>(AreaTableSelectionActivity.EXTRA_SELECTED_TABLE)

            if (areaId != -1 && tableId != -1) {
                return AreaTableSelection(
                    areaId = areaId,
                    areaName = areaName,
                    tableId = tableId,
                    tableName = tableName,
                    tableCapacity = tableCapacity,
                    table = table
                )
            }
        }
        return null
    }
}

/**
 * Extension function for Activity to easily launch area table selection
 */
fun Activity.launchAreaTableSelection(
    launcher: ActivityResultLauncher<Intent>,
    excludedTableIds: List<Int> = emptyList()
) {
    AreaTableSelectionHelper.launchAreaTableSelection(this, launcher, excludedTableIds)
}
