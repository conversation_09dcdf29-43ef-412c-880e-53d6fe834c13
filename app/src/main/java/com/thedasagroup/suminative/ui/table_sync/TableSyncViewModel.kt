package com.thedasagroup.suminative.ui.table_sync

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.MavericksViewModelFactory
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.hilt.AssistedViewModelFactory
import com.airbnb.mvrx.hilt.hiltMavericksViewModelFactory
import com.thedasagroup.suminative.data.model.request.table_sync.SyncOrderRequest
import com.thedasagroup.suminative.data.model.request.table_sync.UpdateOrderRequest
import com.thedasagroup.suminative.data.model.response.table_sync.SyncOrderResponse
import com.thedasagroup.suminative.data.model.response.table_sync.TableResponse
import com.thedasagroup.suminative.domain.table_sync.DeleteOrderForTableUseCase
import com.thedasagroup.suminative.domain.table_sync.GetSyncedOrderForTableUseCase
import com.thedasagroup.suminative.domain.table_sync.SyncOrderToTableUseCase
import com.thedasagroup.suminative.domain.table_sync.ToggleTableOccupiedUseCase
import com.thedasagroup.suminative.domain.table_sync.UpdateOrderForTableUseCase
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

/**
 * ViewModel for table sync operations using Mavericks state management
 */
class TableSyncViewModel @AssistedInject constructor(
    @Assisted state: TableSyncState,
    private val syncOrderToTableUseCase: SyncOrderToTableUseCase,
    private val getSyncedOrderForTableUseCase: GetSyncedOrderForTableUseCase,
    private val toggleTableOccupiedUseCase: ToggleTableOccupiedUseCase,
    private val updateOrderForTableUseCase: UpdateOrderForTableUseCase,
    private val deleteOrderForTableUseCase: DeleteOrderForTableUseCase
) : MavericksViewModel<TableSyncState>(state) {

    /**
     * Sync order to table
     * @param request The sync order request containing table and order data
     */
    suspend fun syncOrderToTable(request: SyncOrderRequest): StateFlow<Async<SyncOrderResponse>> {
        val flow = MutableStateFlow<Async<SyncOrderResponse>>(Loading())
        setState {
            copy(syncOrderResponse = Loading())
        }
        
        syncOrderToTableUseCase(request).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(syncOrderResponse = it())
                }
                else -> {
                    flow.value = Uninitialized
                    copy(syncOrderResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    /**
     * Get synced order for table
     * @param tableId The table ID to get the synced order for
     */
    suspend fun getSyncedOrderForTable(tableId: Int): StateFlow<Async<SyncOrderResponse>> {
        val flow = MutableStateFlow<Async<SyncOrderResponse>>(Loading())
        setState {
            copy(getSyncedOrderResponse = Loading())
        }
        
        getSyncedOrderForTableUseCase(tableId).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(getSyncedOrderResponse = it())
                }
                else -> {
                    flow.value = Uninitialized
                    copy(getSyncedOrderResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    /**
     * Clear sync order response state
     */
    fun clearSyncOrderResponse() {
        setState {
            copy(syncOrderResponse = Uninitialized)
        }
    }

    /**
     * Clear get synced order response state
     */
    fun clearGetSyncedOrderResponse() {
        setState {
            copy(getSyncedOrderResponse = Uninitialized)
        }
    }

    /**
     * Toggle table occupied status
     * @param tableId The table ID to toggle occupied status
     * @param netPayable The net payable amount for the table
     */
    suspend fun toggleTableOccupied(
        tableId: Int,
        netPayable: Double
    ): StateFlow<Async<TableResponse>> {
        val flow = MutableStateFlow<Async<TableResponse>>(Loading())
        setState {
            copy(toggleTableResponse = Loading())
        }

        toggleTableOccupiedUseCase(tableId, netPayable).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(toggleTableResponse = it())
                }
                else -> {
                    flow.value = Uninitialized
                    copy(toggleTableResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    /**
     * Update order for table
     * @param tableId The table ID to update the order for
     * @param request The update order request containing order data
     */
    suspend fun updateOrderForTable(
        tableId: Int,
        request: UpdateOrderRequest
    ): StateFlow<Async<SyncOrderResponse>> {
        val flow = MutableStateFlow<Async<SyncOrderResponse>>(Loading())
        setState {
            copy(updateOrderResponse = Loading())
        }

        updateOrderForTableUseCase(tableId, request).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(updateOrderResponse = it())
                }
                else -> {
                    flow.value = Uninitialized
                    copy(updateOrderResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    /**
     * Delete order for table
     * @param tableId The table ID to delete the order for
     */
    suspend fun deleteOrderForTable(tableId: Int): StateFlow<Async<SyncOrderResponse>> {
        val flow = MutableStateFlow<Async<SyncOrderResponse>>(Loading())
        setState {
            copy(deleteOrderResponse = Loading())
        }

        deleteOrderForTableUseCase(tableId).execute {
            when (it) {
                is Success -> {
                    flow.value = it()
                    copy(deleteOrderResponse = it())
                }
                else -> {
                    flow.value = Uninitialized
                    copy(deleteOrderResponse = Uninitialized)
                }
            }
        }
        return flow
    }

    /**
     * Clear toggle table response state
     */
    fun clearToggleTableResponse() {
        setState {
            copy(toggleTableResponse = Uninitialized)
        }
    }

    /**
     * Clear update order response state
     */
    fun clearUpdateOrderResponse() {
        setState {
            copy(updateOrderResponse = Uninitialized)
        }
    }

    /**
     * Clear delete order response state
     */
    fun clearDeleteOrderResponse() {
        setState {
            copy(deleteOrderResponse = Uninitialized)
        }
    }

    @AssistedFactory
    interface Factory : AssistedViewModelFactory<TableSyncViewModel, TableSyncState>

    companion object : MavericksViewModelFactory<TableSyncViewModel, TableSyncState> by hiltMavericksViewModelFactory()
}

/**
 * Mavericks state for table sync operations
 */
data class TableSyncState(
    val syncOrderResponse: Async<SyncOrderResponse> = Uninitialized,
    val getSyncedOrderResponse: Async<SyncOrderResponse> = Uninitialized,
    val toggleTableResponse: Async<TableResponse> = Uninitialized,
    val updateOrderResponse: Async<SyncOrderResponse> = Uninitialized,
    val deleteOrderResponse: Async<SyncOrderResponse> = Uninitialized
) : MavericksState
