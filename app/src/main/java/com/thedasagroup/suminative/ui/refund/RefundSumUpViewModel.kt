package com.thedasagroup.suminative.ui.refund

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.MavericksViewModel
import com.airbnb.mvrx.MavericksViewModelFactory
import com.airbnb.mvrx.Uninitialized
import com.airbnb.mvrx.hilt.AssistedViewModelFactory
import com.airbnb.mvrx.hilt.hiltMavericksViewModelFactory
import com.thedasagroup.suminative.data.model.request.pagination.OrderResponse
import com.thedasagroup.suminative.ui.orders.GetPendingOrdersPagedUseCase
import dagger.assisted.Assisted
import dagger.assisted.AssistedFactory
import dagger.assisted.AssistedInject
import kotlinx.coroutines.launch

data class RefundSumUpState(
    val ordersResponse: Async<OrderResponse> = Uninitialized,
    val isLoading: Boolean = false,
    val isRefreshing: Boolean = false
) : MavericksState

class RefundSumUpViewModel @AssistedInject constructor(
    @Assisted state: RefundSumUpState,
    private val getPendingOrdersPagedUseCase: GetPendingOrdersPagedUseCase
) : MavericksViewModel<RefundSumUpState>(state) {

    init {
        viewModelScope.launch {
            loadOrders()
        }
    }

    suspend fun loadOrders() {
        setState { copy(isLoading = true) }
        getPendingOrdersPagedUseCase(isShowAllOrder = true).execute { asyncResult ->
                copy(
                    ordersResponse = asyncResult() ?: Uninitialized,
                    isLoading = false,
                    isRefreshing = false
                )
        }
    }

    fun refreshOrders() {
        setState { copy(isRefreshing = true) }
        viewModelScope.launch {
            loadOrders()
        }
    }

    @AssistedFactory
    interface Factory : AssistedViewModelFactory<RefundSumUpViewModel, RefundSumUpState>

    companion object : MavericksViewModelFactory<RefundSumUpViewModel, RefundSumUpState> by hiltMavericksViewModelFactory()
}
