package com.thedasagroup.suminative.ui.refund

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import com.airbnb.mvrx.MavericksView
import com.airbnb.mvrx.viewModel
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class RefundSumUpActivity : ComponentActivity(), MavericksView {
    
    private val viewModel: RefundSumUpViewModel by viewModel()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            SumiNativeTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    RefundSumUpScreen(
                        viewModel = viewModel,
                        onBackClick = { finish() }
                    )
                }
            }
        }
    }
    
    override fun invalidate() {
        // Required by MavericksView but not used in Compose
    }
}
