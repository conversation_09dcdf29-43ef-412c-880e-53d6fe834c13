package com.thedasagroup.suminative.ui.reservations

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Uninitialized
import com.thedasagroup.suminative.data.model.response.reservations.Area
import com.thedasagroup.suminative.data.model.response.reservations.CreateReservationResponse
import com.thedasagroup.suminative.data.model.response.reservations.ReservationsResponse
import com.thedasagroup.suminative.data.model.response.reservations.Table

data class ReservationsState(
    val activeReservationsResponse: Async<ReservationsResponse> = Uninitialized,
    val allReservationsResponse: Async<ReservationsResponse> = Uninitialized,
    val createReservationResponse: Async<CreateReservationResponse> = Uninitialized,
    val editReservationResponse: Async<Boolean> = Uninitialized,
    val cancelReservationResponse: Async<Boolean> = Uninitialized,
    val areasResponse: Async<List<Area>> = Uninitialized,
    val tablesResponse: Async<List<Table>> = Uninitialized,
    val areaTablesMap: Map<Int, Async<List<Table>>> = emptyMap(), // Map of areaId to tables
    val selectedAreaId: Int? = null,
    val selectedAreaTabIndex: Int = 0, // Index for area tabs
    val selectedTabIndex: Int = 0, // Index for reservation tabs (Active/History)
    val isRefreshing: Boolean = false,
    val showEditDialog: Boolean = false,
    val editingReservation: EditReservationData? = null
) : MavericksState

data class EditReservationData(
    val reservationId: Int,
    val customerName: String,
    val customerPhone: String,
    val tableName: String,
    val reservationTime: String,
    val numPeople: Int,
    val reservationStatus: Int = 0, // Default to pending
    val storeId: Int = 158, // Default store ID
    val tableId: Int = 1, // Default table ID
    val customerId: Int = 1 // Default customer ID
)
