package com.thedasagroup.suminative.ui.rewards

import com.airbnb.mvrx.Async
import com.airbnb.mvrx.MavericksState
import com.airbnb.mvrx.Uninitialized
import com.thedasagroup.suminative.data.model.response.rewards.AddPointsResponse
import com.thedasagroup.suminative.data.model.response.rewards.GetAllCustomersResponse
import com.thedasagroup.suminative.data.model.response.rewards.GetCustomersResponse
import com.thedasagroup.suminative.data.model.response.rewards.RewardsCustomer
import com.thedasagroup.suminative.data.model.response.rewards.RewardsOverviewResponse

data class RewardsState(
    val getAllCustomersResponse: Async<GetCustomersResponse> = Uninitialized,
    val addPointsResponse: Async<AddPointsResponse> = Uninitialized,
    val userPointsResponse: Async<Double> = Uninitialized,
    val rewardsOverviewResponse: Async<RewardsOverviewResponse> = Uninitialized,
    val isLoading: Boolean = false,
    val selectedCustomerId: Int? = null,
    val selectedBusinessId: Int? = null,
    // UI state management variables
    val customerIdInput: String = "",
    val showRewardsAvailScreen: Boolean = false,
    val selectedCustomer: RewardsCustomer? = null,
    val showRewardsDialog : Boolean = false,
) : MavericksState
