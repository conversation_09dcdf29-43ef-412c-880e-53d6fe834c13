package com.thedasagroup.suminative.ui.refund

import androidx.fragment.app.FragmentActivity
import com.thedasagroup.suminative.data.model.request.order.Order
import com.thedasagroup.suminative.data.model.response.my_guava.orders.list_of_orders.GuavaOrder
import com.thedasagroup.suminative.data.model.response.store_orders.Order2

/**
 * Helper object to show the Payment dialog
 */
object RefundDialogHelper {
    
    /**
     * Shows the payment dialog for a given order
     * 
     * @param activity The FragmentActivity where the dialog should be shown
     * @param order The order to process payment for, or null for a new order
     * @return The PaymentFragment instance that was shown
     */
    fun showPaymentDialog(guavaOrder: GuavaOrder, activity: FragmentActivity, onPaymentSuccess : (Order2) -> Unit): RefundFragment {
        val paymentFragment = RefundFragment.newInstance(guavaOrder, onPaymentSuccess = onPaymentSuccess)
        paymentFragment.show(
            activity.supportFragmentManager,
            RefundFragment.TAG
        )
        return paymentFragment
    }
    
    /**
     * Dismisses any currently showing payment dialog
     * 
     * @param activity The FragmentActivity where the dialog was shown
     */
    fun dismissPaymentDialog(activity: FragmentActivity) {
        val fragment = activity.supportFragmentManager.findFragmentByTag(RefundFragment.TAG)
        if (fragment is RefundFragment) {
            fragment.dismiss()
        }
    }
} 