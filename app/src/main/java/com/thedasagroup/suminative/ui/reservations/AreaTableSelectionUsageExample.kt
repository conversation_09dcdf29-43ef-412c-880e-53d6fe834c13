package com.thedasagroup.suminative.ui.reservations

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme

/**
 * Example Activity showing how to use the AreaTableSelectionActivity
 * This demonstrates the integration pattern for other activities that need
 * to select areas and tables for reservations.
 */
class AreaTableSelectionUsageExample : ComponentActivity() {

    private var selectedAreaTable by mutableStateOf<AreaTableSelectionHelper.AreaTableSelection?>(null)

    // Activity result launcher for area/table selection
    private val areaTableSelectionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        val selection = AreaTableSelectionHelper.parseResult(result.resultCode, result.data)
        if (selection != null) {
            selectedAreaTable = selection
            // Handle the selected area and table
            handleAreaTableSelection(selection)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            SumiNativeTheme {
                ExampleScreen()
            }
        }
    }

    @Composable
    fun ExampleScreen() {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = "Area & Table Selection Example",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black
            )

            Spacer(modifier = Modifier.height(32.dp))

            // Button to launch area/table selection
            Button(
                onClick = {
                    launchAreaTableSelection(areaTableSelectionLauncher)
                },
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF2E7D32)
                ),
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp)
            ) {
                Text(
                    text = "Select Area & Table",
                    fontSize = 18.sp,
                    color = Color.White
                )
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Display selected area and table
            selectedAreaTable?.let { selection ->
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.Gray.copy(alpha = 0.1f)
                    )
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Selected Area & Table",
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.Black
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        Text(
                            text = "Area: ${selection.areaName} (ID: ${selection.areaId})",
                            fontSize = 16.sp,
                            color = Color.Black
                        )
                        
                        Text(
                            text = "Table: ${selection.tableName} (ID: ${selection.tableId})",
                            fontSize = 16.sp,
                            color = Color.Black
                        )
                        
                        Text(
                            text = "Capacity: ${selection.tableCapacity} seats",
                            fontSize = 16.sp,
                            color = Color.Black
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(24.dp))

            // Example of how to use the selection in a reservation
            selectedAreaTable?.let { selection ->
                Button(
                    onClick = {
                        createReservationWithSelection(selection)
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF1976D2)
                    ),
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(56.dp)
                ) {
                    Text(
                        text = "Create Reservation",
                        fontSize = 18.sp,
                        color = Color.White
                    )
                }
            }
        }
    }

    /**
     * Handle the selected area and table
     */
    private fun handleAreaTableSelection(selection: AreaTableSelectionHelper.AreaTableSelection) {
        // This is where you would typically:
        // 1. Store the selection in your state management
        // 2. Update UI to show the selected area/table
        // 3. Enable next steps in your reservation flow
        
        println("Selected Area: ${selection.areaName} (${selection.areaId})")
        println("Selected Table: ${selection.tableName} (${selection.tableId})")
        println("Table Capacity: ${selection.tableCapacity}")
    }

    /**
     * Example of creating a reservation with the selected area and table
     */
    private fun createReservationWithSelection(selection: AreaTableSelectionHelper.AreaTableSelection) {
        // This is where you would create a reservation using the selected table
        // You can use the ReservationsViewModel and CreateReservationUseCase
        
        // Example:
        // val createRequest = CreateReservationRequest(
        //     storeId = currentStoreId,
        //     tableId = selection.tableId,
        //     customerId = customerId,
        //     guestName = guestName,
        //     guestPhone = guestPhone,
        //     numPeople = numPeople,
        //     reservationStatus = 0,
        //     reservationTime = reservationTime
        // )
        // viewModel.createReservation(createRequest)
        
        println("Creating reservation for table: ${selection.tableName}")
    }
}
