package com.thedasagroup.suminative.ui.reservations

import com.airbnb.mvrx.Async
import com.thedasagroup.suminative.data.model.request.reservations.EditReservationRequest
import com.thedasagroup.suminative.data.repo.ReservationsRepository
import kotlinx.coroutines.flow.StateFlow
import javax.inject.Inject

class EditReservationUseCase @Inject constructor(
    private val reservationsRepository: ReservationsRepository
) {
    suspend operator fun invoke(
        reservationId: Int,
        request: EditReservationRequest
    ): StateFlow<Async<Boolean>> {
        return reservationsRepository.editReservation(reservationId, request)
    }
}
