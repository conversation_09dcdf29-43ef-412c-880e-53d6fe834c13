package com.thedasagroup.suminative.di

import com.airbnb.mvrx.hilt.AssistedViewModelFactory
import com.airbnb.mvrx.hilt.MavericksViewModelComponent
import com.airbnb.mvrx.hilt.ViewModelKey
import com.thedasagroup.suminative.ui.payment.PaymentViewModel
import dagger.Binds
import dagger.Module
import com.thedasagroup.suminative.data.database.DatabaseManager
import com.thedasagroup.suminative.data.repo.ProductRepository
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import dagger.multibindings.IntoMap
import dagger.Provides
import javax.inject.Singleton

@Module
@InstallIn(MavericksViewModelComponent::class)
interface PaymentModule {

    @Binds
    @IntoMap
    @ViewModelKey(PaymentViewModel::class)
    fun paymentViewModelFactory(factory: PaymentViewModel.Factory): AssistedViewModelFactory<*, *>
}