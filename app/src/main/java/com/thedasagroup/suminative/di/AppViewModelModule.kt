package com.thedasagroup.suminative.di

import com.airbnb.mvrx.hilt.AssistedViewModelFactory
import com.airbnb.mvrx.hilt.MavericksViewModelComponent
import com.airbnb.mvrx.hilt.ViewModelKey
import com.thedasagroup.suminative.ui.common.CommonViewModel
import com.thedasagroup.suminative.ui.guava_orders.GuavaOrdersViewModel
import com.thedasagroup.suminative.ui.local_orders.LocalOrdersViewModel
import com.thedasagroup.suminative.ui.login.LoginScreenViewModel
import com.thedasagroup.suminative.ui.orders.OrderScreenViewModel
import com.thedasagroup.suminative.ui.products.DownloadProductsViewModel
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel
import com.thedasagroup.suminative.ui.refund.RefundSumUpViewModel
import com.thedasagroup.suminative.ui.reservations.ReservationsViewModel
import com.thedasagroup.suminative.ui.rewards.RewardsViewModel
import com.thedasagroup.suminative.ui.stock.StockScreenViewModel
import com.thedasagroup.suminative.ui.table_sync.TableSyncViewModel
import com.thedasagroup.suminative.ui.user_profile.SelectUserProfileViewModel
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.multibindings.IntoMap

@Module
@InstallIn(MavericksViewModelComponent::class)
interface AppViewModelModule {
    @Binds
    @IntoMap
    @ViewModelKey(LoginScreenViewModel::class)
    fun loginViewModelFactory(factory: LoginScreenViewModel.Factory): AssistedViewModelFactory<*, *>

    @Binds
    @IntoMap
    @ViewModelKey(OrderScreenViewModel::class)
    fun orderScreenViewModelFactory(factory: OrderScreenViewModel.Factory): AssistedViewModelFactory<*, *>

    @Binds
    @IntoMap
    @ViewModelKey(StockScreenViewModel::class)
    fun stockScreenViewModelFactory(factory: StockScreenViewModel.Factory): AssistedViewModelFactory<*, *>

    @Binds
    @IntoMap
    @ViewModelKey(ProductsScreenViewModel::class)
    fun productsScreenViewModelFactory(factory: ProductsScreenViewModel.Factory): AssistedViewModelFactory<*, *>

    @Binds
    @IntoMap
    @ViewModelKey(DownloadProductsViewModel::class)
    fun downloadProductsViewModelFactory(factory: DownloadProductsViewModel.Factory): AssistedViewModelFactory<*, *>

    @Binds
    @IntoMap
    @ViewModelKey(GuavaOrdersViewModel::class)
    fun guavaOrdersScreenViewModelFactory(factory: GuavaOrdersViewModel.Factory): AssistedViewModelFactory<*, *>

    @Binds
    @IntoMap
    @ViewModelKey(SelectUserProfileViewModel::class)
    fun selectUserProfileViewModelFactory(factory: SelectUserProfileViewModel.Factory): AssistedViewModelFactory<*, *>

    @Binds
    @IntoMap
    @ViewModelKey(LocalOrdersViewModel::class)
    fun localOrdersViewModelFactory(factory: LocalOrdersViewModel.Factory): AssistedViewModelFactory<*, *>

    @Binds
    @IntoMap
    @ViewModelKey(CommonViewModel::class)
    fun commonViewModelFactory(factory: CommonViewModel.Factory): AssistedViewModelFactory<*, *>

    @Binds
    @IntoMap
    @ViewModelKey(ReservationsViewModel::class)
    fun reservationsViewModelFactory(factory: ReservationsViewModel.Factory): AssistedViewModelFactory<*, *>

    @Binds
    @IntoMap
    @ViewModelKey(RefundSumUpViewModel::class)
    fun refundSumUpViewModelFactory(factory: RefundSumUpViewModel.Factory): AssistedViewModelFactory<*, *>

    @Binds
    @IntoMap
    @ViewModelKey(RewardsViewModel::class)
    fun rewardsViewModelFactory(factory: RewardsViewModel.Factory): AssistedViewModelFactory<*, *>

    @Binds
    @IntoMap
    @ViewModelKey(TableSyncViewModel::class)
    fun tableSyncViewModelFactory(factory: TableSyncViewModel.Factory): AssistedViewModelFactory<*, *>
}