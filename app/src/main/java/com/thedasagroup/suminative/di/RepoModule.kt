package com.thedasagroup.suminative.di

import android.content.Context
import android.media.AudioAttributes
import android.media.AudioManager
import android.media.SoundPool
import com.instacart.truetime.time.TrueTimeImpl
import com.thedasagroup.suminative.HourUtils
import com.thedasagroup.suminative.R
import com.thedasagroup.suminative.data.database.DatabaseManager
import com.thedasagroup.suminative.data.database.LocalOrderRepository
import com.thedasagroup.suminative.data.model.response.login.LoginResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.data.repo.ClockInOutRepository
import com.thedasagroup.suminative.data.repo.LoginRepository
import com.thedasagroup.suminative.data.repo.LogsRepository
import com.thedasagroup.suminative.data.repo.MyGuavaRepository
import com.thedasagroup.suminative.data.repo.OrdersRepository
import com.thedasagroup.suminative.data.repo.PrintRepository
import com.thedasagroup.suminative.data.repo.ProductRepository
import com.thedasagroup.suminative.data.repo.ReservationsRepository
import com.thedasagroup.suminative.data.repo.RewardsRepository
import com.thedasagroup.suminative.data.repo.SalesRepository
import com.thedasagroup.suminative.data.repo.StockRepository
import com.thedasagroup.suminative.data.repo.SyncRepository
import com.thedasagroup.suminative.data.repo.WaitersRepository
import com.thedasagroup.suminative.ui.utils.SoundPoolPlayer
import com.thedasagroup.suminative.work.OrderSyncManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class) // Installs FooModule in the generate SingletonComponent.
internal object RepoModule {

    @Singleton
    @Provides
    fun providesSharedPrefs(@ApplicationContext context: Context): Prefs {
        return Prefs(context)
    }

    @Singleton
    @Provides
    fun providesLoginRepository(): LoginRepository {
        return LoginRepository()
    }

    @Singleton
    @Provides
    fun providesOrdersRepository(): OrdersRepository {
        return OrdersRepository()
    }

    @Provides
    @Singleton
    fun providesTrueTime() : TrueTimeImpl {
        return TrueTimeImpl()
    }

    @Provides
    @Singleton
    fun providesSoundPool() : SoundPool {
        val audioAttributes = AudioAttributes.Builder()
            .setUsage(AudioAttributes.USAGE_MEDIA)
            .setContentType(AudioAttributes.CONTENT_TYPE_SONIFICATION)
            .build()

        return SoundPool.Builder()
            .setMaxStreams(1)
            .setAudioAttributes(audioAttributes)
            .build()
    }

    @Provides
    @Singleton
    fun providesSoundPoolPlayer(@ApplicationContext context: Context) : SoundPoolPlayer {
        return SoundPoolPlayer.create(context = context, resId = R.raw.notification)
    }

    @Provides
    @Singleton
    fun providesAudioManager(@ApplicationContext context: Context) : AudioManager {
        return context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    }

    @Provides
    @Singleton
    fun providesStockRepository() : StockRepository {
        return StockRepository()
    }

    @Provides
    @Singleton
    fun providesSalesRepository(trueTimeImpl: TrueTimeImpl) : SalesRepository {
        return SalesRepository(trueTimeImpl = trueTimeImpl)
    }

    @Provides
    @Singleton
    fun providesMyGuavaRepository(trueTimeImpl: TrueTimeImpl,
                                  prefs: Prefs) : MyGuavaRepository {
        return MyGuavaRepository(trueTimeImpl = trueTimeImpl, prefs = prefs)
    }

    @Provides
    @Singleton
    fun providesLogsRepository(prefs: Prefs) : LogsRepository {
        return LogsRepository(prefs = prefs)
    }

    @Provides
    @Singleton
    fun providesWaitersRepository() : WaitersRepository {
        return WaitersRepository()
    }

    @Provides
    @Singleton
    fun getHourUtils(): HourUtils {
        return HourUtils()
    }

    @Provides
    @Singleton
    fun providesDatabaseManager(@ApplicationContext context: Context): DatabaseManager {
        return DatabaseManager(context)
    }

    @Singleton
    @Provides
    fun providesOrderLocalRepository(databaseManager: DatabaseManager): LocalOrderRepository {
        return LocalOrderRepository(databaseManager = databaseManager)
    }

    @Singleton
    @Provides
    fun providesOrderSyncManager(@ApplicationContext context: Context) : OrderSyncManager{
        return OrderSyncManager(context = context)
    }

    @Provides
    @Singleton
    fun providesClockInOutRepository(): ClockInOutRepository {
        return ClockInOutRepository()
    }

    @Provides
    @Singleton
    fun providesReservationsRepository(): ReservationsRepository {
        return ReservationsRepository()
    }

    @Provides
    @Singleton
    fun providesPrintRepository(): PrintRepository {
        return PrintRepository()
    }

    @Provides
    @Singleton
    fun providesRewardsRepository(): RewardsRepository {
        return RewardsRepository()
    }

    @Provides
    @Singleton
    fun providesSyncRepository(prefs: Prefs): SyncRepository {
        return SyncRepository(prefs = prefs)
    }
}

