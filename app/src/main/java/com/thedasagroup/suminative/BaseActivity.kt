package com.thedasagroup.suminative

import android.content.Context
import android.graphics.Bitmap
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Restaurant
import androidx.compose.material.icons.filled.TableRestaurant
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.sunmi.printerx.enums.Align
import com.sunmi.printerx.style.BitmapStyle
import com.thedasagroup.suminative.ui.printer.selectPrinter
import com.thedasagroup.suminative.ui.utils.SunmiPrintHelper
import kotlinx.serialization.Serializable

abstract class BaseActivity : AppCompatActivity() {

}

fun printOrderBitmap(bitmap: Bitmap, context: Context) {
    if (BuildConfig.USES_PRINTER_X.isNotEmpty()) {
        selectPrinter?.lineApi()?.run {
            printBitmap(bitmap, BitmapStyle.getStyle().setAlign(Align.CENTER))
            autoOut()
        }
    } else {
        SunmiPrintHelper.getInstance().initSunmiPrinterService(context)
        SunmiPrintHelper.getInstance().initPrinter()
        SunmiPrintHelper.getInstance().feedPaper()
        SunmiPrintHelper.getInstance().printBitmap(bitmap, 0)
        SunmiPrintHelper.getInstance().feedPaper()
    }
}


data class TopLevelRoute<T : Any>(val name: String, val route: T, val icon: ImageVector)

val topLevelRoutes = listOf(
    TopLevelRoute("Store Items", StoreItems("StoreItems"), Icons.Default.Home),
    TopLevelRoute("Tables", Tables("Tables"), Icons.Default.TableRestaurant),
)
val topLevelRoutesReservations = listOf(
    TopLevelRoute("Store Items", StoreItems("StoreItems"), Icons.Default.Home),
    TopLevelRoute("Tables", Tables("Tables"), Icons.Default.TableRestaurant),
    TopLevelRoute("Reservations", Reservations("Reservations"), Icons.Default.Restaurant)
)

@Serializable
data class StoreItems(val id: String)

@Serializable
data class Tables(val id: String)

@Serializable
data class StockManagement(val id: String)

@Serializable
data class Reservations(val id: String)

@Serializable
data class RegularOrders(val id: String)

val ButtonColorDarkGreen = 0xFF023723
val ButtonColorBGGreen = 0xFF2E7D32

val CATEGORY_GREEN_COLOR = 0xFF2E7D32


@Composable
fun AssignTableDialog(
    isTableOccupied: Boolean,
    onDismiss: () -> Unit,
    onAssignTableClick: () -> Unit,
    onClearCartClick: () -> Unit
) {
    androidx.compose.material3.AlertDialog(
        onDismissRequest = {
            onDismiss()
        },
        title = {
            Text(
                text = "Cart Items Found",
                fontWeight = FontWeight.Bold,
                color = Color(0xFF2E7D32)
            )
        },
        text = {
            Column {
                Text(
                    text = "You have items in your cart. What would you like to do?",
                    color = Color.Black
                )
                Spacer(modifier = Modifier.height(16.dp))

                // Action buttons in a column layout
                Column(
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    // Assign Table button - only show when table is not occupied
                    if (false) {
                        Button(
                            onClick = {
                                onAssignTableClick()
                            },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF2E7D32),
                                contentColor = Color.White
                            ),
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text(
                                text = "Assign Table",
                                fontWeight = FontWeight.Bold
                            )
                        }
                    }

                    // Clear Cart and Continue button
                    Button(
                        onClick = {
                            onClearCartClick()
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFFF6B35),
                            contentColor = Color.White
                        ),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(
                            text = "Clear Cart and Continue",
                            fontWeight = FontWeight.Bold
                        )
                    }

                    // Cancel button
                    OutlinedButton(
                        onClick = {
                            onDismiss()
                        },
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color(0xFF666666)
                        ),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text(
                            text = "Cancel",
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
            }
        },
        confirmButton = { },
        dismissButton = { },
        containerColor = Color.White,
        titleContentColor = Color(0xFF2E7D32),
        textContentColor = Color.Black
    )
}