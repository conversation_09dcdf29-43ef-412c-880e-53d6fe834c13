# Table Occupied Status Implementation

## Overview
This document describes the implementation of table occupied status management in the DasaPOS system. The implementation handles different scenarios based on whether a table is occupied or not, and integrates with the table sync APIs for complete order management.

## Table Status Logic

### Table Response Structure
When clicking the "Add Table" button, the system receives a response with table information including the occupied status:

```json
[
    {
        "id": 24,
        "storeId": 158,
        "areaId": 6,
        "tableName": "Table 2",
        "seatingCapacity": 5,
        "tableDetailsJson": "{\"shape\":\"RECTANGLE\",\"color\":\"#40e7dc\",\"position\":{\"row\":1,\"col\":1}}",
        "occupied": false,
        "reserved": false,
        "netPayable": null
    }
]
```

### Visual Indicators
- **Occupied Tables (occupied=true)**: Displayed with red color and netPayable amount shown on the table
- **Available Tables (occupied=false)**: Displayed with normal colors and cart total (if any)

## Implementation Flow

### A) Occupied Table Selection (occupied=true)
When an occupied table is clicked:

1. **Call Get Table Order API** - Retrieves existing order details
2. **Load Order Data** - Populates the cart with existing items and course assignments
3. **Show Ordering Page** - Opens with cart displaying current order
4. **Enable Modifications** - User can modify the existing order

**Implementation**: `handleOccupiedTableSelection()` method in ProductsScreenViewModel

### B) Unoccupied Table Selection (occupied=false)
When an unoccupied table is clicked:

1. **Call toggleOccupied API** - Sets table as occupied with netPayable=0
2. **Show Ordering Page** - Opens with empty cart ready for new orders
3. **Enable Ordering** - User can start adding items to cart

**Implementation**: `handleUnoccupiedTableSelection()` method in ProductsScreenViewModel

## Order Management Workflow

### 1. First Item Added to Cart
When the first item is added to an empty cart:
- **Call Save Table Orders API** - Creates initial order record
- **Implementation**: `saveTableOrderIfNeeded()` method

### 2. Subsequent Cart Changes
When cart is modified after initial save:
- **Call Update Table Orders API** - Updates existing order with changes
- **Implementation**: `saveTableOrderIfNeeded()` method (handles both scenarios)

### 3. Payment Completion
When payment is successfully processed:
- **Call Delete Table Order API** - Removes the order record
- **Call toggleOccupied API** - Sets table as unoccupied with netPayable=0
- **Clear Local Data** - Removes table from selected tables and clears cart
- **Implementation**: `handlePaymentCompletion()` method

## Key Components Modified

### 1. Table Model Enhancement
```kotlin
@Serializable
data class Table(
    // ... existing fields ...
    @SerialName("netPayable")
    val netPayable: Double? = null  // Added netPayable field
)
```

### 2. TableCard Visual Updates
- **Mobile Version**: Shows netPayable for occupied tables, cart total for others
- **Tablet Version**: Shows netPayable for occupied tables, cart total for others
- **Clickable**: All tables (occupied and unoccupied) are now clickable

### 3. ProductsScreenViewModel Enhancements
- **Table Selection Logic**: `handleTableSelectionWithOccupiedStatus()`
- **Order Synchronization**: `saveTableOrderIfNeeded()`
- **Payment Completion**: `handlePaymentCompletion()`
- **Data Conversion**: Methods to convert between internal and API formats

### 4. API Integration
- **Get Synced Order**: Retrieves existing order for occupied tables
- **Toggle Occupied**: Changes table occupied status
- **Sync Order**: Creates new order when first item added
- **Update Order**: Updates existing order when cart changes
- **Delete Order**: Removes order when payment completed

## Data Flow

### Order Creation Flow
```
Unoccupied Table Click → toggleOccupied(netPayable=0) → Show Empty Cart → 
First Item Added → syncOrderToTable() → Subsequent Changes → updateOrderForTable()
```

### Order Retrieval Flow
```
Occupied Table Click → getSyncedOrderForTable() → Load Existing Data → 
Show Populated Cart → Changes → updateOrderForTable()
```

### Payment Completion Flow
```
Payment Success → deleteOrderForTable() → toggleOccupied(netPayable=0) → 
Clear Local Data → Table Available for New Orders
```

## Error Handling

### API Failures
- **Graceful Degradation**: If API calls fail, the UI still functions
- **Local State Management**: Cart and table selection work independently
- **Retry Logic**: Built into the existing repository patterns

### Data Consistency
- **State Synchronization**: Local state is updated regardless of API success
- **Fallback Behavior**: System continues to work even with network issues
- **User Experience**: No blocking operations that prevent normal POS functionality

## Testing Scenarios

### 1. Occupied Table Workflow
1. Click on occupied table (red with netPayable amount)
2. Verify existing order loads in cart
3. Modify order and verify updates are synced
4. Complete payment and verify table becomes unoccupied

### 2. Unoccupied Table Workflow
1. Click on unoccupied table
2. Verify empty cart opens
3. Add first item and verify order is created
4. Add more items and verify updates are synced
5. Complete payment and verify cleanup

### 3. Error Scenarios
1. Network failures during API calls
2. Invalid table data
3. Concurrent table access
4. Payment failures

## Configuration

### API Endpoints Used
- `GET /api/tables/sync/order/{tableId}` - Get existing order
- `POST /api/tables/toggleOccupied` - Toggle occupied status
- `POST /api/tables/sync/order` - Create new order
- `PUT /api/tables/sync/order/{tableId}` - Update existing order
- `DELETE /api/tables/sync/order/{tableId}` - Delete order

### Dependencies
- Table Sync Use Cases (all 5 operations)
- Existing ProductsScreenViewModel
- Table display components (mobile and tablet)
- Payment completion handlers

## Benefits

1. **Complete Order Management**: Full CRUD operations for table orders
2. **Visual Clarity**: Clear indication of table status and amounts
3. **Seamless Integration**: Works with existing POS workflow
4. **Error Resilience**: Graceful handling of network issues
5. **Consistent State**: Local and server state synchronization
6. **User Experience**: Intuitive table selection and order management

The implementation provides a complete table management system that handles all aspects of table-based ordering from selection through payment completion.
