# Bill <PERSON> and Go Sequence Implementation

## Overview
This document outlines the implementation of moving the Bill button to the top tab row and implementing sequential Go button functionality where only one course shows the Go button at a time.

## Changes Made

### 1. <PERSON> Relocation

#### From OrderSummarySection to Top Tab Row
- **Previous Location**: Bottom of Order tab in OrderSummarySection
- **New Location**: Top row alongside Order and Pay tabs
- **Benefits**: More prominent placement, always visible regardless of tab selection

#### Implementation Details
- **File**: `app/src/main/java/com/thedasagroup/suminative/ui/products/cart/CartScreenFigma.kt`
- **Function**: `MainCartTabs()` - Updated to include Bill button
- **Layout**: Row with Order tab, Pay tab, and Bill button
- **Styling**: Consistent green theme (#2E7D32), money icon, "BILL" text

### 2. Sequential Go Button System

#### Concept
- Only one course shows the "Go" button at any given time
- After clicking "Go" on a course, the button moves to the next course in sequence
- Sequence: Starters → Mains → Desserts → (cycles back to Starters)

#### State Management
Added to `ProductsScreenState`:
```kotlin
val currentActiveCourse: String? = null // Track which course has the active Go button
```

#### Core Functions
- `initializeActiveCourse()` - Sets first course as active
- `moveToNextCourse(courseId)` - Moves Go button to next course
- `shouldShowGoButton(courseId)` - Determines if course should show Go button

## Implementation Details

### 1. MainCartTabs Enhancement

#### Updated Function Signature
```kotlin
@Composable
private fun MainCartTabs(
    selectedTab: CartTab,
    onTabSelected: (CartTab) -> Unit,
    onBillClick: () -> Unit = {}
)
```

#### Layout Structure
```
[Order Tab] [Pay Tab] [BILL Button]
    1f        1f        0.8f
```

#### Bill Button Styling
- **Background**: Green (#2E7D32)
- **Icon**: Money icon (20dp)
- **Text**: "BILL" (14sp, bold)
- **Shape**: Rounded corners (8dp)
- **Weight**: 0.8f (slightly smaller than tabs)

### 2. Sequential Go Button Logic

#### Course Status Display Logic
```kotlin
when (status) {
    CourseStatus.GO -> {
        if (shouldShowGoButton(courseId)) {
            // Show clickable Go button
        } else {
            // Show "Waiting" text
        }
    }
    CourseStatus.PREPARING -> // Show "Preparing" text
    CourseStatus.COMPLETE -> // Show "Complete" text
}
```

#### Go Button Flow
1. **Initialization**: First course (Starters) gets Go button
2. **Click Go**: Course status → PREPARING, Go button moves to next course
3. **API Success**: Go button stays on next course
4. **API Failure**: Go button reverts to original course
5. **Sequence**: Starters → Mains → Desserts → Starters (cycles)

### 3. ViewModel Functions

#### Active Course Management
```kotlin
fun initializeActiveCourse() // Set first course as active
fun moveToNextCourse(currentCourseId: String) // Move to next in sequence
fun shouldShowGoButton(courseId: String): Boolean // Check if course should show Go
```

#### Course Sequence Logic
```kotlin
val currentIndex = availableCourses.indexOfFirst { it.id == currentCourseId }
val nextCourse = if (currentIndex < availableCourses.size - 1) {
    availableCourses[currentIndex + 1].id
} else {
    availableCourses.firstOrNull()?.id // Cycle back to first
}
```

## User Experience

### 1. Bill Button
- **Always Visible**: Available in top row regardless of selected tab
- **Consistent Action**: Sends notifications for all courses with items
- **Visual Prominence**: Green button with money icon stands out

### 2. Sequential Go System
- **Clear Workflow**: Only one course active at a time prevents confusion
- **Visual Feedback**: 
  - Active course: Green "Go" button
  - Waiting courses: Gray "Waiting" text
  - Processing courses: Orange "Preparing" text
  - Completed courses: Green "Complete" text

### 3. Kitchen Workflow
- **Ordered Preparation**: Ensures courses are sent to kitchen in sequence
- **Prevents Overlap**: Can't accidentally send multiple courses simultaneously
- **Clear Status**: Kitchen knows which course is currently being prepared

## Benefits

### 1. Improved UX
- **Bill Button**: More accessible, always visible
- **Sequential Flow**: Clearer workflow, prevents errors
- **Visual Clarity**: Obvious which course is next

### 2. Kitchen Efficiency
- **Ordered Notifications**: Courses sent in logical sequence
- **Reduced Confusion**: Only one course active at a time
- **Better Timing**: Prevents all courses being sent simultaneously

### 3. Error Prevention
- **Single Active Course**: Can't accidentally trigger multiple courses
- **Clear Status**: Visual indicators prevent confusion
- **Automatic Progression**: System manages sequence automatically

## Technical Implementation

### 1. State Updates
```kotlin
// Initialize first course as active
LaunchedEffect(Unit) {
    productsScreenViewModel.initializeActiveCourse()
}
```

### 2. Go Button Click Handler
```kotlin
onGoButtonClick = { courseId ->
    // This will:
    // 1. Set course status to PREPARING
    // 2. Send API notification
    // 3. Move Go button to next course on success
    onGoButtonClick(courseId)
}
```

### 3. Course Status Check
```kotlin
val shouldShowGo = productsScreenViewModel.shouldShowGoButton(courseId)
if (shouldShowGo) {
    // Show Go button
} else {
    // Show "Waiting" text
}
```

## Testing

### 1. Unit Tests
- Course sequence logic
- Active course tracking
- Status transitions
- Go button visibility logic

### 2. Manual Testing Scenarios
1. **Bill Button**: Click from different tabs, verify functionality
2. **Go Sequence**: Click Go on Starters → verify moves to Mains
3. **Cycle**: Complete all courses → verify cycles back to Starters
4. **Error Handling**: API failure → verify Go button stays on original course
5. **Visual States**: Verify correct text/button display for each status

## Files Modified

### Core Implementation
- `CartScreenFigma.kt` - Bill button relocation, Go button logic
- `ProductsScreenViewModel.kt` - Sequential course management
- `CourseStatusTest.kt` - Updated tests

### Key Changes
1. **MainCartTabs()** - Added Bill button
2. **CourseHeader()** - Sequential Go button logic
3. **ProductsScreenState** - Added currentActiveCourse tracking
4. **ViewModel Functions** - Course sequence management

## Future Enhancements

### Potential Improvements
1. **Manual Override**: Allow staff to manually select active course
2. **Course Timing**: Track time between course notifications
3. **Kitchen Feedback**: Allow kitchen to control course progression
4. **Custom Sequences**: Configure different course orders per restaurant
5. **Batch Mode**: Option to send all courses at once if needed

The implementation provides a streamlined, intuitive workflow that improves both staff efficiency and kitchen operations while maintaining clear visual feedback throughout the process.
