# Download Products Screen Integration Guide

## Overview
This feature allows you to download products from the API and save them to the local database. The screen shows only when there are no products in the database after selecting a store.

## Files Created

### 1. Database Table: `Product.sq`
- **Location**: `app/src/main/sqldelight/com/thedasagroup/suminative/database/Product.sq`
- **Purpose**: SQLDelight schema for storing products locally
- **Features**: Complete product entity with all fields from StockItem model

### 2. Repository: `ProductRepository.kt`
- **Location**: `app/src/main/java/com/thedasagroup/suminative/data/repo/ProductRepository.kt`
- **Purpose**: Handle database operations for products
- **Key Methods**:
  - `insertProduct()` - Insert single product
  - `insertMultipleProducts()` - Bulk insert products
  - `getProductsByStore()` - Get products by store ID
  - `updateProductStock()` - Update product stock status

### 3. Use Case: `DownloadProductsUseCase.kt`
- **Location**: `app/src/main/java/com/thedasagroup/suminative/ui/products/DownloadProductsUseCase.kt`
- **Purpose**: Handle business logic for downloading and saving products
- **Methods**:
  - `invoke()` - Download products from API and save to database
  - `checkExistingProducts()` - Check if products exist in database

### 4. ViewModel: `DownloadProductsViewModel.kt`
- **Location**: `app/src/main/java/com/thedasagroup/suminative/ui/products/DownloadProductsViewModel.kt`
- **Purpose**: Manage UI state and coordinate download operations

### 5. Screen: `DownloadProductsScreen.kt`
- **Location**: `app/src/main/java/com/thedasagroup/suminative/ui/products/DownloadProductsScreen.kt`
- **Purpose**: UI composable for the download products screen

## Integration Steps

### Step 1: Build the Project
```bash
./gradlew build
```
This will generate the SQLDelight classes needed by ProductRepository.

### Step 2: Add to DI Modules
Add the following to your existing DI modules:

```kotlin
// In AppUseCaseModule.kt
@Provides
fun provideDownloadProductsUseCase(
    stockRepository: StockRepository,
    productRepository: ProductRepository,
    prefs: Prefs
): DownloadProductsUseCase = DownloadProductsUseCase(stockRepository, productRepository, prefs)

// In PaymentModule.kt or create a new module
@Provides
@Singleton
fun provideProductRepository(databaseManager: DatabaseManager): ProductRepository = 
    ProductRepository(databaseManager)
```

### Step 3: Check for Products After Store Selection
In your store selection logic, add a check for existing products:

```kotlin
// After store is selected
val storeId = selectedStore.id ?: 0
val productRepository = // inject ProductRepository
val existingProducts = productRepository.getProductsByStore(storeId)

if (existingProducts.isEmpty()) {
    // Navigate to DownloadProductsScreen
    navigateToDownloadProducts()
} else {
    // Navigate to main screen
    navigateToMainScreen()
}
```

### Step 4: Use the Screen
```kotlin
@Composable
fun YourComposable() {
    val viewModel: DownloadProductsViewModel = mavericksActivityViewModel()
    
    DownloadProductsScreen(
        viewModel = viewModel,
        onDownloadComplete = {
            // Navigate to main screen after successful download
        },
        onSkipDownload = {
            // Optional: Allow skipping download
        }
    )
}
```

## Features

### ✅ API Integration
- Uses existing `GET_STOCK_ITEMS` endpoint
- Fetches all products for the selected store
- Follows existing repository patterns

### ✅ Database Storage
- Saves products locally for offline access
- Supports bulk operations for performance
- Maintains sync status for future updates

### ✅ UI States
- **Loading**: Shows progress indicator during download
- **Success**: Shows product count and last update time
- **Error**: Shows error messages with retry option
- **Existing Products**: Shows continue and refresh options

### ✅ Refresh Functionality
- Refresh button to update products from server
- Maintains existing products while updating
- Shows last update timestamp

## Usage Flow

1. **First Time**: User selects store → Check database → No products found → Show download screen
2. **Download**: User taps download → API call → Save to database → Navigate to main screen
3. **Subsequent Uses**: Products exist → Skip download screen → Go directly to main screen
4. **Refresh**: User can manually refresh products using the refresh button

## Error Handling

- Network connectivity issues
- API response validation
- Database save failures
- User-friendly error messages
- Retry functionality

## Performance Considerations

- Bulk database operations for better performance
- Background processing for API calls
- Efficient database queries with proper indexing
- Memory-conscious handling of large product lists

## Future Enhancements

- **Incremental Sync**: Only download changed products
- **Background Sync**: Periodic updates in background
- **Offline Support**: Enhanced offline functionality
- **Category Filtering**: Download specific categories only 