[{"discount": 0.0, "extraPrice": 0.0, "isB1G1": false, "netPayable": 3.9, "optionPrice": 0.0, "price": 3.9, "quantity": 1, "storeItem": {"additionalInfo": "", "billAmount": 3.9, "brandId": 79, "businessId": 104, "categoryId": 489, "createdBy": 214, "createdOn": "2025-04-14T11:28:15Z", "dailyCapacity": 100, "description": "Sun-kissed Sicilian olives with a bold, briny flavor and tender, meaty bite, capturing the essence of the Mediterranean in every savory morsel.", "discountType": 2, "discountedAmount": 0.0, "discounttypename": "Flat", "extras": [], "id": 3925, "ingredients": "", "modifiedBy": -1, "modifiedOn": "", "name": "Sicilian Olives", "optionSets": [], "pic": "174463009526091610.png", "preparationTime": "", "price": 3.9, "quantity": 1, "servingSize": "", "storeId": 158, "tax": 0.0, "unitId": -1, "unitName": "Select Unit", "vat": true, "uuid": "329578d8-6c6c-454a-8fc6-99e20477a42a", "courseId": "Starters"}, "tax": 0.0, "uuid": "da5cc92f-96be-417f-9b05-edbb6c6bebab", "sentToKitchen": false}, {"discount": 0.0, "extraPrice": 0.0, "isB1G1": false, "netPayable": 3.9, "optionPrice": 0.0, "price": 3.9, "quantity": 1, "storeItem": {"additionalInfo": "", "billAmount": 3.9, "brandId": 79, "businessId": 104, "categoryId": 489, "createdBy": 214, "createdOn": "2025-04-14T11:33:14Z", "dailyCapacity": 100, "description": "Bread Basket", "discountType": 2, "discountedAmount": 0.0, "discounttypename": "Flat", "extras": [], "id": 3926, "ingredients": "", "modifiedBy": -1, "modifiedOn": "", "name": "Bread Basket", "optionSets": [], "pic": "174463039369824252.png", "preparationTime": "", "price": 3.9, "quantity": 1, "servingSize": "", "storeId": 158, "tax": 0.0, "unitId": -1, "unitName": "Select Unit", "vat": true, "uuid": "41534afb-c2a8-465c-8c03-dc7ba23e0ab3", "courseId": "Starters"}, "tax": 0.0, "uuid": "12a3e038-f36f-46fb-9def-c8770747ed0d", "sentToKitchen": false}]