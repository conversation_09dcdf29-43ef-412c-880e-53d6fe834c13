# Cart Close and Table Unselect Implementation

## Overview
This document describes the implementation of cart closing behavior and table unselection in the POS system.

## Requirements Implemented

### 1. Add Table Should Close Cart
When a table is added or selected, the cart should automatically close to provide a cleaner user experience.

### 2. Close Cart Circular Button Should Close Cart
The circular close button in the cart should properly close the cart using the appropriate callback.

### 3. Unselect Current Table When Cart is Closed
When the cart is closed (via close button or any other method), the currently selected table should be unselected, causing the system to fall back to global cart behavior.

## Changes Made

### Modified Files

#### 1. `ProductsScreenViewModel.kt`

**`updateCartVisibility()` method - Lines 1467-1479:**
```kotlin
// Before
fun updateCartVisibility(visible: <PERSON><PERSON><PERSON>) {
    setState {
        copy(showCart = visible)
    }
}

// After
fun updateCartVisibility(visible: <PERSON><PERSON>an) {
    setState {
        if (!visible) {
            // When closing cart, unselect current table
            copy(
                showCart = visible,
                selectedTableIndex = -1 // Unselect current table
            )
        } else {
            copy(showCart = visible)
        }
    }
}
```

**`addSelectedTable()` method - Lines 2305-2325:**
```kotlin
// Before
showCart = true // Always show cart when table is added, even if empty

// After  
showCart = false // Close cart when table is added
```

**`assignGlobalCartToTable()` method - Line 3137:**
```kotlin
// Before
showCart = true // Show cart after assignment

// After
showCart = false // Close cart after assignment
```

#### 2. `CartScreenFigma.kt`

**`MainCartTabs()` function signature - Lines 332-338:**
```kotlin
// Added closeCart parameter for semantic clarity
@Composable
private fun MainCartTabs(
    selectedTab: CartTab,
    onTabSelected: (CartTab) -> Unit,
    onCrossClick: () -> Unit,
    closeCart: () -> Unit // Added parameter
)
```

**Close button click handler - Lines 346-349:**
```kotlin
// Before
onClick = { onCrossClick() }

// After
onClick = { closeCart() } // Use semantic callback
```

## User Experience Impact

### Before Changes
1. **Add Table**: Cart would remain open after adding a table
2. **Close Button**: Worked but used less semantic callback
3. **Table Selection**: Tables remained selected when cart was closed

### After Changes
1. **Add Table**: Cart automatically closes when a table is added/selected
2. **Close Button**: Uses proper `closeCart` callback for better semantics
3. **Table Unselection**: When cart is closed, current table is unselected (selectedTableIndex = -1)

## Technical Details

### Table Unselection Logic
When `updateCartVisibility(visible = false)` is called:
1. Cart is closed (`showCart = false`)
2. Current table is unselected (`selectedTableIndex = -1`)
3. `getCurrentTableId()` returns `null` due to invalid index
4. System falls back to global cart behavior

### Global Cart Fallback
When no table is selected (`selectedTableIndex = -1`):
- `getCurrentTableId()` returns `null`
- All cart operations use global cart instead of table-specific cart
- Items are added to `order.carts` instead of `tableOrders[tableId].carts`
- Course management uses global courses instead of table-specific courses

### UI Behavior
- Table tabs remain visible but none appear selected
- `ScrollableTabRow` handles negative `selectedTabIndex` gracefully
- Users can still select tables manually if needed
- Add Table button remains functional

## Benefits

1. **Cleaner Workflow**: Cart doesn't obstruct view after table operations
2. **Intuitive Behavior**: Closing cart returns to "no table selected" state
3. **Flexible Usage**: Users can work with global cart or select tables as needed
4. **Consistent State**: Cart visibility and table selection are properly synchronized
5. **Better UX**: Less visual clutter and more predictable behavior

## Testing

Comprehensive tests in `CartCloseTableUnselectTest.kt` covering:
- Table unselection when cart is closed
- `getCurrentTableId()` returning null for unselected tables
- Cart opening not affecting table selection
- Multiple tables with unselection behavior
- Global cart fallback behavior

## Edge Cases Handled

1. **Multiple Tables**: When multiple tables exist, closing cart unselects current table but keeps all tables available
2. **Empty Tables List**: Works correctly when no tables are selected
3. **Invalid Index**: `selectedTableIndex = -1` is handled gracefully by all related methods
4. **Global Cart Items**: Existing global cart items remain accessible when tables are unselected

## Backward Compatibility

All changes are backward compatible:
- Existing table selection functionality unchanged
- All existing callbacks and methods work as before
- Only cart visibility and table selection synchronization is modified
- No breaking changes to the API
