{
    "tableId": 97,
    "customerId": 0,
    "businessId": 0,
    "netPayable": 7.8,
    "orderCourses": [
        {
            "coursesName": "",
            "cartJson": "[\n    {\n        \"discount\": 0.0,\n        \"extraPrice\": 0.0,\n        \"isB1G1\": false,\n        \"netPayable\": 3.9,\n        \"optionPrice\": 0.0,\n        \"price\": 3.9,\n        \"quantity\": 1,\n        \"storeItem\": {\n            \"additionalInfo\": \"\",\n            \"billAmount\": 3.9,\n            \"brandId\": 79,\n            \"businessId\": 104,\n            \"categoryId\": 489,\n            \"createdBy\": 214,\n            \"createdOn\": \"2025-04-14T11:28:15Z\",\n            \"dailyCapacity\": 100,\n            \"description\": \"Sun-kissed Sicilian olives with a bold, briny flavor and tender, meaty bite, capturing the essence of the Mediterranean in every savory morsel.\",\n            \"discountType\": 2,\n            \"discountedAmount\": 0.0,\n            \"discounttypename\": \"Flat\",\n            \"extras\": [],\n            \"id\": 3925,\n            \"ingredients\": \"\",\n            \"modifiedBy\": -1,\n            \"modifiedOn\": \"\",\n            \"name\": \"Sicilian Olives\",\n            \"optionSets\": [],\n            \"pic\": \"174463009526091610.png\",\n            \"preparationTime\": \"\",\n            \"price\": 3.9,\n            \"quantity\": 1,\n            \"servingSize\": \"\",\n            \"storeId\": 158,\n            \"tax\": 0.0,\n            \"unitId\": -1,\n            \"unitName\": \"Select Unit\",\n            \"vat\": true,\n            \"uuid\": \"8eb8d964-c160-48b0-9403-00ed6f50f0d6\",\n            \"courseId\": \"\"\n        },\n        \"tax\": 0.0,\n        \"uuid\": \"e0910dea-a02d-48c4-b033-079abce09436\",\n        \"sentToKitchen\": false\n    }\n]",
            "courseSortOrder": 0
        },
        {
            "coursesName": "Starters",
            "cartJson": "[\n    {\n        \"discount\": 0.0,\n        \"extraPrice\": 0.0,\n        \"isB1G1\": false,\n        \"netPayable\": 3.9,\n        \"optionPrice\": 0.0,\n        \"price\": 3.9,\n        \"quantity\": 1,\n        \"storeItem\": {\n            \"additionalInfo\": \"\",\n            \"billAmount\": 3.9,\n            \"brandId\": 79,\n            \"businessId\": 104,\n            \"categoryId\": 489,\n            \"createdBy\": 214,\n            \"createdOn\": \"2025-04-14T11:33:14Z\",\n            \"dailyCapacity\": 100,\n            \"description\": \"Bread Basket\",\n            \"discountType\": 2,\n            \"discountedAmount\": 0.0,\n            \"discounttypename\": \"Flat\",\n            \"extras\": [],\n            \"id\": 3926,\n            \"ingredients\": \"\",\n            \"modifiedBy\": -1,\n            \"modifiedOn\": \"\",\n            \"name\": \"Bread Basket\",\n            \"optionSets\": [],\n            \"pic\": \"174463039369824252.png\",\n            \"preparationTime\": \"\",\n            \"price\": 3.9,\n            \"quantity\": 1,\n            \"servingSize\": \"\",\n            \"storeId\": 158,\n            \"tax\": 0.0,\n            \"unitId\": -1,\n            \"unitName\": \"Select Unit\",\n            \"vat\": true,\n            \"uuid\": \"ea7606c9-c073-442e-9fa6-b548e5472123\",\n            \"courseId\": \"Starters\"\n        },\n        \"tax\": 0.0,\n        \"uuid\": \"ee1d9807-a83e-40cb-be76-b1876150f79c\",\n        \"sentToKitchen\": false\n    }\n]",
            "courseSortOrder": 1
        }
    ],
    "goQueue": "Starters",
    "preparingQueue": "",
    "completeQueue": ""
}

{
    "success": true,
    "message": "Order updated for table 97",
    "data": {
        "id": 1316,
        "tableId": 97,
        "orderCourses": [
            {
                "id": 4440,
                "coursesName": "",
                "courseStatus": 0,
                "courseSortOrder": 0,
                "cartJson": "[\n    {\n        \"discount\": 0.0,\n        \"extraPrice\": 0.0,\n        \"isB1G1\": false,\n        \"netPayable\": 3.9,\n        \"optionPrice\": 0.0,\n        \"price\": 3.9,\n        \"quantity\": 1,\n        \"storeItem\": {\n            \"additionalInfo\": \"\",\n            \"billAmount\": 3.9,\n            \"brandId\": 79,\n            \"businessId\": 104,\n            \"categoryId\": 489,\n            \"createdBy\": 214,\n            \"createdOn\": \"2025-04-14T11:28:15Z\",\n            \"dailyCapacity\": 100,\n            \"description\": \"Sun-kissed Sicilian olives with a bold, briny flavor and tender, meaty bite, capturing the essence of the Mediterranean in every savory morsel.\",\n            \"discountType\": 2,\n            \"discountedAmount\": 0.0,\n            \"discounttypename\": \"Flat\",\n            \"extras\": [],\n            \"id\": 3925,\n            \"ingredients\": \"\",\n            \"modifiedBy\": -1,\n            \"modifiedOn\": \"\",\n            \"name\": \"Sicilian Olives\",\n            \"optionSets\": [],\n            \"pic\": \"174463009526091610.png\",\n            \"preparationTime\": \"\",\n            \"price\": 3.9,\n            \"quantity\": 1,\n            \"servingSize\": \"\",\n            \"storeId\": 158,\n            \"tax\": 0.0,\n            \"unitId\": -1,\n            \"unitName\": \"Select Unit\",\n            \"vat\": true,\n            \"uuid\": \"8eb8d964-c160-48b0-9403-00ed6f50f0d6\",\n            \"courseId\": \"\"\n        },\n        \"tax\": 0.0,\n        \"uuid\": \"e0910dea-a02d-48c4-b033-079abce09436\",\n        \"sentToKitchen\": false\n    }\n]"
            },
            {
                "id": 4441,
                "coursesName": "Starters",
                "courseStatus": 0,
                "courseSortOrder": 1,
                "cartJson": "[\n    {\n        \"discount\": 0.0,\n        \"extraPrice\": 0.0,\n        \"isB1G1\": false,\n        \"netPayable\": 3.9,\n        \"optionPrice\": 0.0,\n        \"price\": 3.9,\n        \"quantity\": 1,\n        \"storeItem\": {\n            \"additionalInfo\": \"\",\n            \"billAmount\": 3.9,\n            \"brandId\": 79,\n            \"businessId\": 104,\n            \"categoryId\": 489,\n            \"createdBy\": 214,\n            \"createdOn\": \"2025-04-14T11:33:14Z\",\n            \"dailyCapacity\": 100,\n            \"description\": \"Bread Basket\",\n            \"discountType\": 2,\n            \"discountedAmount\": 0.0,\n            \"discounttypename\": \"Flat\",\n            \"extras\": [],\n            \"id\": 3926,\n            \"ingredients\": \"\",\n            \"modifiedBy\": -1,\n            \"modifiedOn\": \"\",\n            \"name\": \"Bread Basket\",\n            \"optionSets\": [],\n            \"pic\": \"174463039369824252.png\",\n            \"preparationTime\": \"\",\n            \"price\": 3.9,\n            \"quantity\": 1,\n            \"servingSize\": \"\",\n            \"storeId\": 158,\n            \"tax\": 0.0,\n            \"unitId\": -1,\n            \"unitName\": \"Select Unit\",\n            \"vat\": true,\n            \"uuid\": \"ea7606c9-c073-442e-9fa6-b548e5472123\",\n            \"courseId\": \"Starters\"\n        },\n        \"tax\": 0.0,\n        \"uuid\": \"ee1d9807-a83e-40cb-be76-b1876150f79c\",\n        \"sentToKitchen\": false\n    }\n]"
            }
        ],
        "netPayable": 7.8,
        "customerId": 0,
        "businessId": 0,
        "goQueue": "",
        "preparingQueue": "",
        "completeQueue": ""
    },
    "statusCode": 200
}

#DEDEF5
#D4D4F2