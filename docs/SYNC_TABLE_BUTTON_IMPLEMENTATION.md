# Sync Table Button Implementation

## Overview
This document describes the implementation of a "Sync Table" button in the cart that syncs the current table using the `GetSyncedOrderForTableUseCase`. The button is added to the Pay tab of the cart screen and allows users to manually refresh table data from the server.

## Implementation Details

### Button Location
The Sync Table button is placed in the **Pay tab** of the cart screen, positioned above the payment buttons (Card, Cash, Split Bill). This location provides easy access while maintaining the payment workflow.

### Visual Design
- **Color**: Blue background (`Color(0xFF1976D2)`) to distinguish from payment buttons
- **Icon**: Sync icon (`Icons.Default.Sync`) for clear visual indication
- **Text**: "Sync Table" label
- **Size**: Full width button with 48dp height
- **Shape**: Rounded corners (8dp radius)

### Function Signature Updates

#### CartScreenFigma.kt
```kotlin
fun CartScreenFigma(
    // ... existing parameters
    onSyncTable: () -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
)

fun MobileCartScreen(
    // ... existing parameters  
    onSyncTable: () -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
)
```

#### PayTab.kt
```kotlin
fun PayTabContent(
    // ... existing parameters
    onSyncTable: () -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
)

private fun PayTabPaymentSection(
    // ... existing parameters
    onSyncTable: () -> Unit,
    productsScreenViewModel: ProductsScreenViewModel,
    modifier: Modifier = Modifier
)
```

### Button Implementation
```kotlin
// Sync Table Button
Button(
    onClick = onSyncTable,
    modifier = Modifier
        .fillMaxWidth()
        .height(48.dp),
    colors = ButtonDefaults.buttonColors(
        containerColor = Color(0xFF1976D2),
        contentColor = Color.White
    ),
    shape = RoundedCornerShape(8.dp)
) {
    Icon(
        imageVector = Icons.Default.Sync,
        contentDescription = null,
        modifier = Modifier.size(18.dp)
    )
    Spacer(modifier = Modifier.width(8.dp))
    Text(
        text = "Sync Table",
        fontWeight = FontWeight.Bold,
        fontSize = 14.sp,
        fontFamily = fontPoppins
    )
}
```

## Integration Points

### 1. Parameter Threading
The `onSyncTable` callback is threaded through the component hierarchy:
- `CartScreenFigma` → `MobileCartScreen` → `PayTabContent` → `PayTabPaymentSection`

### 2. Tab-Specific Placement
The button only appears in the **Pay tab**, ensuring it doesn't interfere with the ordering workflow in the Order tab.

### 3. Layout Integration
- Positioned above payment buttons with proper spacing
- Maintains existing payment button layout
- Uses consistent styling with other cart buttons

## Usage Flow

### User Interaction
1. User opens cart
2. Navigates to Pay tab
3. Clicks "Sync Table" button
4. System calls `onSyncTable()` callback
5. Parent component handles sync logic using `GetSyncedOrderForTableUseCase`

### Expected Behavior
When the sync button is clicked, the parent component should:
1. Call `GetSyncedOrderForTableUseCase` with current table ID
2. Retrieve latest order data from server
3. Update local state with synced data
4. Refresh cart display with updated information

## Benefits

### 1. Manual Sync Control
- Users can manually refresh table data when needed
- Useful for resolving sync issues or getting latest updates
- Provides control over when sync operations occur

### 2. Clear Visual Indication
- Blue color distinguishes from payment actions
- Sync icon provides intuitive visual cue
- Positioned logically before payment actions

### 3. Non-Intrusive Design
- Only appears in Pay tab to avoid workflow disruption
- Maintains existing payment button layout
- Consistent with overall cart design language

### 4. Flexible Integration
- Callback-based design allows parent to handle sync logic
- Can be easily extended with loading states or error handling
- Compatible with existing cart architecture

## Technical Implementation

### Files Modified
1. **CartScreenFigma.kt**: Added `onSyncTable` parameter and threading
2. **PayTab.kt**: Added sync button UI and parameter support

### Key Features
- **Icon Integration**: Uses Material Icons Sync symbol
- **Consistent Styling**: Matches cart button design patterns  
- **Proper Spacing**: Maintains visual hierarchy with payment buttons
- **Full Width**: Provides prominent call-to-action appearance

### Import Requirements
```kotlin
import androidx.compose.material.icons.filled.Sync
```

## Future Enhancements

### Potential Improvements
1. **Loading State**: Show spinner during sync operation
2. **Success/Error Feedback**: Visual indication of sync result
3. **Auto-Sync**: Automatic sync on cart open or periodic refresh
4. **Sync Status**: Display last sync time or status indicator

### Integration Considerations
- Parent component must implement sync logic
- Error handling should be managed at parent level
- Loading states can be passed down as parameters
- Success/failure feedback can use existing notification systems

The implementation provides a clean, user-friendly way to manually sync table data while maintaining the existing cart workflow and design consistency.
