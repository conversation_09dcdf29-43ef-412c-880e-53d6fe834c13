# Table-Specific Courses Loading Implementation

## Overview
This document describes the implementation of loading table-specific available courses when loading existing order data for occupied tables. The system now extracts course information from the existing order and creates table-specific course configurations.

## Implementation Details

### Enhanced loadExistingOrderToTable Method
The `loadExistingOrderToTable` method has been updated to:

1. **Extract Course Information**: Parse course names from existing order data
2. **Create Table-Specific Courses**: Generate `MealCourse` objects based on the order
3. **Map Cart Items to Courses**: Properly assign cart items to their respective courses
4. **Update State**: Store table-specific courses in the state

### Course Extraction Process

#### 1. Course Name Mapping
```kotlin
val courseNameToIdMap = mutableMapOf<String, String>()
orderData.orderCourses.forEach { course ->
    val courseId = "course_${course.coursesName.lowercase().replace(" ", "_")}"
    courseNameToIdMap[course.coursesName] = courseId
}
```

#### 2. Cart Item Assignment
```kotlin
orderData.orderCourses.forEach { course ->
    val courseId = courseNameToIdMap[course.coursesName] ?: ""
    
    course.getCart().forEach { cart ->
        carts.add(cart)
        
        cartItemsWithCourses.add(
            CartItemWithCourse(
                cart = cart,
                courseId = courseId
            )
        )
    }
}
```

#### 3. Course Creation
```kotlin
private fun extractCoursesFromOrderData(
    orderData: SyncOrderData
): List<MealCourse> {
    val extractedCourses = mutableListOf<MealCourse>()
    val courseNames = orderData.orderCourses.map { it.coursesName }.distinct()
    
    courseNames.forEachIndexed { index, courseName ->
        val courseId = "course_${courseName.lowercase().replace(" ", "_")}"
        
        val mealCourse = MealCourse(
            id = courseId,
            name = courseName,
            displayName = courseName
        )
        extractedCourses.add(mealCourse)
    }
    
    // Fallback to default courses if none found
    if (extractedCourses.isEmpty()) {
        extractedCourses.addAll(defaultCourses)
    }
    
    return extractedCourses
}
```

## Course ID Generation

### Naming Convention
- **Pattern**: `course_{courseName.lowercase().replace(" ", "_")}`
- **Examples**:
  - "Starters" → `course_starters`
  - "Main Course" → `course_main_course`
  - "Desserts & Coffee" → `course_desserts_&_coffee`

### Unique Course Identification
- Course names are extracted from `orderData.orderCourses`
- Duplicate course names are automatically deduplicated using `distinct()`
- Each unique course name gets a corresponding course ID

## State Management

### Table-Specific Course Storage
```kotlin
val updatedTableAvailableCourses = state.tableAvailableCourses.toMutableMap()
updatedTableAvailableCourses[selection.tableId] = tableSpecificCourses

return state.copy(
    tableOrders = updatedTableOrders,
    cartItemsWithCourses = updatedCartItemsWithCourses,
    tableAvailableCourses = updatedTableAvailableCourses
)
```

### Course-Cart Item Mapping
- Each cart item is properly assigned to its corresponding course
- Course assignments are stored in `cartItemsWithCourses` map
- Table-specific course assignments are maintained separately

## Benefits

### 1. Accurate Course Representation
- Courses match exactly what was originally ordered
- No mismatch between displayed courses and actual order structure
- Preserves original course organization

### 2. Proper Cart Organization
- Cart items are correctly grouped by their original courses
- Course filtering works accurately with loaded data
- Course-specific operations (Go, Complete, etc.) work properly

### 3. Consistent User Experience
- Users see the same course structure as when order was created
- Course names and organization are preserved
- No confusion from default course assignments

### 4. Flexible Course Support
- Supports any number of courses from the original order
- Handles custom course names automatically
- Falls back to default courses if order data is incomplete

## Example Scenarios

### Scenario 1: Standard Course Order
**Original Order Courses**: "Starters", "Mains", "Desserts"
**Generated Course IDs**: `course_starters`, `course_mains`, `course_desserts`
**Result**: Table shows three courses with items properly assigned

### Scenario 2: Custom Course Names
**Original Order Courses**: "Appetizers", "Main Course", "Sweet Treats"
**Generated Course IDs**: `course_appetizers`, `course_main_course`, `course_sweet_treats`
**Result**: Table shows custom course names with proper item assignments

### Scenario 3: Single Course Order
**Original Order Courses**: "All Items"
**Generated Course IDs**: `course_all_items`
**Result**: Table shows single course with all items assigned

## Error Handling

### Empty Course Data
- If no courses found in order data, falls back to default courses
- Ensures table always has at least basic course structure
- Prevents UI errors from missing course data

### Invalid Course Names
- Course ID generation handles special characters and spaces
- Consistent lowercase conversion for ID uniqueness
- Maintains original display names for user interface

## Integration Points

### Table Selection Flow
1. User selects occupied table
2. `getSyncedOrderForTable` API called
3. `loadExistingOrderToTable` processes response
4. Table-specific courses extracted and stored
5. Cart items assigned to proper courses
6. UI displays table with correct course structure

### Course Management
- Table-specific courses are used for all course operations
- Course filtering respects the loaded course structure
- Course status management works with extracted courses
- New items can be added to existing or new courses

The implementation ensures that when loading existing orders for occupied tables, the course structure exactly matches what was originally created, providing a seamless and accurate user experience.
