# New Course Auto-Selection Implementation

## Overview
This document describes the enhancement to automatically select newly added courses for new items in both table-specific and global cart scenarios. When a user adds a new course (via any method), it should be automatically selected as the course for new items.

## Key Features

### 1. Auto-Selection for All Course Addition Methods
- **`addNewCourse()`**: Auto-selects custom named courses
- **`addNumberedCourse()`**: Auto-selects numbered courses (Course 1, Course 2, etc.)
- **`addDefaultCourses()`**: Auto-selects first newly added default course
- **Scope**: Works for both table-specific and global carts

### 2. Table-Specific Auto-Selection
- Each table maintains independent course selection
- Adding course to Table 1 doesn't affect Table 2's selection
- Uses `tableSelectedCourseForNewItems` map for table-specific storage

### 3. Global Auto-Selection
- Works when no table is selected
- Uses `selectedCourseForNewItems` for global storage
- Maintains consistency with table-based behavior

## Implementation Details

### Modified Methods

#### 1. `addNewCourse()` - Enhanced
```kotlin
fun addNewCourse(courseName: String, availableCourses: List<MealCourse>) {
    setState {
        val currentTableId = getCurrentTableId()
        if (currentTableId != null) {
            // Table-specific: Auto-select new course
            val updatedTableSelectedCourses = tableSelectedCourseForNewItems.toMutableMap()
            updatedTableSelectedCourses[currentTableId] = courseName
            
            copy(
                tableAvailableCourses = updatedTableCourses,
                tableSelectedCourseForNewItems = updatedTableSelectedCourses
            )
        } else {
            // Global: Auto-select new course
            copy(
                availableCourses = updatedCourses,
                selectedCourseForNewItems = courseName
            )
        }
    }
}
```

#### 2. `addNumberedCourse()` - Enhanced
```kotlin
// Table-specific
val updatedTableSelectedCourses = tableSelectedCourseForNewItems.toMutableMap().apply {
    this[currentTableId] = courseName // Always auto-select new course
}

// Global
val newSelectedCourse = courseName // Always auto-select new course
```

#### 3. `addDefaultCourses()` - Enhanced
```kotlin
// Table-specific: Auto-select first newly added course
val updatedTableSelectedCourses = tableSelectedCourseForNewItems.toMutableMap().apply {
    this[currentTableId] = coursesToAdd.firstOrNull()?.name ?: updatedCourses.firstOrNull()?.name ?: ""
}

// Global: Auto-select first newly added course
val newSelectedCourse = coursesToAdd.firstOrNull()?.name ?: updatedCourses.firstOrNull()?.name ?: ""
```

## User Experience Flow

### Scenario 1: Adding Custom Course to Table
1. User selects Table 1
2. User adds custom course "Appetizers"
3. System automatically selects "Appetizers" for new items on Table 1
4. Next item added to Table 1 goes to "Appetizers" course
5. **Result**: Seamless course assignment without manual selection

### Scenario 2: Adding Numbered Course
1. User clicks "Course +" button
2. System creates "Course 2" (or next number)
3. System automatically selects "Course 2" for new items
4. User adds item - it goes to "Course 2" automatically
5. **Result**: Immediate usability of new course

### Scenario 3: Adding Default Courses
1. User clicks "Default Courses +" button
2. System adds "Starters", "Mains", "Desserts"
3. System automatically selects "Starters" (first new course)
4. Next item goes to "Starters" automatically
5. **Result**: Logical default selection

### Scenario 4: Multiple Tables Independence
1. Table 1 has "Course 1" selected, Table 2 has "Course A" selected
2. User adds "Custom Course" to Table 1
3. Table 1 auto-selects "Custom Course"
4. Table 2 keeps "Course A" selected (unchanged)
5. **Result**: Independent table course management

## Benefits

1. **Immediate Usability**: New courses are ready to use without manual selection
2. **Intuitive Behavior**: Users expect new courses to be selected automatically
3. **Consistent Experience**: Works the same across all course addition methods
4. **Table Independence**: Each table maintains its own course selection state
5. **Reduced User Friction**: Eliminates extra step of manually selecting new courses
6. **Logical Defaults**: Default courses auto-select the most logical option (first course)

## Technical Details

### State Management
- **Table-specific**: Uses `tableSelectedCourseForNewItems: Map<Int, String>`
- **Global**: Uses `selectedCourseForNewItems: String`
- **Course Names**: Uses course name as the identifier for consistency
- **Independence**: Each table maintains separate selection state

### Course Selection Logic
- **New Custom Course**: Always auto-selected
- **New Numbered Course**: Always auto-selected (changed from "only if first")
- **New Default Courses**: First newly added course is auto-selected
- **Existing Courses**: Selection behavior unchanged

### Backward Compatibility
- All existing functionality preserved
- No breaking changes to existing course selection logic
- Enhanced behavior is additive only

## Testing

Comprehensive tests in `NewCourseAutoSelectionTest.kt` covering:
- Auto-selection for table-specific courses (all methods)
- Auto-selection for global courses (all methods)
- Multiple tables with independent selections
- Default courses auto-selection behavior
- Edge cases and state validation

## Edge Cases Handled

1. **Empty Course Lists**: Auto-selection works when adding first course
2. **Multiple Tables**: Each table maintains independent selection
3. **Mixed Scenarios**: Global and table-specific courses work together
4. **Course Name Consistency**: Uses course names as identifiers throughout
5. **Fallback Logic**: Graceful handling when course lists are empty

## User Interface Impact

### Before Enhancement
- User adds new course → Course appears in list
- User must manually select new course for it to be used
- Extra step required for every new course

### After Enhancement
- User adds new course → Course appears in list AND is auto-selected
- New items immediately use the new course
- Seamless workflow with no extra steps

This enhancement significantly improves the user experience by eliminating the manual step of selecting newly added courses, making the course management workflow more intuitive and efficient.
