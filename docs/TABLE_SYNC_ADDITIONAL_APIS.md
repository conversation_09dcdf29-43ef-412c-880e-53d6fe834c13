# Additional Table Sync APIs Implementation

## Overview
This document describes the implementation of three additional Table Sync APIs that extend the existing table sync functionality. These APIs provide complete CRUD operations for table management.

## New APIs Implemented

### 1. Toggle Table Occupied Status (POST)
**Endpoint**: `POST /api/tables/toggleOccupied?tableId=47&netPayable=47.5`

**Implementation**: Uses Ktor HTTP client (apiClient) as requested

**Parameters**:
- `tableId` (int): The ID of the table to toggle
- `netPayable` (double): The net payable amount for the table

**Response**:
```json
{
    "id": 47,
    "storeId": 158,
    "areaId": 10,
    "tableName": "TAKE-OUT1 ",
    "seatingCapacity": 1,
    "tableDetailsJson": "{\"shape\":\"SQUARE\",\"color\":\"#2ECC71\",\"position\":{\"row\":1,\"col\":3}}",
    "occupied": false,
    "reserved": false,
    "netPayable": 0.0
}
```

### 2. Update Order for Table (PUT)
**Endpoint**: `PUT /api/tables/sync/order/47`

**Implementation**: Uses Ktor HTTP client (apiClient) as requested

**Request Body**:
```json
{
    "tableId": 12,
    "customerId": 345,
    "businessId": 104,
    "netPayable": 47.5,
    "orderCourses": [
        {
            "coursesName": "Starters",
            "cartJson": "{\"items\":[{\"id\":101,\"name\":\"Soup\",\"qty\":1,\"price\":5.5}]}"
        },
        {
            "coursesName": "Mains",
            "cartJson": "{\"items\":[{\"id\":202,\"name\":\"Burger\",\"qty\":2,\"price\":12.0}]}"
        }
    ]
}
```

**Response**:
```json
{
    "success": true,
    "message": "Order updated for table 47",
    "data": {
        "id": 7,
        "tableId": 47,
        "orderCourses": [
            {
                "id": 15,
                "coursesName": "Starters",
                "cartJson": "{\"items\":[{\"id\":101,\"name\":\"Soup\",\"qty\":1,\"price\":5.5}]}"
            },
            {
                "id": 16,
                "coursesName": "Mains",
                "cartJson": "{\"items\":[{\"id\":202,\"name\":\"Burger\",\"qty\":2,\"price\":12.0}]}"
            }
        ],
        "netPayable": 47.5,
        "customerId": 345,
        "businessId": 104
    },
    "statusCode": 200
}
```

### 3. Delete Order for Table (DELETE)
**Endpoint**: `DELETE /api/tables/sync/order/47`

**Implementation**: Uses Retrofit as requested

**Response**:
```json
{
    "success": false,
    "message": "Order couldn't be deleted for table 12",
    "data": null,
    "statusCode": 500
}
```

## Implementation Details

### New Data Models
1. **UpdateOrderRequest.kt**: Request model for PUT operations
2. **TableResponse.kt**: Response model for toggle occupied operations

### Extended Repository Methods
- `toggleTableOccupied()`: POST with query parameters
- `updateOrderForTable()`: PUT with request body
- `deleteOrderForTable()`: DELETE with Retrofit

### New Use Cases
- `ToggleTableOccupiedUseCase.kt`
- `UpdateOrderForTableUseCase.kt`
- `DeleteOrderForTableUseCase.kt`

### Extended ViewModel
The `TableSyncViewModel` now includes:
- `toggleTableOccupied()` method
- `updateOrderForTable()` method
- `deleteOrderForTable()` method
- Additional state properties for each operation
- Clear methods for each state

### Extended State Management
```kotlin
data class TableSyncState(
    val syncOrderResponse: Async<SyncOrderResponse> = Uninitialized,
    val getSyncedOrderResponse: Async<SyncOrderResponse> = Uninitialized,
    val toggleTableResponse: Async<TableResponse> = Uninitialized,
    val updateOrderResponse: Async<SyncOrderResponse> = Uninitialized,
    val deleteOrderResponse: Async<SyncOrderResponse> = Uninitialized
) : MavericksState
```

## Usage Examples

### Toggle Table Occupied
```kotlin
val result = viewModel.toggleTableOccupied(tableId = 47, netPayable = 47.5)
result.collect { asyncResult ->
    when (asyncResult) {
        is Success -> {
            val response = asyncResult()
            println("Table ${response.id} occupied: ${response.occupied}")
        }
        else -> { /* Handle error */ }
    }
}
```

### Update Order
```kotlin
val updateRequest = UpdateOrderRequest(
    tableId = 12,
    customerId = 345,
    businessId = 104,
    netPayable = 47.5,
    orderCourses = listOf(/* courses */)
)
val result = viewModel.updateOrderForTable(tableId = 47, updateRequest)
```

### Delete Order
```kotlin
val result = viewModel.deleteOrderForTable(tableId = 47)
result.collect { asyncResult ->
    when (asyncResult) {
        is Success -> {
            val response = asyncResult()
            if (response.success) {
                println("Order deleted successfully")
            } else {
                println("Delete failed: ${response.message}")
            }
        }
        else -> { /* Handle error */ }
    }
}
```

## Complete API Coverage

The implementation now provides complete CRUD operations:
- **Create**: `syncOrderToTable()` (POST)
- **Read**: `getSyncedOrderForTable()` (GET)
- **Update**: `updateOrderForTable()` (PUT)
- **Delete**: `deleteOrderForTable()` (DELETE)
- **Toggle**: `toggleTableOccupied()` (POST)

All APIs follow the existing codebase patterns and use the requested HTTP client implementations (Ktor for POST/PUT, Retrofit for GET/DELETE).

## Testing
Comprehensive unit tests have been added to verify:
- Data model serialization/deserialization
- Request/response structure validation
- Error handling scenarios
- Edge cases with empty data

The implementation is production-ready and fully integrated with the existing DasaPOS architecture.
