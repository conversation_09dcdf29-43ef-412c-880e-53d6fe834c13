# AssignTableDialog Implementation

## Overview
This document describes the implementation of the AssignTableDialog that appears when there are items in the cart with no table assigned and a user selects a table. The dialog provides three options based on the table's occupied status.

## Dialog Trigger Condition
The dialog is shown when:
- There are items in the cart (`viewModel.hasGlobalCartItems(state = state)` returns true)
- No table is currently selected (`state.selectedTables.isEmpty()`)
- User selects a table from the AreaTableSelectionScreen

## Dialog Options

### 1. Assign Table
- **Visibility**: Only shown when the selected table is NOT occupied (`!isTableOccupied`)
- **Action**: Assigns the current cart items to the selected table and syncs the table
- **Implementation**:
  - Calls `viewModel.assignGlobalCartToTable(selection)`
  - Calls `viewModel.handleUnoccupiedTableSelection(selection, table, state)` to sync the newly selected table
- **Button Style**: Green button with white text
- **Text**: "Assign Table"

### 2. Clear Cart and Continue
- **Visibility**: Always shown
- **Action**: Clears the current cart and proceeds with table selection based on occupied status
- **Implementation**: 
  - Calls `viewModel.clearGlobalCart()`
  - Calls `viewModel.handleTableSelectionWithOccupiedStatus(selection, table, state)`
- **Button Style**: Orange button with white text
- **Text**: "Clear Cart and Continue"

### 3. Cancel
- **Visibility**: Always shown
- **Action**: Dismisses the dialog without any changes
- **Implementation**: Calls `onDismiss()`
- **Button Style**: Outlined button with gray text
- **Text**: "Cancel"

## Implementation Details

### Dialog Structure
```kotlin
@Composable
fun AssignTableDialog(
    isTableOccupied: Boolean,
    onDismiss: () -> Unit,
    onAssignTableClick: () -> Unit,
    onClearCartClick: () -> Unit
)
```

### Table Occupation Detection
In the main ProductsScreen, the dialog determines if the selected table is occupied by:
```kotlin
val selectedTable = reservationsViewModel.collectAsState().value.areas
    .flatMap { it.tables }
    .find { it.id == pendingTableSelection!!.tableId }

AssignTableDialog(
    isTableOccupied = selectedTable?.occupied ?: false,
    // ... other parameters
)
```

### State Management
- `showCartClearDialog`: Boolean state to control dialog visibility
- `pendingTableSelection`: Stores the selected table information while dialog is shown

## User Experience Flow

### Scenario 1: Unoccupied Table Selected
1. User has items in cart with no table assigned
2. User selects an unoccupied table
3. Dialog shows with 3 options:
   - **Assign Table** (available)
   - **Clear Cart and Continue**
   - **Cancel**

### Scenario 2: Occupied Table Selected
1. User has items in cart with no table assigned
2. User selects an occupied table
3. Dialog shows with 2 options:
   - **Clear Cart and Continue**
   - **Cancel**
   - **Assign Table** is hidden since table is occupied

## Integration Points

### ProductsScreen (Tablet)
- Full integration with reservations view model
- Proper table occupation status detection
- Complete table selection flow

### CategoriesScreen (Mobile)
- Basic integration without table occupation detection
- `isTableOccupied` is set to `false` by default
- All three options are always available

### Table Selection Logic
The dialog is triggered in the `onAreaTableSelected` callback:
```kotlin
if (state.selectedTables.isEmpty() && viewModel.hasGlobalCartItems(state = state)) {
    // Show AssignTableDialog with options
    pendingTableSelection = selection
    showCartClearDialog = true
} else {
    // Handle table selection based on occupied status
    viewModel.handleTableSelectionWithOccupiedStatus(selection, table, state = state)
    orderScreenViewModel.updateCurrentRoute("0")
}
```

## Benefits

1. **User Choice**: Provides clear options for handling existing cart items
2. **Context Awareness**: Shows different options based on table occupation status
3. **Prevents Data Loss**: Users can choose to keep their cart items by assigning to table
4. **Clear Actions**: Each option has a clear, descriptive label
5. **Consistent UX**: Same dialog behavior across tablet and mobile versions

## Technical Implementation

### Files Modified
1. **ProductsScreen.kt**: Updated table selection logic and dialog integration with table sync
2. **CategoriesScreen.kt**: Updated mobile version with new dialog parameters and table sync
3. **ProductsScreenViewModel.kt**: Made `handleUnoccupiedTableSelection` method public
4. **AssignTableDialog**: Enhanced with conditional option display

### Key Features
- **Conditional UI**: Assign Table option only shows for unoccupied tables
- **Smart Actions**: Clear Cart and Continue calls the appropriate table selection handler
- **Table Sync Integration**: Assign Table action now properly syncs the newly selected table
- **State Management**: Proper cleanup of dialog state on all actions
- **Error Handling**: Graceful fallback if table occupation status cannot be determined
- **Cross-Platform Sync**: Both tablet and mobile versions call table sync on assign

The implementation provides a user-friendly way to handle the common scenario where users have items in their cart and want to select a table, giving them control over whether to keep or clear their existing cart items.
