# Courses Notification Implementation

## Overview
This document outlines the implementation of the courses notification API functionality in the POS system. The implementation includes API integration, UI changes, and dependency injection setup.

## Changes Made

### 1. API Integration

#### API Endpoint Configuration
- **File**: `app/src/main/java/com/thedasagroup/suminative/data/api/ApiClient.kt`
- **Added**: `COURSES_NOTIFICATION` constant pointing to `/api/printer/coursesNotification`

#### Data Models
- **Request Model**: `app/src/main/java/com/thedasagroup/suminative/data/model/request/courses_notification/CoursesNotificationRequest.kt`
  - Fields: `storeId`, `courseName`, `tableName`, `cartJson`
- **Response Models**: `app/src/main/java/com/thedasagroup/suminative/data/model/response/courses_notification/CoursesNotificationResponse.kt`
  - Success response with `success`, `message`, `data` fields
  - Error response matching the 500 error format from the API

#### Repository Implementation
- **File**: `app/src/main/java/com/thedasagroup/suminative/data/repo/PrintRepository.kt`
- **Features**:
  - Three overloaded methods for flexibility
  - Automatic JSON conversion for Cart lists
  - Proper error handling for 500 responses
  - Follows existing repository patterns

### 2. Use Case Layer
- **File**: `app/src/main/java/com/thedasagroup/suminative/domain/courses_notification/SendCoursesNotificationUseCase.kt`
- **Purpose**: Encapsulates business logic for sending course notifications
- **Features**: Three overloaded invoke methods matching repository methods

### 3. UI Changes

#### Cart Tab Modifications
- **File**: `app/src/main/java/com/thedasagroup/suminative/ui/products/cart/CartScreenFigma.kt`
- **Changes**:
  - Removed `BILL` tab from `CartTab` enum
  - Updated tab content handling to only show `ORDER` and `PAY` tabs
  - Added Bill button to `OrderSummarySection`

#### Bill Button Implementation
- **Location**: Added to the Order tab's summary section
- **Styling**: Green button matching POS theme (`#2E7D32`)
- **Icon**: Money icon from Material Icons
- **Action**: Calls `productsScreenViewModel.sendCoursesNotification(order)`

### 4. ViewModel Integration

#### ProductsScreenViewModel Updates
- **File**: `app/src/main/java/com/thedasagroup/suminative/ui/products/ProductsScreenViewModel.kt`
- **Added Dependencies**: `SendCoursesNotificationUseCase`
- **New Method**: `sendCoursesNotification(order: Order)`

#### sendCoursesNotification Function Features
- Gets store ID from preferences
- Determines table name from current table selection or defaults to "Walk-in"
- Groups cart items by course
- Sends separate notifications for each course with items
- Handles success/error responses
- Runs asynchronously using coroutines

### 5. Dependency Injection
- **File**: `app/src/main/java/com/thedasagroup/suminative/di/RepoModule.kt`
- **Added**: `PrintRepository` provider method
- **Integration**: Properly integrated with Dagger Hilt

## Usage Flow

1. User adds items to cart and assigns them to courses
2. User clicks the "BILL" button in the Order tab
3. System groups cart items by course
4. For each course with items:
   - Creates course notification request
   - Sends API call to `/api/printer/coursesNotification`
   - Handles response (success/error)
5. Notifications are sent to printer system for kitchen preparation

## API Request Example

```json
{
  "storeId": 158,
  "courseName": "Mains",
  "tableName": "T-12",
  "cartJson": "[{\"storeItem\":{\"id\":201,\"name\":\"Steak\"},\"quantity\":1,\"price\":19.99}]"
}
```

## Error Handling

The implementation handles the specific 500 error format mentioned in the requirements:
```json
{
  "timestamp": "2025-08-10T14:03:31.687+0000",
  "status": 500,
  "error": "Internal Server Error",
  "message": "Format specifier '%7s'",
  "path": "/BackendDASA-1.0.0/api/printer/coursesNotification"
}
```

## Testing

A usage example file has been created at:
`app/src/main/java/com/thedasagroup/suminative/data/repo/PrintRepositoryUsageExample.kt`

This file demonstrates all three ways to use the API:
1. With request object
2. With individual parameters
3. With Cart list (auto-converted to JSON)

## Next Steps

1. Test the implementation with actual API calls
2. Add proper error handling UI (toasts/dialogs)
3. Consider adding loading states for better UX
4. Add unit tests for the new functionality
