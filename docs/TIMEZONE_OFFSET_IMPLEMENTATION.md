# Timezone Offset Implementation in CreateReservationUseCase

## Overview
Successfully enhanced the CreateReservationUseCase to automatically handle timezone offset calculation, providing both automatic device timezone detection and manual timezone override capabilities.

## Features Implemented

### 🌍 **Automatic Timezone Detection**
- **Device timezone calculation** using `TimeZone.getDefault()`
- **Automatic offset conversion** from milliseconds to minutes
- **UTC offset calculation** (e.g., +5h = 300 minutes, -3h = -180 minutes)
- **Fallback mechanism** when timezone offset is not provided

### 🔧 **Flexible API Design**
- **Optional timezone parameter** with automatic fallback
- **Backward compatibility** with existing code
- **Two invoke methods** for different use cases
- **Clean separation of concerns**

### 📱 **Enhanced Use Case Methods**

#### Method 1: Request-based with Optional Timezone
```kotlin
suspend operator fun invoke(
    request: CreateReservationRequest,
    timezoneOffset: Int? = null
): StateFlow<Async<CreateReservationResponse>>
```

#### Method 2: Parameter-based with Optional Timezone
```kotlin
suspend operator fun invoke(
    id: Int? = null,
    storeId: Int,
    tableId: Int,
    customerId: Int,
    guestName: String,
    guestPhone: String,
    numPeople: Int,
    reservationStatus: Int,
    reservationTime: String,
    timezoneOffset: Int? = null
): StateFlow<Async<CreateReservationResponse>>
```

## Implementation Details

### Timezone Calculation Logic
```kotlin
/**
 * Gets the device's current timezone offset in minutes east of UTC
 * @return Timezone offset in minutes (e.g., +5h = 300, -3h = -180)
 */
private fun getDeviceTimezoneOffset(): Int {
    val timeZone = TimeZone.getDefault()
    return timeZone.rawOffset / (1000 * 60) // Convert milliseconds to minutes
}
```

### Request Processing
```kotlin
suspend operator fun invoke(
    request: CreateReservationRequest,
    timezoneOffset: Int? = null
): StateFlow<Async<CreateReservationResponse>> {
    val finalTimezoneOffset = timezoneOffset ?: getDeviceTimezoneOffset()
    
    val requestWithTimezone = request.copy(
        timezoneOffset = finalTimezoneOffset
    )
    
    return reservationsRepository.createReservation(requestWithTimezone)
}
```

## Usage Examples

### Automatic Timezone Detection
```kotlin
// Uses device timezone automatically
val request = CreateReservationRequest(
    id = 30,
    storeId = 158,
    tableId = 12,
    customerId = 321,
    guestName = "Jane Doe",
    guestPhone = "03001234567",
    numPeople = 4,
    reservationStatus = 0,
    reservationTime = "2025-07-22T18:30",
    timezoneOffset = 0 // Will be overridden by use case
)

createReservationUseCase(request) // Automatically calculates timezone
```

### Manual Timezone Override
```kotlin
// Specify custom timezone offset
createReservationUseCase(request, timezoneOffset = 300) // +5 hours UTC
```

### Parameter-based Usage
```kotlin
// Build request directly in use case
createReservationUseCase(
    id = null,
    storeId = 158,
    tableId = 12,
    customerId = 321,
    guestName = "Jane Doe",
    guestPhone = "03001234567",
    numPeople = 4,
    reservationStatus = 0,
    reservationTime = "2025-07-22T18:30",
    timezoneOffset = null // Uses device timezone
)
```

## Integration Updates

### ReservationsScreen.kt Changes
- **Removed manual timezone calculation** from the save button
- **Simplified request creation** by letting use case handle timezone
- **Cleaner code** with automatic timezone handling

**Before:**
```kotlin
timezoneOffset = TimeZone.getDefault().rawOffset / (1000 * 60)
```

**After:**
```kotlin
timezoneOffset = 0 // Will be automatically calculated by the use case
```

### Backward Compatibility
- **Existing calls remain unchanged** - all current usage continues to work
- **Optional parameter design** ensures no breaking changes
- **Automatic fallback** provides seamless transition

## Technical Benefits

### 🔧 **Code Quality**
- **Single responsibility** - timezone logic centralized in use case
- **DRY principle** - no duplicate timezone calculation code
- **Clean architecture** - business logic in appropriate layer
- **Testable design** - timezone logic can be easily unit tested

### 🌍 **Timezone Handling**
- **Accurate timezone detection** using Android's native APIs
- **Proper UTC offset calculation** in minutes as required by API
- **Handles daylight saving time** through system timezone
- **Cross-timezone compatibility** for international usage

### 📱 **User Experience**
- **Automatic timezone detection** - no user configuration needed
- **Consistent behavior** across different devices and locations
- **Reliable time handling** for reservation scheduling
- **Professional time management** suitable for business use

## API Compatibility

### Request Structure Maintained
The `CreateReservationRequest` structure remains unchanged:
```kotlin
@Serializable
data class CreateReservationRequest(
    @SerialName("id") val id: Int? = null,
    @SerialName("storeId") val storeId: Int,
    @SerialName("tableId") val tableId: Int,
    @SerialName("customerId") val customerId: Int,
    @SerialName("guestName") val guestName: String,
    @SerialName("guestPhone") val guestPhone: String,
    @SerialName("numPeople") val numPeople: Int,
    @SerialName("reservationStatus") val reservationStatus: Int,
    @SerialName("reservationTime") val reservationTime: String,
    @SerialName("timezoneOffset") val timezoneOffset: Int
)
```

### Server Integration
- **Proper timezone offset format** (minutes east of UTC)
- **API-compatible values** (e.g., +5h = 300, -3h = -180)
- **Consistent time handling** across client and server
- **International timezone support**

## Testing Considerations

### Unit Testing
- **Mock timezone scenarios** for different regions
- **Test automatic detection** vs manual override
- **Verify offset calculations** for various timezones
- **Test edge cases** like daylight saving transitions

### Integration Testing
- **End-to-end timezone handling** from UI to API
- **Cross-timezone reservation creation** testing
- **Time accuracy verification** in different regions
- **API compatibility validation**

The implementation provides a robust, professional timezone handling solution that enhances the reservation system's reliability and user experience while maintaining full backward compatibility.
