# Auto Course Selection Implementation

## Overview
This document describes the implementation of auto course selection functionality in the POS system. When no courses exist and an item is added to the cart, the system will automatically create "Course 1" and select it for new items. This functionality works seamlessly for both global carts and table-specific carts.

## Key Features

### 1. Auto Course Creation
- **Trigger**: When adding an item to cart and no courses exist
- **Action**: Automatically creates "Course 1"
- **Selection**: Auto-selects the new course for future items
- **Scope**: Works for both table-specific and global carts

### 2. Table-Specific Auto Course Selection
- **Table Initialization**: New tables start with empty course lists
- **Auto-Creation**: First item added to a table triggers course creation
- **Independence**: Each table maintains its own course state
- **Consistency**: Uses same "Course 1" naming as global carts

### 2. Auto Selection Logic
- If no courses exist → Create "Course 1" and auto-select it
- If courses exist but none selected → Auto-select the first available course
- If a course is already selected → Use the selected course

## Implementation Details

### Modified Files
- `ProductsScreenViewModel.kt` - Added auto course creation logic

### Key Methods

#### `addItemToCart()` - Enhanced
```kotlin
// Auto-create and select course if no courses exist
var updatedState = state
val availableCourses = state.getCurrentTableAvailableCourses()
if (availableCourses.isEmpty()) {
    // Create first course and auto-select it
    updatedState = createAndSelectFirstCourse(state)
}

// Get the current selected course for new items (table-specific or global)
val currentCourseId = updatedState.getCurrentTableSelectedCourseForNewItems().ifEmpty {
    updatedState.getCurrentTableAvailableCourses().firstOrNull()?.name ?: ""
}
```

#### `createAndSelectFirstCourse()` - New Helper Method
```kotlin
private fun createAndSelectFirstCourse(state: ProductsScreenState): ProductsScreenState {
    val currentTableId = getCurrentTableId(
        selectedTables = state.selectedTables,
        selectedTableIndex = state.selectedTableIndex
    )

    return if (currentTableId != null) {
        // Handle table-specific courses
        val courseName = "Course 1"
        val newCourseId = "course_1"
        val newCourse = MealCourse(name = courseName)
        
        // Update table-specific state
        state.copy(
            tableAvailableCourses = state.tableAvailableCourses.toMutableMap().apply {
                this[currentTableId] = listOf(newCourse)
            },
            tableSelectedCourseForNewItems = state.tableSelectedCourseForNewItems.toMutableMap().apply {
                this[currentTableId] = newCourseId
            },
            tableActiveCourses = state.tableActiveCourses.toMutableMap().apply {
                this[currentTableId] = newCourseId
            }
        )
    } else {
        // Handle global courses
        state.copy(
            availableCourses = listOf(MealCourse(name = "Course 1")),
            selectedCourseForNewItems = "course_1",
            currentActiveCourse = "course_1"
        )
    }
}
```

#### `addSelectedTable()` - Enhanced for Table Course Initialization
```kotlin
// Initialize table courses if not already present (but don't auto-create courses yet)
// Auto-course creation will happen when first item is added
val updatedTableCourses = state.tableAvailableCourses.toMutableMap()
val updatedTableSelectedCourses = state.tableSelectedCourseForNewItems.toMutableMap()
val updatedTableActiveCourses = state.tableActiveCourses.toMutableMap()

if (!updatedTableCourses.containsKey(selection.tableId)) {
    updatedTableCourses[selection.tableId] = emptyList() // Start with no courses
    updatedTableSelectedCourses[selection.tableId] = ""
    updatedTableActiveCourses[selection.tableId] = ""
}

setState {
    copy(
        // ... other state updates ...
        tableAvailableCourses = updatedTableCourses,
        tableSelectedCourseForNewItems = updatedTableSelectedCourses,
        tableActiveCourses = updatedTableActiveCourses
    )
}
```

## User Experience Flow

### Scenario 1: No Courses Exist (Auto-Selection)
1. User adds item to cart
2. System detects no courses exist
3. System automatically creates "Course 1"
4. System auto-selects "Course 1" for new items
5. Item is added under "Course 1"
6. Cart opens showing the item under "Course 1"
7. **Result**: User sees their item organized under a course without any manual selection

### Scenario 2: Courses Exist
1. User adds item to cart
2. System uses existing selected course or first available course
3. Item is added under the selected/first course
4. Cart opens showing the item under the appropriate course

### Scenario 3: Adding More Items After Auto-Selection
1. First item triggers auto-course creation and selection
2. Subsequent items automatically go to the selected "Course 1"
3. User can manually select different courses if needed
4. **Result**: Consistent behavior - all items go to selected course

### Scenario 4: Table-Specific Auto-Selection
1. User adds a new table (no courses exist for this table)
2. User adds first item to the table
3. System detects no courses for this table
4. System automatically creates "Course 1" for this table
5. System auto-selects "Course 1" for new items on this table
6. Item is added under "Course 1" for this specific table
7. **Result**: Each table maintains independent course state

### Scenario 5: Multiple Tables with Different Course States
1. Table 1 has existing courses, Table 2 is new with no courses
2. User switches to Table 2 and adds an item
3. System creates "Course 1" for Table 2 (independent of Table 1)
4. User switches back to Table 1 - original courses remain unchanged
5. **Result**: Tables maintain separate, independent course configurations

## Benefits

1. **Seamless UX**: No manual course selection required when starting fresh
2. **Consistent Behavior**: Works the same for both table-specific and global carts
3. **Backward Compatible**: Existing functionality remains unchanged
4. **Intuitive**: Items automatically appear under a course without user intervention
5. **Eliminates Empty State**: Cart always shows organized content with courses
6. **Reduces User Friction**: One less step for users to manage their orders
7. **Table Independence**: Each table maintains its own course state automatically
8. **Scalable**: Works seamlessly with multiple tables and complex scenarios
9. **Zero Configuration**: No setup required - works out of the box for all scenarios

## Technical Implementation Details

### State Management
- Uses existing `ProductsScreenState` structure
- Leverages current course management system
- Maintains separation between table-specific and global courses
- Preserves all existing course functionality (Go buttons, status tracking, etc.)

### Course ID Consistency
- Uses course name as the course ID for consistency with existing `initializeTableCourses`
- "Course 1" is both the display name and internal identifier
- Maintains compatibility with existing course selection logic

## Bug Fix: State Preservation in addItemToCart

### Issue Identified
The `addItemToCart` method was not properly preserving the course state changes from `createAndSelectFirstCourse`. The newly created courses were being lost during state updates.

### Root Cause
```kotlin
// BEFORE (Incorrect)
setState {
    copy( // Using original state, losing course changes
        tableOrders = updatedTableOrders,
        showCart = updatedOrder.carts?.isNotEmpty() ?: false
    )
}

// AFTER (Fixed)
setState {
    updatedState.copy( // Using updatedState to preserve course changes
        tableOrders = updatedTableOrders,
        showCart = updatedOrder.carts?.isNotEmpty() ?: false
    )
}
```

### Fix Applied
- **Table-specific cart**: Use `updatedState.copy()` instead of `copy()` in setState blocks
- **Global cart**: Use `updatedState.copy()` instead of `copy()` in setState blocks
- **Preservation**: Ensures course changes from `createAndSelectFirstCourse` are maintained

## Testing

The implementation includes comprehensive tests covering both global and table-specific scenarios:

### `AutoCourseSelectionTest.kt`
- Auto course creation for global cart
- Auto course creation for table-specific cart
- No course creation when courses already exist
- Edge cases and state validation

### `TableAutoCourseSelectionTest.kt`
- Table-specific auto course creation
- Multiple tables with different course states
- Course state independence between tables
- Complete auto-selection flow for tables
- Table initialization and course setup
- Fallback to global courses when no table selected

### `AddItemAutoCourseCreationTest.kt`
- Auto course creation when adding first item to table
- Auto course creation when adding first item to global cart
- No course creation when courses already exist
- Course assignment to items after auto-creation
- Multiple tables with independent course auto-creation

## Future Enhancements

1. **Configurable Course Names**: Allow customization of default course name
2. **Smart Course Detection**: Auto-detect course type based on item category
3. **Course Templates**: Pre-defined course structures for different meal types
