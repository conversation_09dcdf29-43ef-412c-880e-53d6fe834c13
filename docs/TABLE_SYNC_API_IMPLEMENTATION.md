# Table Sync API Implementation

## Overview
This document describes the implementation of Table Sync APIs for the DasaPOS system. The implementation includes five operations: sync order to table (POST), get synced order (GET), toggle table occupied (POST), update order (PUT), and delete order (DELETE) following the existing codebase patterns.

## API Endpoints Implemented

### 1. Sync Order to Table (POST)
- **Method**: POST
- **Endpoint**: `https://dasadirect.com/BackendDASA-1.0.0/api/tables/sync/order`
- **Implementation**: Uses Ktor HTTP client (apiClient)
- **Request Body**:
```json
{
    "tableId": 47,
    "customerId": 345,
    "businessId": 104,
    "netPayable": 47.5,
    "orderCourses": [
        {
            "coursesName": "Starters",
            "cartJson": "{\"items\":[{\"id\":101,\"name\":\"Soup\",\"qty\":1,\"price\":5.5}]}"
        },
        {
            "coursesName": "Mains",
            "cartJson": "{\"items\":[{\"id\":202,\"name\":\"Burger\",\"qty\":2,\"price\":12.0}]}"
        }
    ]
}
```

### 2. Get Synced Order for Table (GET)
- **Method**: GET
- **Endpoint**: `https://dasadirect.com/BackendDASA-1.0.0/api/tables/sync/order/{tableId}`
- **Implementation**: Uses Retrofit
- **Example**: `https://dasadirect.com/BackendDASA-1.0.0/api/tables/sync/order/47`

### 3. Toggle Table Occupied (POST)
- **Method**: POST
- **Endpoint**: `https://dasadirect.com/BackendDASA-1.0.0/api/tables/toggleOccupied?tableId=47&netPayable=47.5`
- **Implementation**: Uses Ktor HTTP client (apiClient)
- **Parameters**: `tableId` (int), `netPayable` (double)

### 4. Update Order for Table (PUT)
- **Method**: PUT
- **Endpoint**: `https://dasadirect.com/BackendDASA-1.0.0/api/tables/sync/order/{tableId}`
- **Implementation**: Uses Ktor HTTP client (apiClient)
- **Request Body**: Same structure as sync order request
```json
{
    "tableId": 12,
    "customerId": 345,
    "businessId": 104,
    "netPayable": 47.5,
    "orderCourses": [
        {
            "coursesName": "Starters",
            "cartJson": "{\"items\":[{\"id\":101,\"name\":\"Soup\",\"qty\":1,\"price\":5.5}]}"
        }
    ]
}
```

### 5. Delete Order for Table (DELETE)
- **Method**: DELETE
- **Endpoint**: `https://dasadirect.com/BackendDASA-1.0.0/api/tables/sync/order/{tableId}`
- **Implementation**: Uses Retrofit
- **Example**: `https://dasadirect.com/BackendDASA-1.0.0/api/tables/sync/order/47`

### Response Structures

#### Sync/Get/Update/Delete Order Response
```json
{
    "success": true,
    "message": "Order saved for table 47",
    "data": {
        "id": 7,
        "tableId": 47,
        "orderCourses": [
            {
                "id": 13,
                "coursesName": "Starters",
                "cartJson": "{\"items\":[{\"id\":101,\"name\":\"Soup\",\"qty\":1,\"price\":5.5}]}"
            },
            {
                "id": 14,
                "coursesName": "Mains",
                "cartJson": "{\"items\":[{\"id\":202,\"name\":\"Burger\",\"qty\":2,\"price\":12.0}]}"
            }
        ],
        "netPayable": 47.5,
        "customerId": 345,
        "businessId": 104
    },
    "statusCode": 200
}
```

#### Toggle Table Occupied Response
```json
{
    "id": 47,
    "storeId": 158,
    "areaId": 10,
    "tableName": "TAKE-OUT1 ",
    "seatingCapacity": 1,
    "tableDetailsJson": "{\"shape\":\"SQUARE\",\"color\":\"#2ECC71\",\"position\":{\"row\":1,\"col\":3}}",
    "occupied": false,
    "reserved": false,
    "netPayable": 0.0
}
```

#### Delete Order Error Response
```json
{
    "success": false,
    "message": "Order couldn't be deleted for table 12",
    "data": null,
    "statusCode": 500
}
```

## Implementation Architecture

### 1. Data Models
- **Request Models**:
  - `SyncOrderRequest.kt` - Contains table sync request structure
  - `UpdateOrderRequest.kt` - Contains update order request structure
  - Both include `OrderCourse` data class for course information

- **Response Models**:
  - `SyncOrderResponse.kt` - Contains response structure for sync/get/update/delete operations
  - `TableResponse.kt` - Contains response structure for toggle occupied operation
  - Includes `SyncOrderData` and `SyncOrderCourse` data classes

### 2. Repository Layer
- **SyncRepository.kt**: Implements all five API calls
  - `syncOrderToTable()`: POST request using Ktor HTTP client
  - `getSyncedOrderForTable()`: GET request using Retrofit
  - `toggleTableOccupied()`: POST request using Ktor HTTP client
  - `updateOrderForTable()`: PUT request using Ktor HTTP client
  - `deleteOrderForTable()`: DELETE request using Retrofit
  - Follows existing repository patterns with `BaseRepository`
  - Uses `safeApiCall()` for error handling

### 3. Use Cases
- **SyncOrderToTableUseCase.kt**: Handles sync order operation
- **GetSyncedOrderForTableUseCase.kt**: Handles get synced order operation
- **ToggleTableOccupiedUseCase.kt**: Handles toggle table occupied operation
- **UpdateOrderForTableUseCase.kt**: Handles update order operation
- **DeleteOrderForTableUseCase.kt**: Handles delete order operation
- All use cases follow the existing pattern with dependency injection

### 4. ViewModel
- **TableSyncViewModel.kt**: Mavericks-based state management
  - Manages all five operations (sync, get, toggle, update, delete)
  - Provides state management with `TableSyncState`
  - Includes methods for clearing state for each operation
  - Separate state properties for each operation type

### 5. Dependency Injection
- Added `SyncRepository` to `RepoModule.kt`
- Added use cases to `AppUseCaseModule.kt`
- Follows existing DI patterns

## Usage Examples

### Using ViewModel (Recommended)
```kotlin
// Create sync request
val syncRequest = SyncOrderRequest(
    tableId = 47,
    customerId = 345,
    businessId = 104,
    netPayable = 47.5,
    orderCourses = listOf(
        OrderCourse(
            coursesName = "Starters",
            cartJson = "{\"items\":[{\"id\":101,\"name\":\"Soup\",\"qty\":1,\"price\":5.5}]}"
        )
    )
)

// Sync order
val result = viewModel.syncOrderToTable(syncRequest)
result.collect { asyncResult ->
    when (asyncResult) {
        is Success -> {
            // Handle success
            val response = asyncResult()
            println("Order synced: ${response.message}")
        }
        else -> {
            // Handle error or loading
        }
    }
}

// Get synced order
val getResult = viewModel.getSyncedOrderForTable(tableId = 47)

// Toggle table occupied
val toggleResult = viewModel.toggleTableOccupied(tableId = 47, netPayable = 47.5)

// Update order
val updateRequest = UpdateOrderRequest(
    tableId = 12,
    customerId = 345,
    businessId = 104,
    netPayable = 47.5,
    orderCourses = listOf(/* order courses */)
)
val updateResult = viewModel.updateOrderForTable(tableId = 47, updateRequest)

// Delete order
val deleteResult = viewModel.deleteOrderForTable(tableId = 47)
```

### Using Use Cases Directly
```kotlin
// Inject use cases
@Inject lateinit var syncOrderToTableUseCase: SyncOrderToTableUseCase
@Inject lateinit var getSyncedOrderForTableUseCase: GetSyncedOrderForTableUseCase

// Use directly
val result = syncOrderToTableUseCase(syncRequest)
val getResult = getSyncedOrderForTableUseCase(47)
```

## Files Created/Modified

### New Files
1. `data/model/request/table_sync/SyncOrderRequest.kt`
2. `data/model/request/table_sync/UpdateOrderRequest.kt`
3. `data/model/response/table_sync/SyncOrderResponse.kt`
4. `data/model/response/table_sync/TableResponse.kt`
5. `data/repo/SyncRepository.kt`
6. `domain/table_sync/SyncOrderToTableUseCase.kt`
7. `domain/table_sync/GetSyncedOrderForTableUseCase.kt`
8. `domain/table_sync/ToggleTableOccupiedUseCase.kt`
9. `domain/table_sync/UpdateOrderForTableUseCase.kt`
10. `domain/table_sync/DeleteOrderForTableUseCase.kt`
11. `ui/table_sync/TableSyncViewModel.kt`
12. `ui/table_sync/TableSyncUsageExample.kt`

### Modified Files
1. `data/api/ApiClient.kt` - Added table sync endpoint constant
2. `di/RepoModule.kt` - Added SyncRepository provider
3. `di/AppUseCaseModule.kt` - Added use case providers

## Key Features

1. **Mixed Implementation**: POST/PUT requests use Ktor HTTP client, GET/DELETE requests use Retrofit (as requested)
2. **Complete CRUD Operations**: Create (POST), Read (GET), Update (PUT), Delete (DELETE), plus Toggle functionality
3. **Mavericks State Management**: Full integration with existing state management pattern
4. **Error Handling**: Uses existing `BaseRepository.safeApiCall()` pattern
5. **Dependency Injection**: Fully integrated with Hilt DI
6. **Type Safety**: All models use Kotlinx Serialization with proper annotations
7. **Consistent Patterns**: Follows existing codebase architecture and naming conventions
8. **Comprehensive Testing**: Unit tests for all data models and serialization

## Testing Recommendations

1. **Unit Tests**: Test use cases and repository methods
2. **Integration Tests**: Test API calls with mock servers
3. **UI Tests**: Test ViewModel state management
4. **Example Usage**: Use `TableSyncUsageExample.kt` for testing different scenarios

## Notes

- The implementation follows the existing codebase patterns exactly
- Error handling is consistent with other repositories
- State management uses Mavericks as requested
- Both APIs return the same response structure for consistency
- The cart JSON is stored as a string to match the API specification
